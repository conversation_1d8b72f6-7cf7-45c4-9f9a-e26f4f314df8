<?php
namespace <PERSON><PERSON><PERSON><PERSON>\App\Listener;

use DB;
use <PERSON><PERSON><PERSON><PERSON>\App\Models\Tenant;
use <PERSON><PERSON><PERSON>di\App\Models\TenantOption;
use Illuminate\Support\Facades\Mail;
use <PERSON><PERSON>hidi\Core\Entity\ExportJob;

class SendAlertsJob{

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle($post_id, $job_id, $tenant_id)
    {

        DB::setDefaultConnection('tenant');
        $tenant = Tenant::find($tenant_id);
        if(!is_null($tenant)){
            $tenant->configure()->use();
        }
        
        $job = DB::table('export_job')->where('id', $job_id)->update(['status' => ExportJob::STATUS_QUEUED]);
        
        $post = DB::table('posts')->where('id',$post_id)->first();

        $point =  DB::table('post_point')->select(
            [DB::raw('ST_Y(value) as latitude'), DB::raw('ST_X(value) as longitude')]
        )->where('post_id',$post_id)->first();
     

        // $post_tags = DB::table('posts_tags')->where('post_id',$post_id)->pluck('tag_id')->toArray();
        $post_tags = DB::table('posts_tags')->where('post_id', $post_id)->pluck('tag_id')->first();
        if ($post_tags !== null) {
             $post_tags = [$post_tags]; // Convert the first element to an array
        } else {
             $post_tags = []; // Set an empty array if no element found
        }
      
        $emails = [];
        $alert_array = [];
        $site_public_name = '';

        if(!is_null($point)){

            $alerts = DB::connection('tenant')->table('alerts')
                            ->selectRaw("id,radius,categories,email,hash,
                             ( 6371 * acos( cos( radians(?) ) *
                               cos( radians(latitude) )
                               * cos( radians(longitude) - radians(?)
                               ) + sin( radians(?) ) *
                               sin( radians(latitude) ) ) 
                             ) AS distance", [$point->latitude, $point->longitude, $point->latitude])
                ->having("distance", "<=", 100)
                ->where("status", 1)
                ->orderBy("distance",'asc')
                ->get(); 
                
                $uniqueEmails = []; // Using associative array for faster lookup

foreach($alerts as $alert) {
    if($alert->distance <= $alert->radius) { 
        $cats = json_decode($alert->categories, true); // Decode as associative array
        $foundMatch = false;
        
        foreach($cats as $cat) {
            if(in_array($cat, $post_tags)) {
                $email = strtolower($alert->email);
                if(!isset($uniqueEmails[$email])) {
                    $uniqueEmails[$email] = [
                        'email' => $email,
                        'hash' => $alert->hash
                    ];
                    $foundMatch = true; // Set flag indicating a match was found
                }
            }
        }
        
        // If a match was found, push to $emails outside the inner loop
        if($foundMatch) {
            $emails[] = $uniqueEmails[$email];
        }
    }
}
        }

        $sender_name = TenantOption::where('tenant_id',$tenant_id)->where('tenant_key','sender_name')->first()->tenant_value;
        $sender_email = TenantOption::where('tenant_id',$tenant_id)->where('tenant_key','sender_email')->first()->tenant_value;
        $deployment_name = TenantOption::where('tenant_id',$tenant_id)->where('tenant_key','deploymentName')->first()->tenant_value;
        $public_name = TenantOption::where('tenant_id',$tenant_id)->where('tenant_key','publicName')->first()->tenant_value;
        if (isset($public_name) && !empty($public_name)) {
            $site_public_name = $public_name;
        } else {
            $site_public_name = $deployment_name;
        }

        // Post details data
        $post_title = $post->title;
        $post_id = $post->id;
        $post_link = '';
        $post_location = '';
        $post_location_array = [];
        if (isset($post->mgmt_lev_3) && !empty($post->mgmt_lev_3)) {
            $post_location_array[] = $post->mgmt_lev_3;
        }
        if (isset($post->mgmt_lev_1) && !empty($post->mgmt_lev_1)) {
            $post_location_array[] = $post->mgmt_lev_1;
        }
        if (isset($post->mgmt_lev_2) && !empty($post->mgmt_lev_2)) {
            $post_location_array[] = $post->mgmt_lev_2;
        }
        if (isset($post_location_array) && !empty($post_location_array)) {
            $post_location = implode(', ', $post_location_array);
        }
        $post_date = $post->post_date;
        $post_description = $post->content;
        $get_tags = DB::table('tags')
            ->select('tag')
            ->whereIn('id', $post_tags)->get()->pluck('tag');
        $tags_array = $get_tags->toArray();
        $post_tags = implode(', ', $tags_array);
        $frontend_url =  app('tenant')->frontend_url == '**************:8080' ? 'http://'.app('tenant')->frontend_url : 'https://'.app('tenant')->frontend_url;
        $country = app('tenant')->unique_name;

        if(!empty($emails)){
            foreach($emails as $email){
                try{
                    Mail::send('emails.post_alert', [
                        'post_id' => $post->id,
                        'subs_url' => url("/api/v3/get-alerts/unsubscribe-email/".$email['hash']),
                        'title' => $post_title,
                        'link' => $post_link,
                        'location' => $post_location,
                        'post_date' => $post_date,
                        'description' => $post_description,
                        'tags' => $post_tags,
                        'frontend_url' => $frontend_url,
                        'country' => $country,
                        'site_public_name' => $site_public_name,
                    ], function($message) use ($email, $sender_name, $sender_email){
                        $message->from($sender_email,$sender_name)->to($email['email'])->subject('Recibir alertas');
                    });
        
                }catch(\Exception $e){
                    //echo $e->getMessage();
                }
            }

            $job = DB::table('export_job')->where('id', $job_id)->update(['status' => ExportJob::STATUS_SUCCESS]);
        }

        return false;
    }

    
}
