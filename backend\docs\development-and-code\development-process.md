---
description: >-
  The work for the platform is done in 3 week cycles and is composed of 2
  phases.
---

# Development process

A Cycle is 3 weeks long, and it’s composed of 2 Phases:

* Plan and Reflect phase: 1 week long
* Development phase: 2 weeks long

## **Plan & Reflect Phase**

### **Reflect:**

The dev team reflects on process and iterates on it. This is done through retrospectives, tracking and notes from the cycle.

### **Plan**

The dev team prepares all work for the upcoming Dev Phase. This includes:

* Writing a Spec for each Issue in an Epic.
  * Writing demo scripts
  * Writing test scripts
  * Writing what needs to be done \(Intended behavior\)
  * If necessary, tech spec is written.
* Breaking tickets down to a small, releasable unit of work
* Dropping tickets that don’t serve the goal of a cycle, or would put the cycle at risk.
  * Dropping is reviewed with PM to be aligned and based on priorities.
  * Example: In Cycle \#0, we just want to be able to export data correctly. Anything that does not serve that goal is of less priority.
* Making sure we have the designs we need to start dev
* Mapping out how the dev phase has to run. For instance, flagging dependencies and having an idea of what goes first/last

## **Development Phase**

The dev team does the work they planned with the PM, and as they move forward, adjust as necessary.

In short: The development phase is when we go from zero to demo in each ticket.

#### Tracking:

Cumulative Burndown Charts are generated every 2 days so we can easily see the trends in our cycle.

#### DONE Reviews: 

For each ticket, the devs share a video with the PM demonstrating the work, or get on a demo call. This work either gets accepted \(goes into final testing phase\), or rejected \(goes back to Development\).

## Open source contributors

Open source contributors does not need to join and follow the cycles, you can do it in your own pace. We have marked issues that are up for grabs by community devs "​[Community task](https://github.com/ushahidi/platform/labels/Community%20Task)" in github.

Other tasks that we haven’t labeled yet may be suitable for work. Feel free to [contact](mailto:<EMAIL>) the team if you intend to work on something that grabs your attention. 

  


  
  
  
****

