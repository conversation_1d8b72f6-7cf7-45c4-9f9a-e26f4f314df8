version: "3.6"

services:
  frontend:
    container_name: frontend
    image: frontend
    build:
      context: frontend
      dockerfile: Dockerfile-static
      args:
        ENVIRONMENT: prod
    ports:
      - 8080:8080
    # depends_on:
    #   - backend

  backend:
    build: ./backend
    container_name: backend
    image: backend
    environment:
      REDIS_HOST: redis
      DOCKERIZE_WAIT_FOR_mysql: tcp://mysql:3306
      DOCKERIZE_WAIT_FOR_redis: tcp://redis:6379
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: ireport
      DB_USERNAME: ireport
      DB_PASSWORD: ireport
      ENABLE_PLATFORM_TASKS: 'true'
      ENABLE_QUEUE_LISTEN: 'true'
      APP_DEBUG: 'true'
      CACHE_DRIVER: 'array'
      TIME_ZONE: 'CAT'
      STORAGE_TYPE: 'local'
    command: start
    ports:
      - 4199:80
    # depends_on:
    #   - mysql
    #   - redis
    volumes:
      - ./backend/app:/var/www/app
      - ./backend/bootstrap:/var/www/bootstrap
      - ./backend/config:/var/www/config
      - ./backend/database:/var/www/database
      - ./backend/public:/var/www/public
      - ./backend/resources:/var/www/resources
      - ./backend/routes:/var/www/routes
      - ./backend/src:/var/www/src
      - ./backend/storage:/var/www/storage
      - ./backend/phinx.php:/var/www/phinx.php

  # mysql:
  #   image: mysql:8.0
  #   container_name: mysql
  #   ports:
  #     - '4198:3306'
  #   environment:
  #     MYSQL_ROOT_PASSWORD: root
  #     MYSQL_DATABASE: ireport
  #     MYSQL_USER: ireport
  #     MYSQL_PASSWORD: ireport
  #   volumes:
  #     - ./backend/init:/docker-entrypoint-initdb.d
  #     - mysql_data:/var/lib/mysql

  # redis:
  #   image: redis:alpine
  #   container_name: redis
  #   ports:
  #     - '6379:6379'

# volumes:
#   mysql_data: