version: "3.6"

services:
  frontend:
    container_name: frontend
    image: frontend
    build:
      context: frontend
      dockerfile: Dockerfile-static
      args:
        ENVIRONMENT: prod
    ports:
      - 8080:8080
    # depends_on:
    #   - backend

  backend:
    build: ./backend
    container_name: backend
    image: backend
    environment:
      # Connect to WAMP MySQL on host machine
      DB_HOST: host.docker.internal
      DB_PORT: 3306
      DB_DATABASE: ireport
      DB_USERNAME: root
      DB_PASSWORD: ""
      ENABLE_PLATFORM_TASKS: 'true'
      ENABLE_QUEUE_LISTEN: 'true'
      APP_DEBUG: 'true'
      CACHE_DRIVER: 'array'
      TIME_ZONE: 'CAT'
      STORAGE_TYPE: 'local'
    command: start
    ports:
      - 4199:80
    volumes:
      - ./backend/app:/var/www/app
      - ./backend/bootstrap:/var/www/bootstrap
      - ./backend/config:/var/www/config
      - ./backend/database:/var/www/database
      - ./backend/public:/var/www/public
      - ./backend/resources:/var/www/resources
      - ./backend/routes:/var/www/routes
      - ./backend/src:/var/www/src
      - ./backend/storage:/var/www/storage
      - ./backend/phinx.php:/var/www/phinx.php

