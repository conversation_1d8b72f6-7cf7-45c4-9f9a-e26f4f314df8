{"name": "gasparesganga/php-shapefile", "description": "PHP library to read and write ESRI Shapefiles, compatible with WKT and GeoJSON", "type": "library", "keywords": ["shapefile", "shape", "<PERSON><PERSON>", "shp", "dbf", "wkt", "g<PERSON><PERSON><PERSON>"], "homepage": "https://gasparesganga.com/labs/php-shapefile/", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://gasparesganga.com/labs/php-shapefile/", "role": "Developer"}], "require": {"php": ">=5.4.0"}, "autoload": {"psr-4": {"Shapefile\\": "src/Shapefile/"}}}