<?php

/**
 * <PERSON><PERSON><PERSON>di Role Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use <PERSON>hahidi\Core\Tool\Authorizer;
use Us<PERSON>hidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Tool\Permissions\AclTrait;
use Ushahidi\Core\Entity\Permission;

class RoleAuthorizer implements Authorizer
{
    use UserContext;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;
    
    // Check if user has Admin access
    use AdminAccess;

    use AclTrait;


    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {
        // These checks are run within the user context.
        $user = $this->getUser();
        
        if ($privilege === 'delete' && $entity->protected === true) {
            return false;
        }

        if($this->acl->hasPermission($user, Permission::ACCESS_AND_MODIFY_PARAMETERS_SETTINGS)) {
            return true;
        }
        
        // Only allow admin access
        if ($this->isUserAdmin($user)) {
            return true;
        }

        if ($user->getId() and $privilege === 'read') {
            return true;
        }
        // All users are allowed to search forms.
        if ($user->getId() and $privilege === 'search') {
            return true;
        }

        return false;
    }
}
