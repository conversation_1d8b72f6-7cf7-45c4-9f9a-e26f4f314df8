<?php

namespace Us<PERSON>hidi\App\Http\Controllers\API;

use <PERSON>hahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use Ushahidi\Core\Usecase;
use League\Csv\Reader;
use League\Csv\Writer;
use DB;
use Log;

/**
 * Ushahidi API Contacts Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class PostsCSVController extends RESTController {

    protected function getResource(){
        return 'posts';
    }

    protected function getIdentifiers(Request $request)
    {
        return $this->getRouteParams($request);
    }

    protected function getFilters(Request $request)
    {
        $user               = service('session')->getUser();
        $params             = $this->getRouteParams($request);
        $params['parent']   = isset($params['parent_id']) ? $params['parent_id'] : null;
    
        if(!is_null($user->mgmt_lev_1) && $user->mgmt_lev_1!="") { 
            $params['mgmt_lev_1'] = $user->mgmt_lev_1; 
        }
        
        return $request->query() + $params;
    }
    public function exportCSV(Request $request) 
    {        
        header("Content-Type: application/json; charset=UTF-8");
        $field_name_mapping = [];
        $tags_keys = [];
        $tags_mapping = [];
        $postRows = [];
        $location_key = $latitude = $longitude = null;

        // Get filter values from request for POST search
        $filters        = $this->demoCheck($this->getFilters($request));
        $this->usecase  = $this->usecaseFactory
            ->get($this->getResource(), 'search')
            ->setFilters($filters);
        $posts =$this->executeUsecase($request);
        $posts = $posts['results'];

        // Get all form fields based on form
        $attributes_filters = array(
            'form_id' => $filters['form'],
        );
        $this->usecase = $this->usecaseFactory
            ->get('form_attributes', 'search')
            ->setFilters($attributes_filters);
        $form_fields = $this->executeUsecase($request);
        $form_fields = $form_fields['results'];
        // CSV Headers Posts
        // $header = [
        //     'ID del informe', 'Estado del informe', 'creado', 'actualizado', 'Fecha de reporte del informe',
        //     'Departamento', 'Provincia' ,'Distrito', 'ID padre', 'Título del informe', 'Descripción', 'Latitud', 'Longitud'
        // ];
        
        $header = [
            'Report ID', 'Report Status', 'Created', 'Updated', 'Report Date',
             'Region', 'District', 'Parent ID', 'Report Title', 'Description', 'Latitude', 'Longitude'
        ];

        if ($filters['form'] == 1) {
            $header[] = 'Published';
        }
 
                        
        // ignore default fields
        $ignore_keys = ['title', 'description'];

        // Generate CSV extra Headers from form fields used in current form
        foreach($form_fields as $key => $value){
            if (!in_array($value['type'],$ignore_keys)) {
                if ($value['type']=='point') { // Location Type
                    $location_key = $value['key'];
                } else {
                    $field_name_mapping[$value['key']] = $value['label'];
                    $header[] = $value['label'];

                    // tags related fields
                    if ($value['type'] == 'tags') {
                        $tags_keys[$value['key']] = $value['label'];
                    }
                }
            }
        }

        // Change usecase to use form_attributes
        $this->usecase = $this->usecaseFactory
            ->get('tags', 'search');
        $raw_tags_mapping = $this->executeUsecase($request);
        $raw_tags_mapping =$raw_tags_mapping['results'];
        
        foreach($raw_tags_mapping as $key => $value){
            $tags_mapping[$value['id']] = $value['tag'];
        }

        // Posts data generation for CSV
        // $publishedMap = [
        //     1 => 'Publicar',
        //     0 => 'No Publicar',
        // ];

        // $postStatusMap = [
        //     'draft' => 'Pendiente',
        //     'verification' => 'Verificación en progreso',
        //     'published' => 'Verificado',
        //     'archived' => 'No verificado',
        //     'responded' => 'Respondido',
        //     'evaluated' => 'Evaluado',
        // ];

        $publishedMap = [
            1 => 'Published',
            0 => 'Not Published',
        ];

        $postStatusMap = [
            'draft' => 'Draft',
            'verification' => 'Verification in progress',
            'published' => 'Published',
            'archived' => 'Archieved',
            'responded' => 'Responded',
            'evaluated' => 'Evaluated',
        ];
        
        foreach($posts as $key=>$value) {
            $postRows[$key]['Post ID'] = isset($value['id'])?$value['id']:'';
          //  $postRows[$key]['Post Status'] = isset($value['status'])?$value['status']:'';
            $postRows[$key]['Post Status'] = isset($value['status'])?($postStatusMap[$value['status']] ?? ''):'';
            $postRows[$key]['Created'] = isset($value['created'])?$value['created']:'';
            $postRows[$key]['Updated'] = isset($value['updated'])?$value['updated']:'';
            $postRows[$key]['Post Date'] = isset($value['post_date'])?$value['post_date']:'';
//            $postRows[$key]['Ward'] = isset($value['mgmt_lev_3'])?$value['mgmt_lev_3']:'';
            $postRows[$key]['County'] = isset($value['mgmt_lev_1'])?$value['mgmt_lev_1']:'';
            $postRows[$key]['District'] = isset($value['mgmt_lev_2'])?$value['mgmt_lev_2']:'';
            $postRows[$key]['Parent ID'] = isset($value['parent_id'])?$value['parent_id']:'';
            $postRows[$key]['Title of incident'] = isset($value['title'])?$value['title']:'';
            $postRows[$key]['Description'] = isset($value['content'])?$value['content']:'';

            // Get location values from location field
            if (isset($location_key) && isset($value['values'][$location_key])) {
                $postRows[$key]['Latitude'] = $value['values'][$location_key][0]['lat'];
                $postRows[$key]['Longitude'] = $value['values'][$location_key][0]['lon'];
            } else {
                $postRows[$key]['Latitude'] = '';
                $postRows[$key]['Longitude'] = '';
            }
            if($filters['form'] == 1) {
               $postRows[$key]['published'] = isset($value['published']) ? ($publishedMap[$value['published']] ?? '') : '';
            }
            if (isset($value['values'])) {
                foreach ($field_name_mapping as $field_key => $field_value) {
                    // Add form fields values apart from default fields
                    if (isset($value['values']) && array_key_exists($field_key, $value['values'])) {
                        // Tags are return in numeric Ids so we will take their proper labels
                        if (isset($tags_keys) && array_key_exists($field_key, $tags_keys)) {
                            $temp_tag_id = $value['values'][$field_key][0];
                            $postRows[$key][$field_key] = $tags_mapping[$temp_tag_id];
                        } else {
                            $postRows[$key][$field_key] = $value['values'][$field_key][0];
                        }
                    } else {
                        $postRows[$key][$field_key] = '';
                    }
                }
            }
        }


        $json_response = [];
        
        if (isset($postRows) && !empty($postRows)) {
            $json_response = [
                'success' => true, 
                'headers' => $header,
                'data' => $postRows
            ];
        } else {
            $json_response = [
                'success' => false
            ];
        }

        return response($json_response);

        // // CSV Write
        // $csv 		= Writer::createFromFileObject(new \SplTempFileObject());
		
        // // Add Headers in CSV file
		// $csv->insertOne($header);
        // $out        = fopen('php://output', 'w');        

        // // Insert Data into rows
        // $csv->insertAll($postRows);
        // return response($csv->output())
        //     ->withHeaders([
        //         'Content-Type'          => 'application/csv',
        //         'Content-Disposition'   => 'attachment; filename="Reports.csv"',
        //     ]);
    }   
}