<?php

/**
 * <PERSON><PERSON>hidi Permission Entity
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Entity;

use <PERSON><PERSON>hidi\Core\StaticEntity;

class Permission extends StaticEntity
{
    protected $id;
    protected $name;
    protected $description;
    // FIXME: this LEGACY_DATA_IMPORT has to be removed after the prod release
    const LEGACY_DATA_IMPORT    = 'Bulk Data Import';
    // Standard permissions names
    const DATA_IMPORT_EXPORT    = 'Bulk Data Import and Export';
    // const MANAGE_POSTS          = 'Manage Posts';
    // const MANAGE_SETS           = 'Manage Collections and Saved Searches';
    const MANAGE_SETTINGS       = 'Manage Settings';
    // const MANAGE_USERS          = 'Manage Users';
    // const EDIT_OWN_POSTS        = 'Edit their own posts';

    // UJEVMS-52 - <PERSON><PERSON><PERSON> Luca, <EMAIL> - 28/01/2020.
    // const MANAGE_COMMENTS       = 'Manage Comments';

    // UJEVMS-56 - SCALA Luca, <EMAIL> - 30/01/2020.
    const LIST_USERS            = 'List Users';

    // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/08/2020.

    const MAKE_CHANGES_TO_PLATFORM = 'Make Changes To The EVIEW Platform';
    const TROUBLESHOOTING = 'Troubleshooting';
    const ASSIGN_SUPER_ROLE = 'Assign Super User Roles';
    const CREATE_USERS = 'Create Users';
    const ASSIGN_ROLES = 'Assign User Roles';
    const DELETE_ROLES = 'Delete Users';
    const MANAGE_SETUP_INFO = 'Access/Edit User Server Setup Information';
    const VIEW_NEW_DELETE_ACCOUNT = 'View Created And Deleted Accounts';
    const VIEW_USER_ACTIVITY = 'View User Activity';
    const SET_RISK_CRITERIA = 'Set Risk Forecasting Criteria';
    const ADD_RISK_DATA = 'Add Risk Data';
    const MOD_RISK_DATA = 'Modify Risk Data';
    const DEL_RISK_DATA = 'Delete Risk Data';
    const CREATE_POST = 'Register Incident';
    const VIEW_POSTS = 'View Incidents';
    const FILTER_POSTS = 'Filters Incident By Category';
    const DEL_POSTS = 'Delete Incident';
    const EDIT_POSTS = 'Edit Incident';
    const LINK_POSTS = 'Link/Unlink Incident';
    const ESCALATE_POSTS = 'Escalate Incident';
    const MGMT_COLLECTION_MSG = 'Manage Collections, Saved Searches and Messages';

    const ACCESS_EVIEW_DASHBOARD = 'Access CEWER dashboard';
    const CREATE_REPORT = 'Create report';
    const ACCESS_INCIDENT_RISK_REPORTS = 'Access incident/risk reports';
    const VIEW_INCIDENT_RISK_REPORTER_IDENTITY = 'View incident/risk reporter identity';
    const MODIFY_REPORT_INFORMATION = 'Modify report information';
    const MODIFY_PERSONAL_INCIDENT_RISK_REPORTS = 'Modify personal incident/risk reports';
    const ACCESS_ANALYSIS_AND_FILTERS_SECTION = 'Access analysis and filters section';
    const ACCESS_AND_MODIFY_PARAMETERS_SETTINGS = 'Access and modify parameters settings';
    const EXPOERT_INCIDENT_LIST = 'Export incident list';

    //  new permissions

    const AccessManagement = "Access Management";
    const UserManagement = "User Management";
    const  ViewReportsMapConflictividadElectoral = "View Reports - Map - Conflictividad Electoral";
    const ViewReportsMapElectionOperations = "View Reports - Map - Operaciones Electorales";
    const ViewReportsListConflictividadElectoral = "View Reports - List - Conflictividad Electoral";
    const ViewReportsListElectionOperations = "View Reports - List - Operaciones Electorales";
    const  CommentReportConflictividadElectoral = "Comment Report - Conflictividad Electoral";
    const CommentReportOperacionesElectorales = "Comment Report - Operaciones Electorales";
    const  CreateANewreportConflictividadElectoral = "Create a new report - Conflictividad Electoral";
    const  CreateANewReportOperacionesElectorales = "Create a new report - Operaciones Electorales";
    const EditOwnIncidentReportContentConflictividadElectoral = "Edit own incident report content - Conflictividad Electoral";
    const EditOwnIncidentReportContentOperacionesElectorales = "Edit own incident report content - Operaciones Electorales";
    const EditAndManageReportsConflictividadElectoral = "Edit and manage reports - Conflictividad Electoral";
    const EditAndManageReportsOperacionesElectorales = "Edit and manage reports - Operaciones Electorales";
    const AccessAndEditIncidentReportsFormNamesOfDeports= "Access and edit incident reports form - Names of reports";
    const  AccessAndEditIncidentIndicators  = "Access and edit incident indicators";
    const  DeleteReportsConflictividadElectoral = "Delete reports - Conflictividad Electoral";
    const DeleteReportsOperacionesElectorales = "Delete reports - Operaciones Electorales";
    const ViewIncidentReporterIdentityConflictividadElectoral = "View incident reporter identity - Conflictividad Electoral";
    const ViewIncidentReporterIdentityOperacionesElectorales = "View incident reporter identity - Operaciones Electorales";
    const ExportIncidentListConflictividadElectoral = "Export incident list - Conflictividad Electoral";
    const ExportIncidentListOperacionesElectorales = "Export incident list - Operaciones Electorales";
    const AccessAnalysisDashboardConflictividadElectoral = "Access Analysis Dashboard - Conflictividad Electoral";
    const AccessAnalysisDashboardOperacionesElectorales = "Access Analysis Dashboard - Operaciones Electorales";

    // DataTransformer
    public function getDefinition()
    {
        return [
            'id' => 'int',
            'name' => 'string',
            'description' => 'string',
        ];
    }

    // Entity
    public function getResource()
    {
        return 'permission';
    }

    // StatefulData
    protected function getImmutable()
    {
        return array_merge(parent::getImmutable(), ['name']);
    }
}
