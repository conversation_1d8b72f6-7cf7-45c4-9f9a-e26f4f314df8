<?php

/**
 * Us<PERSON>hidi Form Stage Validator
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Validator\Form\Role;

use <PERSON>hahidi\Core\Entity;
use Ushahidi\Core\Entity\FormRepository;
use Ushahidi\Core\Entity\RoleRepository;
use Ushahidi\App\Validator\LegacyValidator;

class Update extends LegacyValidator
{
    protected $form_repo;
    protected $role_repo;
    protected $default_error_source = 'form_role';

    public function setFormRepo(FormRepository $form_repo)
    {
        $this->form_repo = $form_repo;
    }

    public function setRoleRepo(RoleRepository $role_repo)
    {
        $this->role_repo = $role_repo;
    }

    protected function getRules()
    {
        return [
            'form_id' => [
                ['digit'],
                [[$this->form_repo, 'exists'], [':value']],
            ],
            'role_id' => [
                [[$this->role_repo, 'idExists'], [':value']],
            ],
        ];
    }
}
