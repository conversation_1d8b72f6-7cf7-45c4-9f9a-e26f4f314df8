<?php

/**
 * <PERSON><PERSON>hidi Password Hasher
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Hasher;

use <PERSON><PERSON>hidi\Core\Tool\Hasher;

class Password implements Hasher
{
    public function hash($password)
    {
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
    }
}
