<?php

/**
 * <PERSON><PERSON><PERSON><PERSON> CSV Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use Ushahidi\Core\Entity\CSV;
use Us<PERSON>hidi\Core\Entity\Permission;
use Ushahidi\Core\Tool\Authorizer;
use Ushahidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\UserContext;
use Us<PERSON>hidi\Core\Traits\PrivAccess;
use <PERSON><PERSON>hidi\Core\Tool\Permissions\AclTrait;
use Ushahidi\App\Facades\Features;

class CSVAuthorizer implements Authorizer
{
    use UserContext;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;

    // Check if user has Admin access
    use AdminAccess;

    // Check that the user has the necessary permissions
    // if roles are available for this deployment.
    use AclTrait;

    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {
        // Check if the user can import data first
        if (!Features::isEnabled('data-import')) {
            return false;
        }

        // These checks are run within the user context.
        $user = $this->getUser();

        // Allow role with the right permissions
        // $this->acl->hasPermission($user, Permission::DATA_IMPORT_EXPORT) or
        // $this->acl->hasPermission($user, Permission::LEGACY_DATA_IMPORT)
        if ($this->acl->hasPermission($user, [Permission::ExportIncidentListConflictividadElectoral,Permission::ExportIncidentListOperacionesElectorales ])) {
            return true;
        }

        // Allow admin access
        if ($this->isUserAdmin($user)) {
            return true;
        }

        return false;
    }
}
