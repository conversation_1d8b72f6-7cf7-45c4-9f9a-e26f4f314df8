<?php

namespace Us<PERSON>hidi\App\Http\Controllers\API\Posts;

use Us<PERSON>hidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use <PERSON>hahidi\Core\Usecase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Ushahidi API Posts Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class PostsController extends RESTController
{
    /**
     * @var int Post Parent ID
     */
    protected $parentId = null;

    /**
     * @var string Post Type
     */
   // protected $postType = 'report';

    // Ushahidi_Rest
    protected function getResource()
    {
        return 'posts';
    }

    protected function getIdentifiers(Request $request)
    {
        return $this->getRouteParams($request);
        //  + [
        //     'type'      => $this->postType
        // ];
    }

    protected function getFilters(Request $request)
    {
        $user               = service('session')->getUser();
        //$userExtra          = service('repository.users_ex')->getByUserId($user->getId());
        $params             = $this->getRouteParams($request);
       // $params['type'  ]   = $this->postType;
        $params['parent']   = isset($params['parent_id']) ? $params['parent_id'] : null;
    
        
        if(!is_null($user->mgmt_lev_1) && $user->mgmt_lev_1!="") { $params['mgmt_lev_1'] = $user->mgmt_lev_1; }
        
        return $request->query() + $params;
    }

    protected function getPayload(Request $request)
    {
        $params = $this->getRouteParams($request);
        return $request->json()->all() + [
           // 'type'      => $this->postType,
            'parent_id' => isset($params['parent_id']) ? $params['parent_id'] : null,
        ];
    }

    /**
     * Create A Post
     *
     * POST /api/posts
     *
     * @return void
     */
    public function store(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'create')
            ->setPayload($this->getPayload($request))
            ->setIdentifiers($this->getIdentifiers($request));

        return $this->prepResponse($this->executeUsecase($request), $request);
    }

    /**
     * Retrieve All Posts
     *
     * GET /api/posts
     *
     * @return void
     */
    public function index(Request $request)
    {
	    $filters        = $this->demoCheck($this->getFilters($request));
	//  Log::info('Debugging information: filters: ' . json_encode($filters));
        $this->usecase  = $this->usecaseFactory
            ->get($this->getResource(), 'search')
            ->setFilters($filters)
            ->setIdentifiers($this->getIdentifiers($request));

        return $this->prepResponse($this->executeUsecase($request), $request);
    }

    /**
     * Retrieve An Entity
     *
     * GET /api/foo/:id
     *
     * @return void
     */
    public function show(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'read')
            ->setIdentifiers($this->getIdentifiers($request));

        return $this->prepResponse($this->executeUsecase($request), $request);
    }

    /**
     * Update An Entity
     *
     * PUT /api/foo/:id
     *
     * @return void
     */
    public function update(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'update')
            ->setIdentifiers($this->getIdentifiers($request))
            ->setPayload($this->getPayload($request));

        return $this->prepResponse($this->executeUsecase($request), $request);
    }

    /**
     * Delete An Entity
     *
     * DELETE /api/foo/:id
     *
     * @return void
     */
    public function destroy(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'delete')
            ->setIdentifiers($this->getIdentifiers($request));

        return $this->prepResponse($this->executeUsecase($request), $request);
    }

    /**
     * Retrieve post stats
     *
     * GET /api/posts/stats
     *
     * @return void
     */
    public function stats(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'stats')
            ->setFilters($this->getFilters($request))
            // @todo allow injecting formatters based on resource + action
            ->setFormatter(service('formatter.entity.post.stats'));

        return $this->prepResponse($this->executeUsecase($request), $request);
    }

    /**
     * Retrieve post count by filters
     * Post /api/posts/analysis-report
     *
     * @return results
     */
    public function analysisReports(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'analysis')
            ->setIdentifiers($this->getPayload($request));
        return $this->prepResponse($this->executeUsecase($request), $request);
    }

            /**
     * Retrieve post data for Dashboard
     *
     * GET /api/posts/dashboardregion
     *
     * @return void
     */
    public function dashboardRegion(Request $request)
    {
        $response = [];
        $filters  = $this->demoCheck($this->getFilters($request));

        $query = DB::table('posts')->select('id');
        $query = $query->whereNotNull('form_id');

        if($request->has('formId')){            
            $query = $query->where('form_id','=', $request->get('formId'));
        }
        if($request->has('from_date') && $request->has('to_date')){ 
            $created_after = strtotime($request->get('from_date'));
            $to_date = $request->get('to_date');
            $created_before = strtotime($to_date.' 23:59:59');
            $query = $query->whereBetween('created', [$created_after, $created_before]);
        }

        $response['total_posts'] = $query->get()->count();

        $regionQuery  =   DB::table('posts')->select('mgmt_lev_1 as regions', DB::raw('COUNT(*) as total_count'));
        $regionQuery = $regionQuery->whereNotNull('form_id');
        
        if($request->has('formId')){            
            $regionQuery = $regionQuery->where('form_id','=', $request->get('formId'));
        }
        if($request->has('from_date') && $request->has('to_date')){ 
            $created_after = strtotime($request->get('from_date'));
            $to_date = $request->get('to_date');
            $created_before = strtotime($to_date.' 23:59:59');
            $regionQuery = $regionQuery->whereBetween('created', [$created_after, $created_before]);
        }


        $regions = $regionQuery->groupBy('mgmt_lev_1')->get();

        foreach($regions as $key=>$value) {
            //$region_name = empty($value->regions) ? 'NA':$value->regions;
            $response['regions'][$value->regions] = $value->total_count;
        }
         if($request->get('formId') == 1) {
            // $pie_chart_women = $this->getCategoryData($request->get('att_1'), $request);
            // if($pie_chart_women['yes'] > 0 || $pie_chart_women['no'] > 0 ) {
            //   $response['pie_chart']['women'] = $pie_chart_women;
            // }
            //  $pie_chart_minorities = $this->getCategoryData($request->get('att_2'), $request);
            // if($pie_chart_minorities['yes'] > 0 || $pie_chart_minorities['no'] > 0 ) {
            //   $response['pie_chart']['minorities'] = $pie_chart_minorities;
            // }

            //  $pie_chart_LGTBIQ = $this->getCategoryData($request->get('att_3'), $request);
            // if($pie_chart_LGTBIQ['yes'] > 0 || $pie_chart_LGTBIQ['no'] > 0 ) {
            //   $response['pie_chart']['LGTBIQ'] = $pie_chart_LGTBIQ;
            // }

            $pie_chart_one = $this->getTagData($request->get('att_1'), $request);
            $pie_chart_two = $this->getTagData($request->get('att_2'), $request);
            $pie_chart_three= $this->getTagData($request->get('att_3'), $request);
            $response['pie_chart'][$request->get('att_1')] = $pie_chart_one;
            $response['pie_chart'][$request->get('att_2')] = $pie_chart_two;
            $response['pie_chart'][$request->get('att_3')] = $pie_chart_three;
   
         }
        return response()->json($response,200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
    }

    /**
     * Retrieve post data for Dashboard Trend
     *
     * GET /api/posts/dashboardtrend
     *
     * @return void
     */
    public function dashboardTrend(Request $request)
    {
        $response = [];
        $monthly_records = [];
        $daily_records = [];
 
        // Request Parameters
        $month = $request->get('date_month');
        $year = $request->get('date_year');
        $formId = $request->get('formId');

        // Dates value conversion
        if (isset($month) && isset($month)) {
            $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
            $start_date_of_month = strtotime( "$year-$month-01 00:00:00");
            $start_date_of_year = strtotime( "$year-01-01 00:00:00");
            $end_date_of_year = strtotime( "$year-12-31 23:59:59");
            $end_date_of_month = strtotime( "$year-$month-$daysInMonth 23:59:59");
            $response['month'] = $month;
            $response['year'] = $year;

            // Calculate Data for Monthly Report
            $query  =   DB::table('posts')->selectRaw('YEAR(FROM_UNIXTIME(created)) AS Y, 
            MONTH(FROM_UNIXTIME(created)) AS m, 
            COUNT(id) AS COUNT');

            if($request->has('formId')){            
                $query = $query->where('form_id','=', $request->get('formId'));
            }

            $query = $query->whereBetween('created', [$start_date_of_year, $end_date_of_year]);
            $query->groupBy('Y','m');
            $result = $query->get();

            if (isset($result) && !empty($result)) {
                foreach($result as $key=>$value) {
                    $monthly_records[$value->m] = $value->COUNT;
                }

                // Generate monthly array for a given year 
                for($i=1;$i<=12;$i++) {
                    if (!isset($monthly_records[$i])) {
                        $monthly_records[$i] = 0;
                    }
                }
                ksort($monthly_records);
                $response['monthly_records'] = $monthly_records;
            }
            
            // Calculate Data for Daily Report
            $dailyQuery  =   DB::table('posts')->selectRaw('YEAR(FROM_UNIXTIME(created)) AS Y, 
            MONTH(FROM_UNIXTIME(created)) AS m, 
            DAY(FROM_UNIXTIME(created)) AS d, 
            COUNT(id) AS COUNT');

            if($request->has('formId')){            
                $dailyQuery = $dailyQuery->where('form_id','=', $request->get('formId'));
            }

            $dailyQuery = $dailyQuery->whereBetween('created', [$start_date_of_month, $end_date_of_month]);
            $dailyQuery->groupBy('Y','m','d');
            $result = $dailyQuery->get();

            if (isset($result) && !empty($result)) {
                $sum = 0;
                foreach($result as $key=>$value) {
                    $daily_records[$value->d] = $value->COUNT;
                    $sum = $sum + $value->COUNT;
                }

                for($i=1;$i<=$daysInMonth;$i++) {
                    if (!isset($daily_records[$i])) {
                        $daily_records[$i] = 0;
                    }
                }
                ksort($daily_records);
                $response['daily_records'] = $daily_records;
            }
        }

        return response()->json($response,200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
    }


    private function getCategoryData($categoryId, $request)
    {
        $category = DB::table('form_attributes')->where('key', $categoryId)->get()[0];
    
        $sumValues = 0;
        $count = NULL;
    
        if ($request->has('from_date') && $request->has('to_date')) {
            $created_after = strtotime($request->get('from_date'));
            $to_date = $request->get('to_date');
            $created_before = strtotime($to_date . ' 23:59:59');
    
            $count = DB::table('post_decimal')
                ->join('posts', 'posts.id', '=' , 'post_decimal.post_id')
                ->where('post_decimal.form_attribute_id', $category->id)
                ->where('posts.form_id',  $request->get('formId'))
                ->whereBetween('posts.created', [$created_after, $created_before])
                ->get();
        }
    
        if ($count && count($count) > 0) {
            foreach ($count as $result) {
                if (is_numeric($result->value)) {
                    $sumValues += (float)$result->value;
                }
            }
        } 
        $config = json_decode($category->config);
        $result = $this->getYesOrNoCount($config[0]->target->key , [$created_after, $created_before], $request->get('formId'));
    
        return [
            'yes' => empty($result) || !isset($result[0]->count_yes) ? 0 : $result[0]->count_yes , // replace with actual 'si' count
            'no' => empty($result) || !isset($result[0]->count_no) ? 0 : $result[0]->count_no, // replace with actual 'no' count
            'total_affected_count' => $sumValues,
        ];
    }

    private function getYesOrNoCount($formAttributeId , $dateTme , $formId) {
      return DB::table('form_attributes')
      ->select(
        DB::raw('count(CASE WHEN tags.slug = "si" THEN posts_tags.id END) as count_yes'),
        DB::raw('count(CASE WHEN tags.slug = "no" THEN posts_tags.id END) as count_no')) 
    ->join('posts_tags', 'form_attributes.id', '=', 'posts_tags.form_attribute_id')
    ->join('posts', 'posts.id', '=' , 'posts_tags.post_id')
    ->join('tags', 'posts_tags.tag_id', '=', 'tags.id')
    ->where('form_attributes.key', $formAttributeId)
    ->where('posts.form_id',  $formId)
    ->whereIn('tags.slug', ['si', 'no']) // Use whereIn for multiple conditions
    ->whereBetween('posts.created', $dateTme)
    ->groupBy('form_attributes.id')
    ->get();
    }

    private function getTagData($categoryId, $request){
        $tag = DB::table('form_attributes')->where('key', $categoryId)->get()[0];
        $label = $tag->label;
        $data = DB::table('posts_tags')
            ->join('tags', 'posts_tags.tag_id', '=', 'tags.id')
            ->where('posts_tags.form_attribute_id',  $tag->id)
            ->select('tags.tag', DB::raw('count(posts_tags.tag_id) as count'))
            ->groupBy('posts_tags.tag_id')
            ->pluck('count', 'tags.tag'); 
           
        return $data;
    }
}
