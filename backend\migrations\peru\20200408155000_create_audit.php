<?php

// UJEVMS-65 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 08/04/2020.

use Phinx\Migration\AbstractMigration;

class CreateAudit extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('audit', [
                'id' => false
            ])
      ->addColumn('id', 'integer')
      ->addColumn('entity', 'string')
      ->addColumn('value', 'text')
      ->addColumn('user_id', 'integer')
      ->addColumn('created', 'integer')
      ->create();
  }
}
