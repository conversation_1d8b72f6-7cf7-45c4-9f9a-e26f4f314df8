<?php

// UJEVMS-65 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 08/04/2020.

namespace <PERSON><PERSON><PERSON>di\Core\Entity;

use <PERSON><PERSON><PERSON>di\Core\StaticEntity;

class Audit extends StaticEntity
{
    protected $id;
    protected $user_id;
    protected $entity;
    protected $created;
    protected $value;
    protected $type;

    protected function getDefinition()
    {
        return [
            'id'             => 'int',
            'user_id'        => 'int',
            'entity'         => 'string',
            'value'          => 'string',
            'created'        => 'int',
            'type'           => 'string'
        ];
    }

    public function getResource()
    {
        return 'audit';
    }
}
