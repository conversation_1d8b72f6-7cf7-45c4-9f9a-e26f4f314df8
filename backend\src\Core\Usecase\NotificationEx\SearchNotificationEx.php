<?php

// UJEVMS-62 - <PERSON><PERSON><PERSON>, <EMAIL> - 17/02/2020.

namespace <PERSON><PERSON>hidi\Core\Usecase\NotificationEx;

use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\SearchUsecase;

class SearchNotificationEx extends SearchUsecase
{
  protected function getPagingFields()
  {
    return [
      'orderby' => 'id',
      'order'   => 'desc',
      'limit'   => 20,
      'offset'  => 0
    ];
  }
}
