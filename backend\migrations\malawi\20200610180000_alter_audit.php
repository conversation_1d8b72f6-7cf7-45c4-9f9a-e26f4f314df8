<?php

// UJEVMS-65 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 08/04/2020.

use Phinx\Migration\AbstractMigration;

class AlterAudit extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('audit')
      ->addColumn('type', 'string', [
        'default' => '',
        'comment' => 'Define type of activity',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
