# Documentation

## Contributing via GitHub

Anyone may suggest changes or additions to the documentation that you are reading. All that is required is a Github account and some familiarity with the [Markdown](https://guides.github.com/features/mastering-markdown/) format.

Assuming you have a github account and there's some document you would like to edit, please follow this [guide](contributing-docs-via-github.md).

## Major contributions

If you have to remember three words from all this, those would be: concise, direct and neutral

If you are planning to provide a major contribution, consisting of significant amounts of content, we may be able to help you.

For instance, if your intention is to edit a lot of pages or to create new complete sections, you may find it easier to do that using the GitBook web interface.

In order to obtain permission to edit via GitBook, you will need two things:

1. A github account
2. Send us a message on [<EMAIL>](mailto:<EMAIL>)

## Style considerations

Our intention is to make our documentation available to as many people as possible. The Ushahidi Platform is used all over the world by people of different cultures, ages and linguistic backgrounds.

We use the English language as it is the most widely spread language for global communication. While making this choice, we must be mindful of the diversity of our audience. Not all our readers may be comfortable with English. We must not add to their burden with confused explanations, complicated structure or obscure wording.

Here are a few things to keep in mind. 

* **Keep your pages short**. If you have to scroll down more than two or three times so as to review the content of your newly created page, it may be worth splitting that content up into two or several different pages.
* **Keep your text to the point**. Avoid at all costs quickly rambling through different ideas or adding to the same page concepts that are not directly connected.
* **Explicit is better than implicit**. Be mindful of what you are assuming your audience to know. If you would rather not go into the length of explaining something, consider linking to an explanation. If you feel some level of knowledge is required to go through your text, be clear about it. Also consider adding links that may help your audience obtain that knowledge.
* **Use paragraphs effectively**. Try to keep your paragraphs short. Avoid mixing ideas in the same paragraph. A new idea, perspective or concept is a great opportunity to start a new paragraph. Complex ideas can take several paragraphs. If your paragraph contains more than 8 lines or sentences, this is a strong signal for you to consider splitting that paragraph up.
* **Don't use sophisticated language**. Our documentation is not intended only for native or advanced English speakers. Any person with a beginner to intermediate level should be able to read this without giving up.
* **Use short sentences and neutral, standard vocabulary.** Please try to write in short sentences. Avoid long sentences with multiple conjunctions. Also use vocabulary as plain as possible. Avoid obscure words and local, excessively colloquial or elaborate expressions.  If you feel it's absolutely necessary to use a specific, not widely used term, please consider linking to its definition.



