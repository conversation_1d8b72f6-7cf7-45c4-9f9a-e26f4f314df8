<?php

// UJEVMS-69 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 07/09/2020.


use Phinx\Migration\AbstractMigration;

class AddPermissionExportIncidentList extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

    $this->execute("
    start transaction;
    
    set FOREIGN_KEY_CHECKS = 0;

    insert into roles_permissions (role, permission) values
        ('super', 'Export incident list')
        , ('admin', 'Export incident list')
        , ('managementuser', 'Export incident list')
        , ('sysadmin', 'Export incident list');


    insert into permissions (name, description) values 
        ('Export incident list', 'Export incident list');
    
    set FOREIGN_KEY_CHECKS = 1;
    
    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
