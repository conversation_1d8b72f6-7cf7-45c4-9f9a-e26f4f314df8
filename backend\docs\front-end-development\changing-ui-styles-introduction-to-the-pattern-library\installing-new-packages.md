# Installing new packages

Third party packages, libraries and/or plugins can easily be added to the pattern library for additional features and functionality.

### Installing New Packages

New packages can be added to your project by running npm install _example-package_ --save-dev.

This command does the following:

* installs _example-package_ into the node\_modules directory at the project root.
* saves _example-package_ as a project dependency in [package.json](https://github.com/ushahidi/platform-pattern-library/blob/gh-pages/package.json)

After installation you will need to follow the directions for each package in order to set up within your project. These can usually be found in the package's README file.

