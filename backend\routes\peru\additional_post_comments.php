<?php

// Additional post comments
$router->group([
    'middleware' => ['scope:additional-post-comments']
], function () use ($router) {

    // Public access
    $router->post('/additional-post-comments/create', 'AdditionalPostCommentsController@save_post');
    $router->get('/additional-post-comments/get/{post_id:[0-9]+}', 'AdditionalPostCommentsController@get_comments');

    // Restricted access
    $router->group([
        'middleware' => [
            'auth:api'
            ]
    ], function () use ($router) {
        $router->delete('/additional-post-comments/delete/{id:[0-9]+}', 'AdditionalPostCommentsController@delete');
    });
});
