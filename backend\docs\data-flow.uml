@startuml

title <PERSON><PERSON>hidi Data Flow

[Twitter]
[SMS]
[Email]

node "<PERSON><PERSON><PERSON><PERSON>" {
    () Post
    () "REST API" as RESTAPI
    () "Notifications"
    
    [Ushahidi Core]
}

[Mobile App]
[Web Interface]

[Twitter]-right->Post
[Email]-right->Post
[SMS]-right->Post

Post-right->[Ushahidi Core]

RESTAPI<-left->[Ushahidi Core]

[RESTAPI]<-right->[Web Interface]
[RESTAPI]<->[Mobile App]
[Ushahidi Core]-down->Notifications

@enduml