# Localization and Translation

Ushahidi is translated into  34 localizations. Localization refers to a language in a specific location. Each language varies in "completeness" and there are 24 more localizations that still does not have any translations available. Ushahidi deployments with completed languages make it easier for the local regions to communicate with their users. With your help, we'd like to better serve them.

All the software translations for the Ushahidi platform and mobile applications are stored on [Github](https://github.com/ushahidi/Ushahidi-Localizations) and translation is done on [Transifex](https://www.transifex.com/).  Transifex list for Ushahidi is [https://www.transifex.com/organization/ushahidi/dashboard](https://www.transifex.com/organization/ushahidi/dashboard), or for individual projects, see:

| Project Name | Transifex Project |
| :--- | :--- |
| Ushahidi v3 | [https://www.transifex.com/organization/ushahidi/dashboard/ushahidi-v3](https://www.transifex.com/organization/ushahidi/dashboard/ushahidi-v3) |
| Ushahidi v3 mobile | [https://www.transifex.com/ushahidi/ushahidi-v3x-mobile/dashboard/](https://www.transifex.com/ushahidi/ushahidi-v3x-mobile/dashboard/) |

#### Join the Translation teams <a id="LocalizationandTranslation-JointheTranslationteams"></a>

You can join our translation team on Transifex. For more details see on how to contribute and how translation works see [Transifex documentation](https://docs.transifex.com) or their [Youtube-channel](https://www.youtube.com/channel/UCTAijZE5WHedPZ8ZmESh9kg). You can also [reach out to the Ushahidi team](../get-in-touch.md) to get more guidance on your way to start translating!  


