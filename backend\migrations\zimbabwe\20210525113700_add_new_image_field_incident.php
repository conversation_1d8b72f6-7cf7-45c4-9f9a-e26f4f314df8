<?php

// UJEVMS-56 - <PERSON><PERSON><PERSON>, <EMAIL> - 11/02/2020.
// This migration will initialize the form attributes and tags.

use Phinx\Migration\AbstractMigration;

class AddNewImageFieldIncident extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->execute("
      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Photo', 'upload', 'media', 0, 25, NULL, 0, '[]', 1);
    ");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
