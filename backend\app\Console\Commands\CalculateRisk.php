<?php

namespace <PERSON><PERSON><PERSON><PERSON>\App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CalculateRisk extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'calculate:risk';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Queue a CalculateWoredaDailyRisk Job.';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $likelihood_map = array();

        // Get target, perpetrator by woreda for today
        $query_posts_for_today="SELECT posts.id, woreda_context.id AS woreda_context_id, posts.mgmt_lev_3, post_varchar.form_attribute_id, post_varchar.value
                                FROM post_varchar
                                INNER JOIN posts ON post_varchar.post_id=posts.id
                                INNER JOIN woreda_context ON woreda_context.name=posts.mgmt_lev_3
                                WHERE DATE(posts.post_date)=CURDATE()";
        $posts = DB::select($query_posts_for_today);
        $item = new class{};
        $temp_index = -1;
        foreach ($posts as $post) {
            if ($temp_index == -1 || $temp_index == $post->id) {
                $item->woreda = $post->woreda_context_id;
            } else {
                array_push($likelihood_map, $item);
                $item = new class{};
            }
            $temp_index = $post->id;
        }
        array_push($likelihood_map, $item);

        // Get violence by woreda for today
        $query_violence_for_today="SELECT FinalPostTags.id, woreda_context.id AS woreda_context_id, FinalPostTags.parent_id, tags.tag, FinalPostTags.form_attribute_id
                                    FROM (
                                    SELECT PostTags.id, PostTags.mgmt_lev_3, tags.tag, tags.parent_id, PostTags.form_attribute_id
                                    FROM (SELECT posts.id, posts_tags.tag_id, posts.mgmt_lev_3, posts_tags.form_attribute_id FROM posts_tags INNER JOIN posts ON posts_tags.post_id=posts.id WHERE DATE(posts.post_date)=CURDATE() AND posts_tags.form_attribute_id in(5, 7, 8) ) AS PostTags
                                    INNER JOIN tags
                                    ON PostTags.tag_id=tags.id
                                    ) AS FinalPostTags   
                                    INNER JOIN tags
                                    ON tags.id=FinalPostTags.parent_id                                    
                                    INNER JOIN woreda_context
                                    ON FinalPostTags.mgmt_lev_3=woreda_context.name";
        $posts_violence = DB::select($query_violence_for_today);
        foreach ($likelihood_map as $mapItem) {
            $violence_item = null;
            foreach($posts_violence as $post_violence) {
                if (isset($mapItem->woreda)) {

                    if ($mapItem->woreda == $post_violence->woreda_context_id && $post_violence->form_attribute_id === 5) {
                        if($post_violence) {
                            $mapItem->violence = $post_violence->tag;
                        }
                    }
                    if ($mapItem->woreda == $post_violence->woreda_context_id && $post_violence->form_attribute_id === 7) {
                        if($post_violence) {
                            $mapItem->target = $post_violence->tag;
                        }
                    }
                    if ($mapItem->woreda == $post_violence->woreda_context_id && $post_violence->form_attribute_id === 8) {
                        if($post_violence) {
                            $mapItem->perpetrator = $post_violence->tag;
                        }
                    }
                    
                }
                
            }
            
        }
        
        // output result to store
        $woreda_context_map = array();
        foreach ($likelihood_map as $mapItem) {
            if (property_exists($mapItem, 'violence') && property_exists($mapItem, 'perpetrator') && property_exists($mapItem, 'target')) {
                $inclusivityRisk = $this->calculateElementaryRisk("inclusivity", $mapItem->violence, $mapItem->perpetrator, $mapItem->target);
                $transparencyRisk = $this->calculateElementaryRisk("transparency", $mapItem->violence, $mapItem->perpetrator, $mapItem->target);
                $accountabilityRisk = $this->calculateElementaryRisk("accountability", $mapItem->violence, $mapItem->perpetrator, $mapItem->target);
                $competitivenessRisk = $this->calculateElementaryRisk("competitiveness", $mapItem->violence, $mapItem->perpetrator, $mapItem->target);
                $confidenceInInstitutionRisk = $this->calculateElementaryRisk("confidenceintheinstitution", $mapItem->violence, $mapItem->perpetrator, $mapItem->target);
                $compositeRisk = $this->calculateCompositeRisk($inclusivityRisk, $transparencyRisk, $accountabilityRisk, $competitivenessRisk, $confidenceInInstitutionRisk);

                $mapItem->inclusivityRisk=$inclusivityRisk;
                $mapItem->transparencyRisk=$transparencyRisk;
                $mapItem->accountabilityRisk=$accountabilityRisk;
                $mapItem->competitivenessRisk=$competitivenessRisk;
                $mapItem->confidenceInInstitutionRisk=$confidenceInInstitutionRisk;
                $mapItem->compositeRisk=$compositeRisk;

                array_push($woreda_context_map, $mapItem);
            }
        }
        // Get woreda_context_risk entries for previous day
        // $query_woreda_context_risk_yesterday = "SELECT * FROM woreda_context_risk WHERE DATE(woreda_context_risk.date)=DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
       
        // Get woreda_context entries
        $query_woread_context = "SELECT id, name FROM woreda_context";
        $woreda_entries = DB::select($query_woread_context);
        foreach ($woreda_entries as $entry) {
            // $woreda_context_items: will include the entires which has same woreda of the current iteration
            // [woreda, inclusivityRisk, transparencyRisk, accountabilityRisk, competitivenessRisk, confidenceInInstitutionRisk, compositeRisk]
            $woreda_context_items = array();
            foreach($woreda_context_map as $item) {
                if ($item->woreda == $entry->id) {
                    array_push($woreda_context_items, $item);
                }
            }

            $dailyWoredaRisk = 0;
            $inclusivityRisk = 0;
            $transparencyRisk = 0;
            $accountabilityRisk = 0;
            $competitivenessRisk = 0;
            $confidenceInInstitutionRisk = 0;

            if (count($woreda_context_items) > 0) { // if not, then the values will be zero
                $dailyWoredaRisk = $this->max_attribute_in_array($woreda_context_items, 'compositeRisk');
                $inclusivityRisk = $this->max_attribute_in_array($woreda_context_items, 'inclusivityRisk');
                $transparencyRisk = $this->max_attribute_in_array($woreda_context_items, 'transparencyRisk');
                $accountabilityRisk = $this->max_attribute_in_array($woreda_context_items, 'accountabilityRisk');
                $competitivenessRisk = $this->max_attribute_in_array($woreda_context_items, 'competitivenessRisk');
                $confidenceInInstitutionRisk = $this->max_attribute_in_array($woreda_context_items, 'confidenceInInstitutionRisk');
            }

            // get the woreda_context_risk entry for previous day
            $woreda_context_risk_yesterday = null;
            $query_woreda_context_risk_yesterday = "SELECT * FROM woreda_context_risk WHERE woreda_context_id = $entry->id ORDER BY date DESC LIMIT 1";
            $woreda_context_entries_yesterday = DB::select($query_woreda_context_risk_yesterday);
            foreach($woreda_context_entries_yesterday as $entry_old) {
                $woreda_context_risk_yesterday = $entry_old;
                break;
            }

            $yesterdayRisk = 0;
            $twoDaysAgoRisk = 0;
            $threeDaysAgoRisk = 0;
            $fourDaysAgoRisk = 0;
            $fiveDaysAgoRisk = 0;
            $sixDaysAgoRisk = 0;
            $sevenDaysAgoRisk = 0;
            $maxPastRisk = 0;

            if ($woreda_context_risk_yesterday) {
                $yesterdayRisk = $woreda_context_risk_yesterday->dailyWoredaRisk;
                $twoDaysAgoRisk = $woreda_context_risk_yesterday->yesterdayRisk;
                $threeDaysAgoRisk = $woreda_context_risk_yesterday->twoDaysAgoRisk;
                $fourDaysAgoRisk = $woreda_context_risk_yesterday->threeDaysAgoRisk;
                $fiveDaysAgoRisk = $woreda_context_risk_yesterday->fourDaysAgoRisk;
                $sixDaysAgoRisk = $woreda_context_risk_yesterday->fiveDaysAgoRisk;
                $sevenDaysAgoRisk = $woreda_context_risk_yesterday->sixDaysAgoRisk;
            }

            $oneDayAgoExtendedRisk = 0;

            $oneDayAgoExtendedRisk = max($dailyWoredaRisk, $yesterdayRisk, $twoDaysAgoRisk, $threeDaysAgoRisk, $fourDaysAgoRisk, $fiveDaysAgoRisk, $sixDaysAgoRisk, $sevenDaysAgoRisk);


            // Get forcasted risk from offset
            // $query_woread_forecasted = "SELECT max(forecastedRisk) as maxRisk, woreda_context_id
            //     FROM woreda_context_risk JOIN woreda_context ON woreda_context_risk.woreda_context_id = woreda_context.id
            //     WHERE woreda_context_risk.woreda_context_id = $entry->id
            //     GROUP BY woreda_context_id";
            // $woreda_forecasted = DB::select($query_woread_forecasted);
            // $maxVal = $woreda_forecasted[0]->maxRisk;

            // $oneDayAgoExtendedRisk = max($maxVal, $oneDayAgoExtendedRisk);
            // $dailyWoredaRisk  = max($maxVal, $dailyWoredaRisk);

            // $oneDayAgoExtendedRisk = ($oneDayAgoExtendedRisk === 0) ? $maxVal : $oneDayAgoExtendedRisk;
            // $dailyWoredaRisk  = ($dailyWoredaRisk === 0) ? $maxVal : $dailyWoredaRisk;
            // insert risk result
            DB::table('woreda_context_risk')->insert(
                [
                    'date' => date('Y-m-d'),
                    'dailyWoredaRisk' => $dailyWoredaRisk,
                    'yesterdayRisk' => $yesterdayRisk,
                    'twoDaysAgoRisk' => $twoDaysAgoRisk,
                    'threeDaysAgoRisk' => $threeDaysAgoRisk,
                    'fourDaysAgoRisk' => $fourDaysAgoRisk,
                    'fiveDaysAgoRisk' => $fiveDaysAgoRisk,
                    'sixDaysAgoRisk' => $sixDaysAgoRisk,
                    'sevenDaysAgoRisk' => $sevenDaysAgoRisk,
                    'maxPastRisk' => 0,
                    'maxPastZoneRisk' => 0,
                    'maxAllPastRisk' => 0,
                    'inclusivityRisk' => $inclusivityRisk,
                    'transparencyRisk' => $transparencyRisk,
                    'accountabilityRisk' => $accountabilityRisk,
                    'competitivenessRisk' => $competitivenessRisk,
                    'confidenceInInstitutionRisk' => $confidenceInInstitutionRisk,
                    'forecastedRisk' => 0,
                    'woreda_context_id' => $entry->id,
                    'oneDayAgoExtendedRisk' => $oneDayAgoExtendedRisk                    
                ]
            );
        }
    }

    /**
     * Function to calculate the Risk based on likelihood and impact
     *
     * @param $likelihood Float
     * @param $impact Float
     * @return Float
     */
    private function calculateRisk(float $likelihood, float $impact) {
        $maxLikelihood = 5;
        $maxImpact = 5;

        $likelihoodLevel = min($likelihood, $maxLikelihood);
        $impactLevel = min($impact, $maxImpact);

        return min(floor($likelihoodLevel * $impactLevel / $maxLikelihood) + 1, $maxImpact);
    }

    /**
     * Execute the console command.
     *
     * @param $dimension String
     * @param $fieldType String
     * @param $fieldValue String
     * @return Float
     */
    private function calculateElementaryRiskForField(string $dimension, string $fieldType, string $fieldValue) {
        $form_attribute_id = 0;
        $likelihood = 0;
        $impact = 0;
        if ($fieldType == 'violence') {
            $form_attribute_id = 5;
            $likelihood = 1.0; // Other / Unknow value of Violence - default value
            $impact = 1;
        }
        if ($fieldType == 'perpetrator') {
            $form_attribute_id = 8;
            $likelihood = 4.0; // Other / Unknow value of perpetrator - default value
            $impact = 1;
        }
        if ($fieldType == 'target') {
            $form_attribute_id = 7;
            $likelihood = 7.0; // Other / Unknow value of Target - default value
            $impact = 1;
        }


        $query_get_likelihood = "SELECT likelihood FROM likelihood_context WHERE form_attribute_id=".$form_attribute_id." AND NAME='".$fieldValue."'";
        $results_likelihood = DB::select($query_get_likelihood);
        if(count($results_likelihood) > 0) {
            $likelihood = $results_likelihood[0]->likelihood;
        }

        $query_get_impact = "SELECT ".$dimension." FROM form_option_impact WHERE form_attribute_id=".$form_attribute_id." AND NAME='".$fieldValue."'";
        $results_impact = DB::select($query_get_impact);
        if(count($results_impact) > 0) {
            $impact = $results_impact[0]->$dimension;
        }

        return $this->calculateRisk($likelihood, $impact);
    }

    private function calculateElementaryRisk(string $dimension, string $violenceValue, string $perpetratorValue, string $targetValue) {
        $maxImpact = 5;
        $violenceRisk = $this->calculateElementaryRiskForField($dimension, "violence", $violenceValue);
        $perpetratorRisk = $this->calculateElementaryRiskForField($dimension, "perpetrator", $perpetratorValue);
        $targetRisk = $this->calculateElementaryRiskForField($dimension, "violence", $targetValue);

        return min(floor($violenceRisk * $perpetratorRisk * $targetRisk / ($maxImpact * $maxImpact)) + 1, $maxImpact);
    }

    private function calculateCompositeRisk($inclusivityRisk, $transparencyRisk, $accountabilityRisk, $competitivenessRisk, $confidenceInInstitutionRisk) {
        $maxImpact = 5;
        $inclusivityWeight = 1;
        $transparencyWeight = 1;
        $accountabilityWeight = 1;
        $competitivenessWeight = 1;
        $confidenceInInstitutionWeight = 1;
        $weightSum = $inclusivityWeight + $transparencyWeight + $accountabilityWeight + $competitivenessWeight + $confidenceInInstitutionWeight;

        return min(round(($inclusivityWeight * $inclusivityRisk + $transparencyWeight * $transparencyRisk + $accountabilityWeight * $accountabilityRisk + $competitivenessWeight * $competitivenessRisk + $confidenceInInstitutionWeight * $confidenceInInstitutionRisk)  / $weightSum), $maxImpact);
    }

    private function max_attribute_in_array($array, $prop) {
        return max(array_map(function($o) use($prop) {
            return $o->$prop;
        },
            $array));
    }
}
