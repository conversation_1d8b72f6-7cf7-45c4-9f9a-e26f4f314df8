<?php

use Phinx\Migration\AbstractMigration;

class UpdateContactUsEmailInTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
        // Contact Us Email
        $zimbabwe_contact_us_email = '<EMAIL>';
        $liberia_contact_us_email = '<EMAIL>';
        $peru_contact_us_email = '<EMAIL>';
        $malawi_contact_us_email = '<EMAIL>';

        // Contact Us Admin
        $zimbabwe_contact_us_admin = 'Admin';
        $liberia_contact_us_admin = 'Admin';
        $peru_contact_us_admin = 'Admin';
        $malawi_contact_us_admin = 'Admin';

        $this->execute("DELETE from tenant_options WHERE tenant_id = 1 AND tenant_key = 'contact_us_email'");
        $this->execute("DELETE from tenant_options WHERE tenant_id = 1 AND tenant_key = 'contact_us_admin'");
       $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'contact_us_email', '$malawi_contact_us_email')");
       $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'contact_us_admin', '$malawi_contact_us_admin')");

    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}