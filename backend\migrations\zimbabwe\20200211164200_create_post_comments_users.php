<?php

// UJEVMS-62 - <PERSON><PERSON><PERSON>, <EMAIL> - 14/02/2020.

use Phinx\Migration\AbstractMigration;

class CreatePostCommentsUsers extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('post_comments_users')
      ->addColumn('post_comment_id', 'integer')
      ->addColumn('user_id', 'integer')
      ->addColumn('created', 'integer', ['default' => 0])
      ->addForeignKey('post_comment_id', 'post_comments', 'id', [
        'delete' => 'CASCADE',
        'update' => 'RESTRICT',
      ])
      ->addForeignKey('user_id', 'users', 'id', [
        'delete' => 'CASCADE',
        'update' => 'RESTRICT',
      ])
      ->create();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
