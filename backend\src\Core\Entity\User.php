<?php

/**
 * Ushahidi Platform User Entity
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Entity;

use Ushahidi\Core\StaticEntity;

class User extends StaticEntity
{
    protected $id;
    protected $active;
    protected $email;
    protected $realname;
    protected $password;
    protected $logins;
    protected $failed_attempts;
    protected $last_login;
    protected $last_attempt;
    protected $created;
    protected $updated;
    protected $role;
    protected $language;
    protected $contacts;
    protected $mgmt_lev_1;
    protected $mgmt_lev_2;
    protected $mgmt_lev_3;
    protected $organization;
    protected $position;

    // DataTransformer
    protected function getDefinition()
    {
        return [
            'id'              => 'int',
            'active'          => 'boolean',
            'email'           => '*email',
            'realname'        => 'string',
            'password'        => 'string',
            'logins'          => 'int',
            'failed_attempts' => 'int',
            'last_login'      => 'int',
            'last_attempt'    => 'int',
            'created'         => 'int',
            'updated'         => 'int',
            'role'            => 'string',
            'language'        => 'string',
            'contacts'        => 'array',
            'permissions'     => 'array',
            'mgmt_lev_1'      => 'string',
            'mgmt_lev_2'      => 'string',
            'mgmt_lev_3'      => 'string',
            'organization'    => 'string',
            'position'        => 'string'
        ];
    }

    // Entity
    public function getResource()
    {
        return 'users';
    }
}
