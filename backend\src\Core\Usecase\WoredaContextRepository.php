<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Core\Usecase;

use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\SearchUsecase;

class WoredaContextRepository extends SearchUsecase
{

  // - VerifyParentLoaded for checking that the parent exists
  // Usecase
    public function interact()
    {
        // Fetch an empty entity...
        $entity = $this->getEntity();
       // ... and get the search filters for this entity
        $search = $this->getSearch();  
        // // ... get the total count for the search
        $data = $this->repo->getData($search);
        $results['count'] = sizeof($data);
        $results['results'] = $data;
 
        return $results;
    }
}
