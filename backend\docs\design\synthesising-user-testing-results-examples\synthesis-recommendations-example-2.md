# Synthesis recommendations example 2

### Research report

There's a lot of wicked problems to solve around legitimacy and saftey as well as accessibility for those most vulnerable people in society. In general though, the UX of the app is functioning well and the biggest problems are around people and how we all function differently and have vastly different ways of doing things.

### Designers synthesis

Key findings:

_Information clarity & repetition_ - Many users found the information hard to scan, read and forgot after getting a couple of actions deep. We need to not be afraid of repetition.  
Buttons, icons, text and hit areas were too small for even young able-bodied/sighted folks. We need to increase hit areas, sizes all round.

_Safety and balance_ - All users talked about safety at some point and everyone had a different level of process they did to ensure their own safety. Most users were also concerned about the safety of others in the community. The most distressed users were those with more vulnerabilities e.g. couldn't read, severe disability, elderly, isolated etc.

_Reporting and spam_ - There was a lot of talk about how and when reporting a user was appropriate. While everyone wanted it to be there some were quicker to report than others under different circumstances.

_Dispatcher name_ - Everyone said this was some kind of delivery or courier service. Not community related.

_Photography_ - Users loved the illustrations. Photography was neutrally received. Many users expected images to be uploaded by the people requesting food/clothes. Stock image won't fly here. Map imagery as satellite was not positively received. Graphical maps much better. 30 mins radius unanimously loved. Google maps easy to understand.

_UX_ - The flow of screens all mostly made sense and were familiar.

_Chatting is key_ - Every user said they would utilise the chat function heavily. For checking that someone is legit to re-arranging times etc. The only time users said they wouldn't chat is if they'd helped the person before or already knew them

_Reviews_ - Reviews and star ratings was mixed. People struggled with how you could 'rate' a person but they found it useful the more informative it was. Most people were concerned with reviews and stars being equitable so if you receive a lot of help and can't offer anything you also get ratings/reviews.

_Onboarding prompt_ - People were at best perplexed and at worse confused by the 'adding items' first. Some mentioned about what if they had no spare clothes or food and how that made them feel bad to wanting to explore the app first to wanting to straight away offer to help without adding food/clothes. Some people we very put off by being 'told' what to do first.

_Profile_ - Profile's need bios, it helps with legitimacy and connection. Payment info was confusing and needs to not be 1st and have more explanation.

_Timings_ - Seeing text that says 'Pick up in 20 mins' made most users nervous about not being able to accommodate that time. Most users wanted to be able to arrange a time.

_Adding stuff_ - People wanted the option of free text fields to describe rather than being confined to radio buttons.

_Food list_ - Most of the users though the food list was random and not what was up for offer already. This needs to be clearer.

_Quotes_ - Unanimously loved. Everyone loves Barack Obama. Literary quotes suggested.

_Would they use it_ - They all see this as useful. Even though there are current ways of doing this via other apps and groups they all want something a bit more finessed.

### Product Manager's synthesis

Key findings:

* user attributes: who is/isn’t our target audience for the pilot? we need a clearer answer for this
* task timing and scheduling
* get to good stuff asap, jump right in with minimal profile registration setup whatnot
* is this person helping me or am i helping them? &lt;— language is currently unclear
* warmth helps alot - pictures and short quotes
* qualifying by understanding all the details of the task
* credibility & trust
* “the intermediary step”—&gt;matching the loop isn’t the same as closing it
* keeping people safe and feeling safe

## Suggested work before next round of research/prototype amendss

### _Quick wins_

*  Remove report user from chat and keep in a users profile only.
*  Add back buttons &lt;- into the profile page and any pages where they are absent
*  Payment info not top of list in profile
*  Remove any onboarding pop-ups.
*  Better contrast of icons on map
*  Chat is primary action
*  Add in a closing quote mark on quote graphic
*  test a different piece of photography

### _Re-working - longer term work_

*  Text and buttons bigger
*  Plain, natural language for any text
*  Repeat requests, bigger headings and more explanation
*  Add in a bio on profile
*  investigate something other than stars for rating
*  We need a review template for people to add in a minimum amount of detail
*  Food list deeply confusing via request. Needs clarity that these items are ready and available
*  User needs to be able to add in custom images
*  Users really interested in other ways of helping but appreciate the 3 there already
*  Need a way to politely decline an offer of help and introduce element of choice
*  We need to deeply investigate safety concerns
*  Conversational tone in a familiar language \(depends on input from Monica & Angie\)
*  Scheduling and timings for tasks \(Time slots? Leave out? dropboxes?\)
*  Pickup location coordination \(chat? Options? Third party point?\)
*  Clarify and repeat perspective in conversational language: who is helping who with what again?
*  Simplify to two options: Give Help and Get Help -&gt; Then simplify to two more options: Items and Skills\(or Actions\)
*  Type-ahead label generation, or free text \(this is a new way of organizing info-- something we haven’t really tried yet\)

## Via Voting the top issues to investigate are:

* Simplify to two options: Give Help and Get Help - &gt; Then simplify to two more options: Items and Skills\(or Actions\)
* We need to deeply investigate safety concerns

**Then:**

* Repeat requests, bigger headings and more explanation
* Food list deeply confusing via request. Needs clarity that these items are ready and available
* User needs to be able to add in custom images
* Pickup location coordination \(chat? Options? Third party point?\)

The rest of the actions are lowest priority based of voting but achievable via changes to the prototype but we need to quickly assess what we can achieve with the live app.

