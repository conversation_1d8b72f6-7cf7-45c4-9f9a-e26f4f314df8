<?php

use Phinx\Migration\AbstractMigration;

class CreateContactUsTable extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {
        $this->table('contact_us')
            ->addColumn('name', 'string', ['null' => false])
            ->addColumn('email', 'string', ['null' => true])
            ->addColumn('phone_number', 'string', ['null' => true])
            ->addColumn('subject', 'string', ['null' => false])
            ->addColumn('message', 'string', ['null' => false])
            ->addColumn('status', 'integer', ['default' => 0])
            ->addColumn('created', 'integer', ['default' => 0])
            ->addColumn('updated', 'integer', ['default' => 0])
            ->create();
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        $this->dropTable('contact_us');
    }
}
