# Synthesis example 3

Reasons for joining community initiative: Mentions a community champion that another tester had mentioned. The one that set up the face book group and is well known in the community.  
They first helped with a young pregnant mother who was about to be homeless and rallied support around this individual. Set up a whatsapp group for practical and emotional support for this person. Led that group. Likes seeing how other people help. likes seeing the kind of help that's needing. Is a community 'connector' and reposts regularly. The home less mother now helps out \(homeless cafe pop-up\) proves that the 'helped' can become the 'helpers'. Other requests seen like needing a house painted before a landlord inspection or will be kicked out. Is generally well connected throughout community.

Com Champ = Moved here from capitol city. Looking to meet people. Retired. Set up comm group to connect. CIC. Had technical help setting up via Facebook. Used to meet once a month in local community space. Looking for funding. Wants to hand it off. Matching people, charities and those that need help.

Dispatcher = Courier company

Sign in = Standard. No explanation what is it so if they were showing someone it wouldn't be up-front. Or maybe they forgot the app store description or was recommended it. Needs to be summarised in loading/landing pg. Participant mentioned a 'tagline' being needed  
Wonders why there isn't a welcome/thank you screen.

Landing page = Specific food + Clothes "What if you don't have either of those? I could still give time but I don't have spare clothes or food right now"  
Clothing and gets the map page.

Laurence request & Map =  
Symbols, T-shirt, hand, knife & fork. 3 year old was asked to describe what knife and fork would be and they said 'people need food' :\)  
Easy to review. Likes having a map rather than a list. List is typical and boring "I always get lists". Map is different.  
How far in time is good. Other apps give distance in km and participant always has to 'figure it out'. Clicking on symbols and getting people pop up very familiar. Wants people to be very local and cannot travel more than 30 mins. Can drive but has kid so car seat etc. is a bother.  
Describes wanting to search for type of help needed e.g. car broken down and need lift so they would want to take up the offer of the closest person offering help.  
Investigate Laurence's request. Info is fine but could be better and clearer

Requesting food = General list is good and likes the pictures. Food list is random. Talks about the street whatsapp group where they ask for things like eggs/sugar/milk as this list isn't 'useful' stuff and is odd. "Why would I need applesauce?"  
Understands after explanation that this is what people have put up for offer.  
Selects rice.  
Time dependance is tricky. Wants it picked up quick and participant seems anxious. Would have indicated for 'urgent' if they needed it that bad. Wants to be able to arrange via chat a wider time window.

Directions = Easy makes sense and where they would expect. Wants to have an option whwere someone could 'leave it outside the door' or 'Come to my office and ask for \[name' wants a bit more info on the place they will be going so they know what to expect.

Quotes = "aww" cool, positive, nice while I wait. Who it's by is important to say. But not cheesy. Shorter quote would be nice. Makes them feel like part of something. Would like to see earlier in the app. Says the rest of the app is transactional and cold but this is warm.

Profile = Standard but a bit impersonal. No context. Needs bio or lines. Little things that you can pick up on. Look on their facebook to match the story.

Chat = Familiar, easy to use.

Report user = Different times you'd use this - early if the person is inappropriate or spam \(report as spam\), middle if the conversation is odd or pushed boundaries \(flag to admin\) and real inx - unsafe, let you down without getting in touch \(flag and bad review/report\) There would be a block and reject process but concerned about their responsibility to the wider community to report person.  
Also doesn't want to penalise someone right away because what if they had an accident or something legit?  
Wants to know if there are staff that the report won't just 'fall into a black hole' want to hear back around course of action.

Safety = Talks about a situation where someone \(woman\) wanted to pick up a fridge from a man. Realised after sorting the time/day that they were going to an unknown persons hokme as a solo woman. Then arranged someone to go with her. Talked about some exchanges happening 'on doorstep' or 'Left in garden' There is a way to stay safe through where you agree to meet and how.  
Face & name, chat and the 'vibes' are mostly used to figure out if someone is safe. How active they have been on the app. If they have been on the app for a long time but not 'done' anything.  
Concerned about how new members would gain credibility in the community.

Reviews = They can be 'flakey'. If they can't do anything and receive help isn't it unbalanced?

General =  
All UX and screen flows are easy and quickly navigated. A savvy digital person.  
Talks about a situation when someone's boiler broke down and there were a plethora of help types coming in e.g. money, support, plumbing \(in a days time\), professional boiler person tonight. The person had to make the judgement of what help is best in what time/location. \(A good case for the ML element\) Wants a way to filter through many offers and also a way to 'turn off' request and no longer get replies/

Talks about Ollio and giving away 'opened food' and reducing waste.

Talks about another situation/app that says you can offer your tools/kit like a wheelbarrow etc. You don't want to give it away but you can lend in to someone.

Competitors mentioned = Facebook, Ollio, Whatsapp, Helpful peeps, Ask nextdoor,

