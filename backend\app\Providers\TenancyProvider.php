<?php
 
namespace <PERSON><PERSON><PERSON><PERSON>\App\Providers;
 
use <PERSON><PERSON><PERSON>di\App\Models\Tenant;
use Illuminate\Support\ServiceProvider;
 
class TenancyProvider extends ServiceProvider{
    
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
      $this->configureRequests();
    }
 
    /**
     *
     */
    public function configureRequests()
    {
        if (! $this->app->runningInConsole()) {

            $host = @$_SERVER['HTTP_HOST'];
         
            //$host = substr($host, 0, strpos($host, ".")); 
            
            $tenant = Tenant::where('backend_url',$host)->first();
            if(!is_null($tenant)){
                $tenant->configure()->use();
            }
        }
    }
}
