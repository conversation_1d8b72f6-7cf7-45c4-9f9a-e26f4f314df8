<?php

// UEH-35 - <PERSON><PERSON><PERSON>, yagh<PERSON>@unicc.org - 20/10/2022.

use Phinx\Migration\AbstractMigration;

class AddPublishedFlagToPostsTabel extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */

  /**
   * Migrate Up.p
   */
  public function up()
  {
    $this->table('posts')
      ->addColumn('published', 'boolean', ['default' => 0])
      ->update();
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
