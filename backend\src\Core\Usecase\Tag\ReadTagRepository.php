<?php

/**
 * Ushahidi Platform Admin Read Tag Repository
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Usecase\Tag;

interface ReadTagRepository
{
    /**
     * @param  int $id
     * @return Ushahidi\Core\Entity\Tag
     */
    public function get($id);
}
