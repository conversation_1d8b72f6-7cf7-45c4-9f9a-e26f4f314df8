<?php

use Phinx\Migration\AbstractMigration;

class AddIncidentReportType extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {

// /home/<USER>/Projects/UNDP/ireport-malawi-prod/backend/migrations/malawi/20150904021011_add_default_post_type.php

        $connection = $this->getAdapter()->getConnection();
        $adapter = $this->getAdapter();


    $this->execute("
      start transaction;

      -- Setting form attributes.
      set FOREIGN_KEY_CHECKS = 0;
      truncate table forms;
      set FOREIGN_KEY_CHECKS = 1;

     commit;
      ");

        $count = $this->fetchRow("SELECT COUNT(*) AS form_count FROM forms");

        // If we don't yet have any post types..
        if ($count['form_count'] == 0) {
            // .. create a default post type    
            // Note: Timestamp here *may* be in a different timezone, since
            // we're using mysql time, not PHP time.
            $this->execute(
                "INSERT INTO forms (name, description, type, created)
                VALUES ( 'Incident Report', ' EWER Incidents Report (Early Warning and Early Response System', 'incident', UNIX_TIMESTAMP(NOW()) )"
            );

            $connection->prepare(
                "INSERT INTO form_stages (label, priority, required, form_id)
                VALUES ( 'Structure', 0, 1, :form_id )"
            )->execute([ ':form_id' => $connection->lastInsertId() ]);

            $this->execute(
                "INSERT INTO forms (name, description, type, created)
                VALUES ( 'E-day Observer Checklist', 'Elections Day Observer Checklist', 'risk', UNIX_TIMESTAMP(NOW()) )"
            );

            $connection->prepare(
                "INSERT INTO form_stages (label, priority, required, form_id)
                VALUES ( 'Structure', 0, 1, :form_id )"
            )->execute([ ':form_id' => $connection->lastInsertId() ]);

        }
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // Noop - too risky to delete a post type
    }
}
