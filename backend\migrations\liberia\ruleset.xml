<?xml version="1.0"?>
<ruleset name="<PERSON><PERSON><PERSON><PERSON>">
    <description>Ushahidi migrations use PSR2 with exclusions</description>

    <!-- Specs use PSR2 with an exclusion of method names -->
    <rule ref="PSR2">
        <exclude name="PSR1.Classes.ClassDeclaration.MissingNamespace"/>
    </rule>

    <rule ref="Generic.PHP.CharacterBeforePHPOpeningTag" />
    <rule ref="Generic.Arrays.DisallowLongArraySyntax" />
</ruleset>
