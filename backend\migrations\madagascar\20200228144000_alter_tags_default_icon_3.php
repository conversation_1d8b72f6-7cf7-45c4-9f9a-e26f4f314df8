<?php

use Phinx\Migration\AbstractMigration;

class AlterTagsDefaultIcon3 extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->execute("
      update tags set icon = 'fa fa-map-marker';
      ");
    $this->table('tags')
      ->changeColumn('icon', 'string', [
        'limit' => 35,
        'default' => 'fa fa-map-marker',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}

