<?php

namespace <PERSON><PERSON><PERSON><PERSON>\App\Passport;

use Illuminate\Contracts\Auth\Guard;

use Exception;
use Firebase\JWT\JWT;
use <PERSON><PERSON>\Passport\Token;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Passport\Passport;
use Illuminate\Container\Container;
use <PERSON><PERSON>\Passport\TransientToken;
use Lara<PERSON>\Passport\TokenRepository;
use Lara<PERSON>\Passport\ClientRepository as LaravelPassportClientRepository;
use League\OAuth2\Server\ResourceServer;
//use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Contracts\Encryption\Encrypter;
use Illuminate\Contracts\Debug\ExceptionHandler;
use League\OAuth2\Server\Exception\OAuthServerException;
use Symfony\Bridge\PsrHttpMessage\Factory\DiactorosFactory;

use Ushahidi\Core\Entity\UserRepository as UshahidiUserRepository;

class TokenGuard //implements Guard
{
    /**
     * The resource server instance.
     *
     * @var ResourceServer
     */
    protected $server;

    /**
     * The user repo implementation.
     *
     * @var UserRepository
     */
    protected $repo;

    /**
     * The token repository instance.
     *
     * @var TokenRepository
     */
    protected $tokens;

    /**
     * The client repository instance.
     *
     * @var ClientRepository
     */
    protected $clients;

    /**
     * The encrypter implementation.
     *
     * @var Encrypter
     */
    protected $encrypter;

    /**
     * Create a new token guard instance.
     *
     * @param  ResourceServer  $server
     * @param  UserProvider  $provider
     * @param  TokenRepository  $tokens
     * @param  ClientRepository  $clients
     * @param  Encrypter  $encrypter
     * @return void
     */
    public function __construct(
        ResourceServer $server,
        //UserProvider $provider,
        UshahidiUserRepository $repo,
        TokenRepository $tokens,
        LaravelPassportClientRepository $clients,
        Encrypter $encrypter
    ) {
        $this->server = $server;
        $this->tokens = $tokens;
        $this->clients = $clients;
        $this->repo = $repo;
        $this->encrypter = $encrypter;
    }

    /**
     * Get the user for the incoming request.
     *
     * @param  Request  $request
     * @return mixed
     */
    public function user(Request $request)
    {
        if ($request->bearerToken()) {
            return $this->authenticateViaBearerToken($request);
        } elseif ($request->cookie(Passport::cookie())) {
            return $this->authenticateViaCookie($request);
        }
    }

    /**
     * Authenticate the incoming request via the Bearer token.
     *
     * @param  Request  $request
     * @return mixed
     */
    protected function authenticateViaBearerToken($request)
    {
        // First, we will convert the Symfony request to a PSR-7 implementation which will
        // be compatible with the base OAuth2 library. The Symfony bridge can perform a
        // conversion for us to a Zend Diactoros implementation of the PSR-7 request.
        $psr = (new DiactorosFactory)->createRequest($request);

        try {
            $psr = $this->server->validateAuthenticatedRequest($psr);

            // If the access token is valid we will retrieve the user according to the user ID
            // associated with the token. We will use the provider implementation which may
            // be used to retrieve users from Eloquent. Next, we'll be ready to continue.
            $user = $this->repo->get(
                $psr->getAttribute('oauth_user_id')
            );

            if (! $user) {
                return;
            }

            // Next, we will assign a token instance to this user which the developers may use
            // to determine if the token has a given scope, etc. This will be useful during
            // authorization such as within the developer's Laravel model policy classes.
            $token = $this->tokens->find(
                $psr->getAttribute('oauth_access_token_id')
            );

            $clientId = $psr->getAttribute('oauth_client_id');

            // Finally, we will verify if the client that issued this token is still valid and
            // its tokens may still be used. If not, we will bail out since we don't want a
            // user to be able to send access tokens for deleted or revoked applications.
            if ($this->clients->revoked($clientId)) {
                return;
            }

            $user = new \Ushahidi\App\Auth\GenericUser($user->asArray());

            return $token ? $user->withAccessToken($token) : null;
        } catch (OAuthServerException $e) {
            // Log the error
            Container::getInstance()->make(
                ExceptionHandler::class
            )->report($e);

            // Adding WWW-Authenticate ourselves because thephpleague/oauth2-server sucks
            // https://github.com/thephpleague/oauth2-server/issues/738
            abort(
                $e->getHttpStatusCode(),
                $e->getMessage(),
                $e->getHttpHeaders() + ['WWW-Authenticate' => 'Bearer realm="OAuth"']
            );

            return;
        }
    }

    /**
     * Authenticate the incoming request via the token cookie.
     *
     * @param  Request  $request
     * @return mixed
     */
    protected function authenticateViaCookie($request)
    {
        // If we need to retrieve the token from the cookie, it'll be encrypted so we must
        // first decrypt the cookie and then attempt to find the token value within the
        // database. If we can't decrypt the value we'll bail out with a null return.
        try {
            $token = $this->decodeJwtTokenCookie($request);
        } catch (Exception $e) {
            return;
        }

        // We will compare the CSRF token in the decoded API token against the CSRF header
        // sent with the request. If the two don't match then this request is sent from
        // a valid source and we won't authenticate the request for further handling.
        if (! $this->validCsrf($token, $request) ||
            time() >= $token['expiry']) {
            return;
        }

        // If this user exists, we will return this user and attach a "transient" token to
        // the user model. The transient token assumes it has all scopes since the user
        // is physically logged into the application via the application's interface.
        if ($user = $this->repo->get($token['sub'])) {
            $user = new \Ushahidi\App\Auth\GenericUser($user->asArray());
            return $user->withAccessToken(new TransientToken);
        }
    }

    /**
     * Decode and decrypt the JWT token cookie.
     *
     * @param  Request  $request
     * @return array
     */
    protected function decodeJwtTokenCookie($request)
    {
        return (array) JWT::decode(
            $this->encrypter->decrypt($request->cookie(Passport::cookie())),
            $this->encrypter->getKey(),
            ['HS256']
        );
    }

    /**
     * Determine if the CSRF / header are valid and match.
     *
     * @param  array  $token
     * @param  Request  $request
     * @return bool
     */
    protected function validCsrf($token, $request)
    {
        return isset($token['csrf']) && hash_equals(
            $token['csrf'],
            (string) $request->header('X-CSRF-TOKEN')
        );
    }
}
