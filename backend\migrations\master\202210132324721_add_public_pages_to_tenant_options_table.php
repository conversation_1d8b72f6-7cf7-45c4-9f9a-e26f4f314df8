<?php

use Phinx\Migration\AbstractMigration;

class AddPublicPagesToTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {


        $this->execute("DELETE from tenant_options WHERE tenant_id = 1 AND tenant_key = 'private_pages'");
        $this->execute("DELETE from tenant_options WHERE tenant_id = 2 AND tenant_key = 'private_pages'");
        $this->execute("DELETE from tenant_options WHERE tenant_id = 3 AND tenant_key = 'private_pages'");
        
        // Public Pages
        $zimbabwe_public_pages = array(
            'public-pages' => [],
            );
        $zimbabwe_public_pages = json_encode($zimbabwe_public_pages);

        $liberia_public_pages = array(
            'public-pages' => [
                'contact-us',
                'get-alerts',
                'about-us',
                'map/lern',
                'posts/lern'
            ],
            );
        $liberia_public_pages = json_encode($liberia_public_pages);

        $madagascar_public_pages = array(
            'public-pages' => [
                'contact-us',
                'get-alerts',
                'about-us',
                'map/lern',
                'posts/lern'
            ],
            );
        $madagascar_public_pages = json_encode($madagascar_public_pages);

        $peru_public_pages = array(
            'public-pages' => [
                'contact-us',
                'get-alerts',
                'about-us',
                'map/mace',
                'posts/mace'
            ],
            );
        $peru_public_pages = json_encode($peru_public_pages);
       
     //   $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'public_pages', '$zimbabwe_public_pages')");
     //   $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'public_pages', '$liberia_public_pages')");
//        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'public_pages', '$madagascar_public_pages')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'public_pages', '$peru_public_pages')");

    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}