<?php

namespace Us<PERSON>hidi\App\Http\Controllers\API;

use Ushahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use Ushahidi\App\Models\TenantOption;
use Illuminate\Support\Facades\Mail;
use DB;

/**
 * Ushahidi API Contacts Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class ContactUsController extends RESTController{

    protected function getResource(){
        return '';
    }

    public function save(Request $request){

        $this->validate($request,[
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'phone_number' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:255',
        ]);
                   
        $id = DB::table('contact_us')->insertGetId([
            'name' => $request->name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'subject' => $request->subject,
            'message' => $request->message,
        ]);
      
        $contact_us_email = TenantOption::where('tenant_id',app('tenant')->id)->where('tenant_key','contact_us_email')->first()->tenant_value;
        $contact_us_admin = TenantOption::where('tenant_id',app('tenant')->id)->where('tenant_key','contact_us_admin')->first()->tenant_value;
        $sender_name = TenantOption::where('tenant_id',app('tenant')->id)->where('tenant_key','sender_name')->first()->tenant_value;
        $sender_email = TenantOption::where('tenant_id',app('tenant')->id)->where('tenant_key','sender_email')->first()->tenant_value;
        $frontend_url = 'https://'.app('tenant')->frontend_url;
        $country = app('tenant')->unique_name;
        $multiple_contactUs_emails = explode(',', $contact_us_email);
        
        try {
            foreach($multiple_contactUs_emails as $contact_email) {
                Mail::send('emails.contact_us', [
                    'name' => $request->name,
                    'email' => $request->email,
                    'phone_number' => $request->phone_number,
                    'subject' => $request->subject,
                    'the_message' => $request->message,
                    'frontend_url' => $frontend_url,
                    'country' => $country,
                    'admin_name' => $contact_us_admin,
                ], function($message) use ($contact_us_email, $sender_name, $sender_email, $contact_email) {
                    $message->from($sender_email,$sender_name)->to(trim($contact_email))->subject('Contact Us');
                });
            }
        }catch(\Exception $e){
            return response()->json(["success" => false,'message' => $e->getMessage()],500,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }
        
        if($id){
            return response()->json(["success" => true],200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }else{
            return response()->json(["success" => false],422,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }
    }
}