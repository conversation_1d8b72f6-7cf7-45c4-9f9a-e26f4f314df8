<?php

/**
 * <PERSON><PERSON><PERSON><PERSON> Config Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package   Ushahidi\Application
 * @copyright 2014 Ushahidi
 * @license   https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use Ushahidi\Core\Entity\Config;
use Us<PERSON>hidi\Core\Entity\User;
use Ushahidi\Core\Entity\UserRepository;
use Ushahidi\Core\Entity\Permission;
use Ushahidi\Core\Tool\Authorizer;
use <PERSON><PERSON>hidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Tool\Permissions\AclTrait;

// The `HXLAuthorizer` class is responsible for access checks on `HXL` Entities
class HXLAuthorizer implements Authorizer
{
    // The access checks are run under the context of a specific user
    use UserContext;

    // It uses `AdminAccess` to check if the user has admin access
    use AdminAccess;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;

    // Check that the user has the necessary permissions
    // if roles are available for this deployment.
    use AclTrait;


    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {
        // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/08/2020.
        // These checks are run within the `User` context.
        // $this->acl->hasPermission($user, Permission::DATA_IMPORT_EXPORT) ||
        // $this->acl->hasPermission($user, Permission::LEGACY_DATA_IMPORT) ||
        // $this->acl->hasPermission($user, Permission::MANAGE_SETTINGS)
        $user = $this->getUser();
        if ($this->isUserAdmin($user) ||
            // $this->acl->hasPermission($user, Permission::MANAGE_POSTS) ||
            $this->acl->hasPermission($user, [Permission::ExportIncidentListConflictividadElectoral,Permission::ExportIncidentListOperacionesElectorales]) ||
            $this->acl->hasPermission($user, Permission::ACCESS_AND_MODIFY_PARAMETERS_SETTINGS)
        ) {
            return true;
        }
        // If no other access checks succeed, we default to denying access
        return false;
    }
}
