<?php

use Phinx\Migration\AbstractMigration;

class UpdateFormTypes extends AbstractMigration
{

    public function up()
    {
        $pdo = $this->getAdapter()->getConnection();

        $update = $pdo->prepare("
            UPDATE forms SET type = 'incident' WHERE name = 'Basic Post';
            UPDATE forms SET type = 'risk' WHERE name = 'Risk Report';
        ");

        $update->execute();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
