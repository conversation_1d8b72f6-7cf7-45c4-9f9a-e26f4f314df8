---
description: All tasks are filed as issues in github.
---

# Track and submit issues in Github

### Track issues in Github

We log all our issues concerning development and documentation for the Ushahidi Platform in the [Platform-repository](https://github.com/ushahidi/platform/issues).  Issues marked with [Community-task](https://github.com/ushahidi/platform/labels/Community%20Task) are issues that are up for grabs for any community-member.

### Submit issues in GitHub

To submit issues in GitHub for the Ushahidi Platform, please go to [https://github.com/ushahidi/platform/issues](https://github.com/ushahidi/platform/issues) and click on the "New Issue" button, _or_ follow [this link](https://github.com/ushahidi/platform/issues/new/choose).

[![New Issue button in GitHub](https://user-images.githubusercontent.com/2434401/62314495-8a844200-b469-11e9-8439-27ed03ea43c4.png)](https://user-images.githubusercontent.com/2434401/62314495-8a844200-b469-11e9-8439-27ed03ea43c4.png)

You will see a list of options for which type of issue you'd like to submit.  
[![Screen Shot 2019-08-01 at 14 36 16](https://user-images.githubusercontent.com/2434401/62314594-c3241b80-b469-11e9-8b62-115ea30cc150.png)](https://user-images.githubusercontent.com/2434401/62314594-c3241b80-b469-11e9-8b62-115ea30cc150.png)  
The options:

* **Bug report:** If you want to report something that is broken or not working as intended, please file a Bug Report. A good bug report is descriptive and reproducible.
* **Feature/enhancement request:** Suggest an idea for a new feature or a small enhancement to existing features. This can be something as big as "Add a new way to import data into the Ushahidi Platform" or as small as "Change the font size of the login button to make it easier to use".
* **Epic level spec**: this is only used when you want to create a very large request that encompasses multiple tickets and is likely to require a lot of work. It is primarily used by the Ushahidi team to group work together and plan for it, so you can ignore it for now.

For each of the options, when you click it you will be taken to a screen where you can fill in answers to our questions to submit a new issue. Please take the time to fill in all the required fields, as this will increase the chances that your issue will be understood quickly by both staff and community members, and it will also help us triage it correctly.  
[![Screen Shot 2019-08-01 at 14 44 10](https://user-images.githubusercontent.com/2434401/62315024-d7b4e380-b46a-11e9-9829-b737d9d89217.png)](https://user-images.githubusercontent.com/2434401/62315024-d7b4e380-b46a-11e9-9829-b737d9d89217.png)

**Important:** _Do not add tags to issues unless you are a maintainer._ A maintainer will tag issues during triage, and add any staff or community members that need to be aware of the issue immediately.

Once you have filled all the requirements for the issue to be submitted, you can click "Submit new Issue" and your issue will be created. **This triggers a notification to one Ushahidi staff who will review it as soon as possible.**  
[![Screen Shot 2019-08-01 at 14 47 50](https://user-images.githubusercontent.com/2434401/62315262-5b6ed000-b46b-11e9-993f-6ad654d8e138.png)](https://user-images.githubusercontent.com/2434401/62315262-5b6ed000-b46b-11e9-993f-6ad654d8e138.png)

Thank you for reading our guide to submitting a new issue to the Ushahidi Platform. We look forward to hearing from you!

