<?php

use Phinx\Migration\AbstractMigration;

class AddSenderNameAndEmailToTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
        // Sender Name and Email

        // $peru_sender_name = 'iReport Peru';
        // $peru_sender_email = '<EMAIL>';

        $malawi_sender_name = 'iReport Malawi';
        $malawi_sender_email = '<EMAIL>';

        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'sender_name', '$peru_sender_name')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'sender_email', '$peru_sender_email')");

        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'sender_name', '$malawi_sender_name')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'sender_email', '$malawi_sender_email')");
    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}