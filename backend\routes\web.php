<?php

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell Lumen the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

/**
 * API version number
 */
$apiVersion = '3';
$apiBase = 'api/v' . $apiVersion;
$router->get('/', "API\IndexController@index");
$router->get('/tenant', "API\TenantController@index");
$router->post('/tenant', "API\TenantController@getTenant");
$router->post('/mace-data-import', "API\LernDataImportController@store");
$router->get('/update-mace-data-location', "API\LernDataImportController@update_location");
$router->get('/update-mace-data-status', "API\LernDataImportController@update_status");
$router->get($apiBase, "API\IndexController@index");

$router->get('/clear-cache', function(){
    \Cache::flush();
});
$router->group([
    'prefix' => $apiBase,
    'namespace' => 'API'
], function () use ($router) {

    //dynamically import all routes in the routes folder and sub folders
    // <EMAIL>
    $files = preg_grep('/^([^.])/', scandir(dirname(__FILE__)));
    foreach ($files as $filename) {
        $dir = dirname(__FILE__) . '/' . $filename;
        if (is_dir($dir)) {
            foreach (scandir($dir) as $dir) {
                $path = dirname(__FILE__) . '/' .$filename.'/'.$dir;
                if (is_file($path)) {
                    require $path;
                }
            }
        }
    }
});

// Migration
$router->get('/migrate', 'MigrateController@migrate');
