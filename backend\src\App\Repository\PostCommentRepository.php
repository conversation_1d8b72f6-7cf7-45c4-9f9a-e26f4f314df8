<?php

// UJEVMS-52 - SCAL<PERSON> Luca, <EMAIL> - 27/01/2020.

namespace <PERSON><PERSON>hidi\App\Repository;

use Ohanzee\DB;
use <PERSON><PERSON><PERSON>di\Core\Entity;
use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\PostComment;
use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\PostCommentRepository as PostCommentRepositoryContract;
use <PERSON><PERSON><PERSON>di\Core\Entity\UserRepository;
use <PERSON><PERSON><PERSON>di\Core\SearchData;
use Ushahidi\App\Multisite\OhanzeeResolver;
use League\Event\ListenerInterface;
use Ushahidi\Core\Traits\Event;

class PostCommentRepository extends OhanzeeRepository implements PostCommentRepositoryContract
{
  // Use Event trait to trigger events
  use Event;
  protected $user_repo = [];
  protected $comment_post_id = 0;

  public function __construct(
        OhanzeeResolver $resolver,
        UserRepository $user_repo
    ) {
      parent::__construct($resolver);
      $this->user_repo = $user_repo;
    }

  public function getEntity(array $data = null)
  {
    if (!empty($data['id'])) {
      $userInfo = $data['user_id'];
      if (isset($data['user_id'])) {
        $userInfo =  $this->user_repo->getById($data['user_id']);
      }
      $data += [
        'users'           => $this->getUsers($data),
        'linked_reports'  => $this->getPosts($data),        
        'comment_user'    => !is_null($userInfo)?$userInfo->realname:''
      ];
    }

    return new PostComment($data);
  }

  protected function getTable()
  {
    return 'post_comments';
  }

  public function getSearchFields()
  {
    return [
      'post_id',
      'type'
    ];
  }

  protected function setSearchConditions(SearchData $search)
  {
    $query = $this->search_query;
    $this->comment_post_id = $search->post_id;
    // UJEVMS-68 - Fulpagare Pramod, <EMAIL> - 02/04/2020.
    $query
        ->join('post_comments_posts', 'LEFT')->on('post_comments.id', '=', 'post_comments_posts.post_comment_id')
        ->where('post_comments.post_id', '=', $search->post_id)
        ->where('post_comments.type', '=', $search->type)
        ->or_where('post_comments_posts.post_id', '=', $search->post_id)
        ->where('post_comments.type', '=', $search->type);
    return $query;
  }

  public function create(Entity $entity)
  {
    $record = array_filter($entity->asArray());
    $record['created'] = time();

    $users = [];
    if (isset($record['users'])) {
      $users = $record['users'];
      unset($record['users']);
    }
    // UJEVMS-68 - Fulpagare Pramod, <EMAIL> - 02/04/2020.
    $linked_reports = [];
    if (isset($record['linked_reports'])) {
      $linked_reports = $record['linked_reports'];
      unset($record['linked_reports']);
    }

    $id = $this->executeInsert($this->removeNullValues($record));
    $record['id'] = $id;
    $record['users'] = $users;
    $this->setUsers($record);
    // UJEVMS-68 - Fulpagare Pramod, <EMAIL> - 02/04/2020.
    $record['linked_reports'] = $linked_reports;
    $this->setCommentPosts($record);
     // UJEVMS-65 - Fulpagare Pramod, <EMAIL> - 10/04/2020.
    $entity->setState(['id' => $id]);
    $this->emit($this->event, $entity, 'create');
    return $id;
  }

  public function update(Entity $entity)
  {
    $record = $entity->getChanged();
    $record['updated'] = time();

    $this->setUsers($entity->asArray());
    unset($record['users']);
    // UJEVMS-68 - Fulpagare Pramod, <EMAIL> - 02/04/2020.
    $this->setCommentPosts($entity->asArray());
    unset($record['linked_reports']);

    $reponse = $this->executeUpdate([
      'id' => $entity->id
    ], $record);

    // UJEVMS-65 - Fulpagare Pramod, <EMAIL> - 10/04/2020.
    $this->emit($this->event, $entity, 'update');
    return $reponse;
  }
  // UJEVMS-68 - Fulpagare Pramod, <EMAIL> - 02/04/2020.
  protected function getPosts($record)
  {
      if ($this->comment_post_id !== $record['post_id']) {
        return DB::select('posts.id', [DB::expr('title'), 'name'])
        ->from('posts')
        ->where('id', '=', $record['post_id'])
        ->execute($this->db())->as_array();
      } else {
        return DB::select('posts.id', [DB::expr('title'), 'name'])
        ->from('post_comments_posts')
        ->join('posts')
        ->on('posts.id', '=', 'post_comments_posts.post_id')
        ->where('post_comments_posts.post_comment_id', '=', $record['id'])
        ->execute($this->db())->as_array();
      }
  }

  protected function getUsers($record)
  {
    return DB::select('users.id', 'users.realname')
      ->from('post_comments_users')
      ->join('users')
      ->on('users.id', '=', 'post_comments_users.user_id')
      ->where('post_comments_users.post_comment_id', '=', $record['id'])
      ->execute($this->db())
      ->as_array();
  }

  protected function setUsers($record)
  {
    DB::delete('post_comments_users')
      ->where('post_comment_id', '=', $record['id'])
      ->execute($this->db());

    if (!empty($record['users'])) {
      $created = time();
      $values = array_map(function ($user) use ($record, $created) {
        return [$record['id'], $user['id'], $created];
      }, $record['users']);
      $insertQuery = DB::insert('post_comments_users', ['post_comment_id', 'user_id', 'created']);
      foreach ($values as $value) {
        $insertQuery->values($value);
      }
      $insertQuery->execute($this->db());
    }
  }

  protected function setCommentPosts($record)
  {
    DB::delete('post_comments_posts')
      ->where('post_comment_id', '=', $record['id'])
      ->execute($this->db());

    if (!empty($record['linked_reports'])) {
      $created = time();
      $values = array_map(function ($post) use ($record, $created) {
        return [$record['id'], $post['id'], $created];
      }, $record['linked_reports']);
      $insertQuery = DB::insert('post_comments_posts', ['post_comment_id', 'post_id', 'created']);
      foreach ($values as $value) {
        $insertQuery->values($value);
      }
      $insertQuery->execute($this->db());
    }
  }
}
