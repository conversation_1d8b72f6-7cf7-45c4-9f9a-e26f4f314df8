<?php

/**
 * <PERSON><PERSON><PERSON><PERSON> PostSet Listener
 *
 * Listens for new posts that are added to a set
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Listener;

use League\Event\AbstractListener;
use League\Event\EventInterface;

use Ushahidi\Core\Entity\WebhookJobRepository;
use <PERSON>hahidi\Core\Entity\WebhookRepository;

class PostListener extends AbstractListener
{
    protected $repo;

    protected $webhook_repo;

    public function setRepo(WebhookJobRepository $repo)
    {
        $this->repo = $repo;
    }

    public function setWebhookRepo(WebhookRepository $webhook_repo)
    {
        $this->webhook_repo = $webhook_repo;
    }

    public function handle(EventInterface $event, $post_id = null, $event_type = null)
    {
        $state = [
            'post_id' => $post_id,
            'event_type' => $event_type
        ];
        $entity = $this->repo->getEntity();
        $entity->setState($state);
        $this->repo->create($entity);
    }
}
