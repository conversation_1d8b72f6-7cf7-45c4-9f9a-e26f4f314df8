<?php

namespace <PERSON><PERSON>hidi\App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ForecastRiskModelCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'calculate:forecast';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Queue a Calculate ForecastRiskModel Job.';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $file_path1 = realpath(__DIR__ . '/../jsons/risk-1.json');
        $file_path2 = realpath(__DIR__ . '/../jsons/risk-2.json');
        $file_path3 = realpath(__DIR__ . '/../jsons/risk-3.json');
        $file_path4 = realpath(__DIR__ . '/../jsons/risk-4.json');
        $file_path5 = realpath(__DIR__ . '/../jsons/risk-5.json');

        // Import risk model json data
        print_r('Import risk model json data'. PHP_EOL);
        $json1 = json_decode(file_get_contents($file_path1), true);
        $json2 = json_decode(file_get_contents($file_path2), true);
        $json3 = json_decode(file_get_contents($file_path3), true);
        $json4 = json_decode(file_get_contents($file_path4), true);
        $json5 = json_decode(file_get_contents($file_path5), true);

        // Get 'woreda_context_risk' entries for today
        print_r('Get woreda_context_risk entries for today...'. PHP_EOL);
        $query1="SELECT woreda_context_risk.*, woreda_context.*, woreda_context_risk.id as woreda_context_risk_id
                 FROM woreda_context_risk 
                 INNER JOIN woreda_context ON woreda_context_risk.woreda_context_id=woreda_context.id 
                 WHERE STR_TO_DATE(woreda_context_risk.date, '%Y-%m-%d')=CURDATE()";
        $entries=DB::select($query1);

        $count = 0;
        foreach ($entries as $entry) {
            $expForecastedRisks = array();
            print_r('---Compare entry '.($count + 1).', woreda_context_risk.id is '.$entry->woreda_context_risk_id.'---'.PHP_EOL);

            print_r('1=>risk-1.json'. PHP_EOL);
            foreach ($json1['AggregatedRules'][0]['Rules'] as $rule) {
                $conditions = $rule['Conditions'];
                $is_matched = true;
                foreach ($conditions as $condition) {
                    $conditionPieces = explode(":", $condition);
                    $index = $conditionPieces[0];
                    if(isset($entry->$index) && (int)$entry->$index !== (int)$conditionPieces[1]) {
                        $is_matched = false;
                        break;
                    }
                }

                if($is_matched) {
                    array_push($expForecastedRisks, 1);
                }
            }

            print_r('2=>risk-2.json'. PHP_EOL);
            foreach ($json2['AggregatedRules'][0]['Rules'] as $rule) {
                $conditions = $rule['Conditions'];
                $is_matched = true;
                foreach ($conditions as $condition) {
                    $conditionPieces = explode(":", $condition);
                    $index = $conditionPieces[0];
                    if(isset($entry->$index) && (int)$entry->$index !== (int)$conditionPieces[1]) {
                        $is_matched = false;
                        break;
                    }
                }

                if($is_matched) {
                    array_push($expForecastedRisks, 2);
                }
            }

            print_r('3=>risk-3.json'. PHP_EOL);
            foreach ($json3['AggregatedRules'][0]['Rules'] as $rule) {
                $conditions = $rule['Conditions'];
                $is_matched = true;
                foreach ($conditions as $condition) {
                    $conditionPieces = explode(":", $condition);
                    $index = $conditionPieces[0];
                    if(isset($entry->$index) && (int)$entry->$index !== (int)$conditionPieces[1]) {
                        $is_matched = false;
                        break;
                    }
                }

                if($is_matched) {
                    array_push($expForecastedRisks, 3);
                }
            }

            print_r('4=>risk-4.json'. PHP_EOL);
            foreach ($json4['AggregatedRules'][0]['Rules'] as $rule) {
                $conditions = $rule['Conditions'];
                $is_matched = true;
                foreach ($conditions as $condition) {
                    $conditionPieces = explode(":", $condition);
                    $index = $conditionPieces[0];
                    if(isset($entry->$index) && (int)$entry->$index !== (int)$conditionPieces[1]) {
                        $is_matched = false;
                        break;
                    }
                }

                if($is_matched) {
                    array_push($expForecastedRisks, 4);
                }
            }

            print_r('5=>risk-5.json'. PHP_EOL);
            foreach ($json5['AggregatedRules'][0]['Rules'] as $rule) {
                $conditions = $rule['Conditions'];
                $is_matched = true;
                foreach ($conditions as $condition) {
                    $conditionPieces = explode(":", $condition);
                    $index = $conditionPieces[0];
                    if((int)$entry->$index !== (int)$conditionPieces[1]) {
                        $is_matched = false;
                        break;
                    }
                }

                if($is_matched) {
                    array_push($expForecastedRisks, 5);
                }
            }

            $count += 1;
            // Results
            print_r($expForecastedRisks);
            $forecastedRisk = 0;
            if(count($expForecastedRisks) > 0) {
                $forecastedRisk = max($expForecastedRisks);
            }

            if($forecastedRisk != 0) {
                // Update woreda_context_risk table on the column forecastedRisk
                $update_query = "UPDATE woreda_context_risk SET forecastedRisk=".$forecastedRisk." WHERE id=".$entry->woreda_context_risk_id;
                DB::select($update_query);
            }
        }
    }
}
