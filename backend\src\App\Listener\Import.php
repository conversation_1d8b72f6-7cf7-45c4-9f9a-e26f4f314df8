<?php
namespace Us<PERSON>hi<PERSON>\App\Listener;

/**
 * <PERSON><PERSON><PERSON><PERSON> PostSet Listener
 *
 * Listens for new posts that are added to a set
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

use League\Event\AbstractListener;
use League\Event\EventInterface;
use Ushahidi\Core\Entity\Set;
use Ushahidi\App\Facades\Features;

class Import extends AbstractListener
{

    protected $transformer;
    protected $repo;
    /**
     * [transform description]
     * @return [type] [description]
     */
    protected function transform($record)
    {
        $record = $this->transformer->interact($record);

        return $this->repo->getEntity()->setState($record);
    }

    public function handle(
        EventInterface $event,
        $records = null,
        $csv = null,
        $transformer = null,
        $repo = null,
        $importUsecase = null,
        $userId = null
    ) {
        $this->transformer = $transformer;
        $this->repo = $repo;
        $processed = $errors = 0;


        $collection_id = service('repository.set')->create(new Set([
           'name' => $csv->filename,
           'description' => 'Import',
           'view' => 'data',
           'featured' => false
        ]));

        $created_entities = [];
        foreach ($records as $index => $record) {
            // ... transform record
            $entity = $this->transform($record);

            $entity->setState(['import_post_id' => $record['Post ID']]);
            $entity->setState(['user_id' => $userId]);

            // Ensure that under review is correctly mapped to draft
            if (strcasecmp($entity->status, 'under review')== 0) {
                $entity->setState(['status' => 'draft']);
            }

            if (!Features::isEnabled('csv-speedup')) {
                $importUsecase->verify($entity);
            }
            // ... persist the new entity
            try {
                $id = $this->repo->create($entity);
            } catch (Exception $e) {
                $errors++;
            }
            service('repository.set')->addPostToSet($collection_id, $id);

            $processed++;
        }

        $new_status = 'SUCCESS';
        $csv->setState([
            'status' => $new_status,
            'collection_id' => $collection_id,
            'processed' => $processed,
            'errors' => $errors
        ]);

        service('repository.csv')->update($csv);
    }
}
