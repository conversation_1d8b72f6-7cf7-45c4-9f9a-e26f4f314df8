<?php

// UJEVMS-52 - <PERSON><PERSON><PERSON>, <EMAIL> - 27/01/2020.

namespace <PERSON><PERSON>hidi\Core\Usecase\Post;

use <PERSON><PERSON><PERSON>di\Core\Usecase\SearchUsecase;

class SearchPostComment extends SearchUsecase
{
  use VerifyParentLoaded;

  protected function getPagingFields()
  {
    return [
      'orderby' => 'id',
      'order'   => 'asc',
      'limit'   => null,
      'offset'  => 0
    ];
  }
}
