# Welcome to the hackathon!

❤️We are so excited that you are here joining our hackathon!

The hackathon runs between the 2nd of May to the 7th of May and you are welcome to participate whenever you can during that time. The chatter and support will happen on our gitter-channel: [https://gitter.im/ushahidi/installfest-2019](https://gitter.im/ushahidi/installfest-2019). We have tried to schedule our time so there will be someone online at least between 7am CET to 11 pm CET, sometimes longer than that. If you will participate outside these hours, don't worry, we will answer your questions as soon as we get online!

The main goal of the hackathon is to get started with working with the Ushahidi Platform and install it in the environment of your choice. 

You can find the different installation-instructions for different environments here: [https://app.gitbook.com/@ushahidi/s/platform-developer-documentation/getting-started/setup\_alternatives](https://app.gitbook.com/@ushahidi/s/platform-developer-documentation/getting-started/setup_alternatives)



If you are more into css and html, you are also welcome to try out our Pattern Library. This is where we create all css for the Platform and where you can change appearance and colour-scheme etc. You can find the instructions for setting up and working with the Pattern Library here: 

[https://app.gitbook.com/@ushahidi/s/platform-developer-documentation/changing-ui-styles-introduction-to-the-pattern-library](https://app.gitbook.com/@ushahidi/s/platform-developer-documentation/changing-ui-styles-introduction-to-the-pattern-library)

Once you have installed the Platform successfully and played around with it \(you find our user-manual here: \), you are welcome to grab one of our community task issues and contribute. You find the available issues here:

[https://github.com/ushahidi/platform/labels/Community%20Task](https://github.com/ushahidi/platform/labels/Community%20Task)

We have marked them with different labels to make it easier to understand which code needs to be touched:

*  API for issues connected to the backend \(platform repo\): [https://github.com/ushahidi/platform/issues?q=is%3Aopen+label%3AAPI+label%3A%22Community+Task%22](https://github.com/ushahidi/platform/issues?q=is%3Aopen+label%3AAPI+label%3A%22Community+Task%22)
*  CLIENT for the front-end code \(platform-client-repo\): [https://github.com/ushahidi/platform/issues?q=is%3Aopen+label%3A%22Community+Task%22+label%3AClient](https://github.com/ushahidi/platform/issues?q=is%3Aopen+label%3A%22Community+Task%22+label%3AClient)
*  Both API and CLIENT where code in both repos probably needs to be touched: [https://github.com/ushahidi/platform/issues?q=is%3Aopen+label%3A%22Community+Task%22+label%3AClient+label%3AAPI](https://github.com/ushahidi/platform/issues?q=is%3Aopen+label%3A%22Community+Task%22+label%3AClient+label%3AAPI)





