<?php

/**
 * <PERSON><PERSON>hidi Platform Post Export Use Case
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Usecase\Post;

use <PERSON>hahidi\App\Repository\ExportJobRepository;
use Ushahidi\App\Repository\Form\AttributeRepository;
use Ushahidi\App\Repository\HXL\HXLFormAttributeHXLAttributeTagRepository;
use <PERSON>hahidi\App\Repository\Post\ExportRepository;
use Ushahidi\Core\Tool\AuthorizerTrait;
use Ushahidi\Core\Tool\FormatterTrait;
use Ushahidi\Core\Usecase;
use Ushahidi\Core\SearchData;
use Ushahidi\Core\Traits\UserContext;
use Us<PERSON><PERSON>di\Core\Entity\ExportBatch;
use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\ExportBatchRepository;
use <PERSON><PERSON>hidi\Core\Usecase\Concerns\FilterRecords;
use Log;
use DB;

class Export implements Usecase
{
    use UserContext;

    // Uses several traits to assign tools. Each of these traits provides a
    // setter method for the tool. For example, the AuthorizerTrait provides
    // a `setAuthorizer` method which only accepts `Authorizer` instances.
    use AuthorizerTrait,
        FormatterTrait;

    // - FilterRecords for setting search parameters
    use FilterRecords;

    private $postExportRepository;
    private $exportJobRepository;
    private $formAttributeRepository;
    private $hxlFromAttributeHxlAttributeTagRepo;

    /**
     * @var SearchRepository
     */
    protected $repo;

    /**
     * Inject a repository to create/update ExportBatches
     *
     * @param  ExportBatchRepository $repo
     * @return $this
     */
    public function setRepository(ExportBatchRepository $repo)
    {
        $this->repo = $repo;
        return $this;
    }

    /**
     * @var SearchData
     */
    protected $search;

    // - VerifyParentLoaded for checking that the parent exists
    use VerifyParentLoaded;

    public function setExportJobRepository(ExportJobRepository $repo)
    {
        $this->exportJobRepository = $repo;//service('repository.export_job');
    }

    public function setHXLFromAttributeHxlAttributeTagRepo(HXLFormAttributeHXLAttributeTagRepository $repo)
    {
        $this->hxlFromAttributeHxlAttributeTagRepo = $repo;//service('repository.form_attribute_hxl_attribute_tag');
    }

    public function setFormAttributeRepository(AttributeRepository $repo)
    {
        $this->formAttributeRepository = $repo; //service('repository.form_attribute');
    }

    public function setPostExportRepository(ExportRepository $repo)
    {
        $this->postExportRepository = $repo; //service('repository.posts_export');
    }
    
    /**
     * @return array|mixed|\Ushahidi\Core\Array
     */
    public function interact()
    {
        Log::debug('EXPORTER: on interact: ');

        // Load the export job
        $job = $this->exportJobRepository->get($this->getIdentifier('job_id'));
        // load the user from the job into the 'session'
        $this->session->setUser($job->user_id);
        Log::debug('EXPORTER: on interact - user id: ' . $job->user_id);

        // Create new export batch status=pending
        $batchEntity = $this->repo->getEntity()->setState([
            'export_job_id' => $job->id,
            'batch_number' => $this->getIdentifier('batch_number'),
            'status' => ExportBatch::STATUS_PENDING,
            'has_headers' => $this->getFilter('add_header', false)
        ]);
        $batchId = $this->repo->create($batchEntity);

        try {
            // verify the user can export posts
            $this->verifyAuth($job, 'export');
            // merge filters from the controller/cli call with the job's saved filters
            $data = $this->constructSearchData($job);
            if($job->entity_type == 'post') {
            $this->postExportRepository->setSearchParams($data);
            // get the form attributes for the export
            $attributes     = $this->formAttributeRepository->getExportAttributes($data->include_attributes, $job->filters);
            $keyAttributes  = $this->getAttributesWithKeys($attributes);

            /**
             * get the search results based on filters
             * and retrieve the metadata for each of the posts
             **/
            $posts = $this->postExportRepository->getSearchResults();
            foreach ($posts as $idx => $post) {
                // Retrieved Attribute Labels for Entity's values
                $post = $this->postExportRepository->retrieveMetaData($post->asArray(), $keyAttributes);
                $posts[$idx] = $post;
            }
            Log::debug('EXPORTER: on interact Count posts: ' . count($posts));

            /**
             * update the header attributes
             * in the job table so we know which headers to
             * use in other chunks of the export
             */
            if($job->lang == 'en') {
                $this->saveHeaderRow($job, $attributes);
            } 
            if($job->lang == 'am') {
                foreach ($attributes as $key => $value) {                    
                    $attributes[$key]['label'] = isset($value['label_am'])?$value['label_am']:$value['label'];
                }
                $this->saveHeaderRow($job, $attributes);
            }

            /**
             * set 'add header' in the formatter
             * so it knows how to return the results
             * for the csv (with or without a header row)
             */
            $this->formatter->setAddHeader($this->filters['add_header']);
            // handle hxl
            $hxl_rows = $this->formatter->generateHXLRows(
                $this->formatter->createHeading($attributes),
                $this->getHxlRows($job)
            );
            $this->saveHXLHeaderRow($job, $hxl_rows);
            $this->formatter->setHxlHeading($hxl_rows);
            $formatter = $this->formatter;
            Log::debug('EXPORTER: Count posts: ' . count($posts));

            /**
             * KeyAttributes is sent instead of the header row because it contains
             * the attributes with the corresponding features (type, priority) that
             * we need for manipulating the data
             */
            $file = $formatter($posts, $job, $keyAttributes);
            } else if($job->entity_type == 'user') {
                $users = DB::table('users')
                            ->select('users.*','users.id as uid','contacts.contact','contacts.type','roles.short_name as short_name')
                            ->leftjoin('contacts',function($join){
                                $join->on('contacts.user_id', '=', 'users.id')
                                ->where('contacts.type', '=', 'phone');
                            })
                            ->leftjoin('roles', 'roles.name', '=', 'users.role')
                            ->get()
                            ->groupBy('user_id');
                $posts = [];

                foreach($users as $user){
                    foreach($user  as $key => $userData){
                        if(isset($posts[$userData->uid])){
                            $posts[$userData->uid]['TELEPHONE2'] = '"' . $userData->contact. '"';
                        } else {
                            $posts[$userData->uid] = [
                                'username'      => $userData->realname,
                                'email'         => $userData->email,
                                'short_name'    => $userData->short_name,
                                'mgmt_lev_1'    => $userData->mgmt_lev_1
                            ];
                            if($userData->contact != '' && isset($userData->contact)) {
                                $posts[$userData->uid]['TELEPHONE'] = '"' . $userData->contact. '"';
                            }
                        } 
                    }
                }
                $attributes = $posts;
                Log::debug('EXPORTER: on interact Count users: ' . count($posts));
                
                $this->saveHeaderRow($job, $attributes);
                $this->formatter->setAddHeader($this->filters['add_header']);
                $formatter = $this->formatter;
                $file = $formatter->generateUserPDF($posts, $job, $attributes);
            }
        } catch (\Exception $e) {
            // Mark batch as failed
            $batchEntity = $this->repo->get($batchId);
            $batchEntity->setState([                
                'status' => ExportBatch::STATUS_FAILED
            ]);
            $this->repo->update($batchEntity);
            // And rethrow the error
            throw $e;
        }

        // Update export batch status=done
        // Include filename, post count, header row etc
        $batchEntity = $this->repo->get($batchId);
        $batchEntity->setState([
            'status'    => ExportBatch::STATUS_COMPLETED,
            'filename'  => $file->file,
            'rows'      => count($posts),
        ]);
        $this->repo->update($batchEntity);

        return [
            'filename'  => $file->file,
            'id'        => $batchId,
            'jobId'     => $job->id,
            'rows'      => $batchEntity->rows,
            'status'    => $batchEntity->status
        ];
    }
    
    /**
     * @return array|mixed|\Ushahidi\Core\Array
     */
    public function _interact()
    {
        Log::debug('EXPORTER: on interact: ');

        // Load the export job
        $job = $this->exportJobRepository->get($this->getIdentifier('job_id'));
        // load the user from the job into the 'session'
        $this->session->setUser($job->user_id);
        Log::debug('EXPORTER: on interact - user id: ' . $job->user_id);

        // Create new export batch status=pending
        $batchEntity = $this->repo->getEntity()->setState([
            'export_job_id' => $job->id,
            'batch_number' => $this->getIdentifier('batch_number'),
            'status' => ExportBatch::STATUS_PENDING,
            'has_headers' => $this->getFilter('add_header', false)
        ]);
        $batchId = $this->repo->create($batchEntity);

        try {
            // verify the user can export posts
            $this->verifyAuth($job, 'export');
            // merge filters from the controller/cli call with the job's saved filters
            $data = $this->constructSearchData($job);
            if($job->entity_type == 'post') {
            $this->postExportRepository->setSearchParams($data);

            // get the form attributes for the export
            $attributes     = $this->formAttributeRepository->getExportAttributes($data->include_attributes);
            $keyAttributes  = $this->getAttributesWithKeys($attributes);

            /**
             * get the search results based on filters
             * and retrieve the metadata for each of the posts
             **/
            $posts = $this->postExportRepository->getSearchResults();
            foreach ($posts as $idx => $post) {
                // Retrieved Attribute Labels for Entity's values
                $post = $this->postExportRepository->retrieveMetaData($post->asArray(), $keyAttributes);
                $posts[$idx] = $post;
            }
            Log::debug('EXPORTER: on interact Count posts: ' . count($posts));

            /**
             * update the header attributes
             * in the job table so we know which headers to
             * use in other chunks of the export
             */
            if($job->lang == 'en') {
                $this->saveHeaderRow($job, $attributes);
            } 
            if($job->lang == 'am') {
                foreach ($attributes as $key => $value) {                    
                    $attributes[$key]['label'] = isset($value['label_am'])?$value['label_am']:$value['label'];
                }
                $this->saveHeaderRow($job, $attributes);
            }

            /**
             * set 'add header' in the formatter
             * so it knows how to return the results
             * for the csv (with or without a header row)
             */
            $this->formatter->setAddHeader($this->filters['add_header']);
            // handle hxl
            $hxl_rows = $this->formatter->generateHXLRows(
                $this->formatter->createHeading($attributes),
                $this->getHxlRows($job)
            );
            $this->saveHXLHeaderRow($job, $hxl_rows);
            $this->formatter->setHxlHeading($hxl_rows);
            $formatter = $this->formatter;
            Log::debug('EXPORTER: Count posts: ' . count($posts));

            /**
             * KeyAttributes is sent instead of the header row because it contains
             * the attributes with the corresponding features (type, priority) that
             * we need for manipulating the data
             */
            $file = $formatter($posts, $job, $keyAttributes);
            } else if($job->entity_type == 'user') {
                $users = DB::table('users')
                    ->select('users.*','users.id as uid','contacts.contact','contacts.type','roles.short_name as short_name','users_ex.region as region')
                    ->leftjoin('contacts',function($join){
                        $join->on('contacts.user_id', '=', 'users.id')
                        ->where('contacts.type', '=', 'phone');
                    })
                    ->leftjoin('roles', 'roles.name', '=', 'users.role')
                    ->leftjoin('users_ex','users_ex.user_id','=','users.id')
                    ->where('users.role','!=','sysadmin')
                    ->get()
                    ->groupBy('user_id');
                $posts = [];
                foreach($users as $user){
                    foreach($user  as $key => $userData){
                        if(isset($posts[$userData->uid])){
                            $posts[$userData->uid]['TELEPHONE2'] = '"' . $userData->contact. '"';
                        } else {
                            $posts[$userData->uid] = [
                                'username'      => $userData->realname,
                                'email'         => $userData->email,
                                'short_name'    => $userData->short_name,
                                'region'        => $userData->region
                            ];
                            if ($userData->contact != '' && isset($userData->contact)) {
                                $posts[$userData->uid]['TELEPHONE'] = '"' . $userData->contact. '"';
                            }
                        } 
                    }
                }
                $attributes = $posts;
                Log::debug('EXPORTER: on interact Count users: ' . count($posts));

                
                $this->saveHeaderRow($job, $attributes);
                $this->formatter->setAddHeader($this->filters['add_header']);
                $formatter = $this->formatter;
                $file = $formatter->generateUserPDF($posts, $job, $attributes);
            }
        } catch (\Exception $e) {
            // Mark batch as failed
            $batchEntity = $this->repo->get($batchId);
            $batchEntity->setState([                
                'status' => ExportBatch::STATUS_FAILED
            ]);
            $this->repo->update($batchEntity);
            // And rethrow the error
            throw $e;
        }

        // Update export batch status=done
        // Include filename, post count, header row etc
        $batchEntity = $this->repo->get($batchId);
        $batchEntity->setState([
            'status' => ExportBatch::STATUS_COMPLETED,
            'filename' => $file->file,
            'rows' => count($posts),
        ]);
        $this->repo->update($batchEntity);

        return [
            'filename' => $file->file,
            'id' => $batchId,
            'jobId' => $job->id,
            'rows' => $batchEntity->rows,
            'status' => $batchEntity->status
        ];
    }

    /**
     * @param $job
     * If the include_hxl flag is true, generate the heading row and include
     * the hxl heading in the csv
     */
    private function getHxlRows($job)
    {
        $hxl = [];
        if ($job->include_hxl === true) {
            $hxl = $this->hxlFromAttributeHxlAttributeTagRepo->getHxlWithFormAttributes($job);
        }
        return $hxl;
    }

    /**
     * @param $job
     * @param $attributes
     */
    private function saveHeaderRow($job, $attributes)
    {
        if (empty($job->header_row)) {
            $job->setState(['header_row' => $attributes]);
            $this->exportJobRepository->update($job);
        }
    }

    /**
     * @param $job
     * @param $hxl heading row
     */
    private function saveHXLHeaderRow($job, $hxl)
    {
        if (empty($job->hxl_heading_row)) {
            $job->setState(['hxl_heading_row' => $hxl]);
            $this->exportJobRepository->update($job);
        }
    }

    /**
     * @param $filters
     * @param null $job_filters
     * @return array
     * Construct a filters object
     */
    public function constructFilters($filters, $job_filters = null)
    {

        // Set the baseline filter parameters
        $filters = [
            'limit' => $filters['limit'],
            'offset' => $filters['offset'],
        ];
        $filters = [];
        // Merge the export job filters with the base filters
        if ($job_filters) {
            $filters = array_merge($filters, $job_filters);
        }
        array_splice($filters, 2, 1);
        return $filters;
    }

    /**
     * @param $job
     * @param $filters
     * @return mixed
     * Construct a Search Data object to hold the search info
     */
    public function constructSearchData($job)
    {
        $form_ids_by_attributes = $this->formAttributeRepository->getFormsByAttributes($job->fields);
        $filters = $this->constructFilters($this->filters, $job->filters);

        if (!empty($form_ids_by_attributes)) {
            $filters['form'] = array_unique(
                array_merge(
                    isset($filters['form']) ? $filters['form'] : [],
                    array_map(
                        function ($item) {
                            return intval($item);
                        },
                        $form_ids_by_attributes
                    )
                )
            );
        }

        $data = $this->search;

        // Set the fields that should be included if set
        if ($job->fields) {
            $data->include_attributes = $job->fields;
        }

        // set the filters that should be used
        foreach ($filters as $key => $filter) {
            $data->$key = $filter;
        }
        $data->limit = ($this->getFilter('limit')) ? $this->getFilter('limit') : $data->limit;
        $data->offset = ($this->getFilter('offset')) ? $this->getFilter('offset') : $data->offset;
        return $data;
    }

    /**
     * Get the attributes we will use for the CSV header
     * and create an assoc array like
     * {'attribute_key': attribute, '2ndkey' : attribute}
     * that we can use for the heading formatting
     * @param $attributes
     * @return array
     */
    private function getAttributesWithKeys($attributes)
    {
        /**
         * Get the attributes we will use for the CSV header
         * and create an assoc array like
         * {'attribute_key': attribute, '2ndkey' : attribute}
         * that we can use for the heading formatting
         */
        $keyAttributes = [];
        foreach ($attributes as $key => $item) {
            $keyAttributes[$item['key']] = $item;
        }
        return $keyAttributes;
    }

    /**
     * Will this usecase write any data?
     *
     * @return Boolean
     */
    public function isWrite()
    {
        return false;
    }

    /**
     * Will this usecase search for data?
     *
     * @return Boolean
     */
    public function isSearch()
    {
        return true;
    }

    /**
     * @param SearchData $search
     */
    public function setData(SearchData $search)
    {
        $this->search = $search;
    }
}
