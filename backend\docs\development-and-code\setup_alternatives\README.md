# Setup Guides

## Deploying to production environments

Deploy-ready versions from the platform-release repository, which contains production-ready tags of the platform. Ready to use with docker.

{% page-ref page="installing-for-production-environments.md" %}

## Platform API \(Backend\) Install Guides

### Installing with Vagrant

This is the preferred option for local development. The <PERSON>hahidi dev team uses this setup for most of their development work.

{% page-ref page="vagrant-setup.md" %}

### Installing with XAMPP

This option is only recommended for local development, in particular for situations where it's not feasible or desirable to setup vagrant or docker. It has been tested and developed with the help of members of our community.

{% page-ref page="xampp.md" %}

### Installing from the platform-release repository

Deploy-ready versions from the platform-release repository, which contains production-ready tags of the platform. Ready to use with docker.

{% page-ref page="platform\_release\_install.md" %}

## Platform Client \(Frontend\) install guides

{% page-ref page="setting-up-the-platform-client.md" %}

## Platform Pattern Library \(HTML/CSS\) install guide

If you plan to change the css for the Platform Client, follow this guide.

{% page-ref page="setting-up-the-pattern-library-for-development.md" %}



