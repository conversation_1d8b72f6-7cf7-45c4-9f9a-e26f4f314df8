<?php

/**
 * <PERSON><PERSON>hidi Platform Receive Message Use Case
 *
 * - Takes a received SMS message
 * - finds/creates the associated contact
 * - Stores the raw message
 * - Creates a new un-typed post from the message
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Usecase\Message;

use DB;
use Ushahidi\Core\Entity;
use Ushahidi\Core\Entity\Message;
use Ushahidi\Core\Entity\Contact;
use Ushahidi\Core\Entity\Post;
use Ushahidi\Core\SearchData;
use \Ushahidi\App\Repository\FormRepository;
use \Ushahidi\App\Repository\TagRepository;
use Ushahidi\Core\Tool\Validator;
use Ushahidi\Core\Usecase\CreateUsecase;
use Ushahidi\Core\Usecase\CreateRepository;
use <PERSON><PERSON><PERSON>di\Core\Traits\Events\DispatchesEvents;
use <PERSON><PERSON><PERSON>di\Core\Usecase\Concerns\FilterRecords;

use <PERSON><PERSON>hidi\Core\Exception\ValidatorException;

class ReceiveMessage extends CreateUsecase
{
    use DispatchesEvents;
    use FilterRecords;

    /**
     * @var CreateRepository
     */
    protected $contactRepo;
    protected $tagRepo;
    /**
     * Inject a contact repository
     *
     * @param  $repo CreateRepository
     * @return $this
     */
    public function setContactRepository(CreateRepository $contactRepo)
    {
        $this->contactRepo = $contactRepo;
        return $this;
    }

    public function setTagRepository(TagRepository $tagRepo)
    {
        $this->tagRepo = $tagRepo;
        return $this;
    }

    /**
     * @var Validator
     */
    protected $contactValidator;

    /**
     * Inject a contact validator
     *
     * @param  $repo Validator
     * @return $this
     */
    public function setContactValidator(Validator $contactValidator)
    {
        $this->contactValidator = $contactValidator;
        return $this;
    }

    // Usecase
    public function interact()
    {        
        // Fetch and hydrate the message entity...
        $entity = $this->getEntity();

        // ... verify that the message entity can be created by the current user
        $this->verifyReceiveAuth($entity);

        // ... verify that the message entity is in a valid state
        $this->verifyValid($entity);

        // Find or create contact based on >$this->getPayload('from')
        $contact = $this->getContactEntity();

        // ... verify the contact is valid        
        $this->verifyValidContact($contact);
        
        // ... create contact if it doesn't exist
        $contact_id = $this->createContact($contact);
        $entity->setState(compact('contact_id'));
        $entity->setState(['user_id' => $contact->user_id]);
        $id = null;

        // ... persist the new message entity
        $id = $this->repo->create($entity);

        $fields = $this->tagRepo->getSearchFields();
        $this->search = new SearchData(
            $this->getFilters($fields)
        );
        $this->tagRepo->setSearchParams($this->search);
        $tags = $this->tagRepo->getSearchResults();

        $entity->setState(compact('id'));

        $activeForms = DB::select('select * from forms where disabled = ?', [0]);

        $this->dispatch('message.receive', [
            'id' => $id,
            'entity' => $entity,
            'tags' => $tags,
            // @todo pass these some other way
            'inbound_form_id' => $this->getPayload('inbound_form_id', false),
         //   'inbound_form_id' => count($activeForms) > 0 ? $activeForms[0]->id : 1,
            'inbound_fields' => $this->getPayload('inbound_fields', [])
        ]);

        return $id;
    }

    /**
     * Get an empty entity, apply the payload.
     *
     * @return Entity
     */
    protected function getEntity()
    {
        return $this->repo->getEntity()->setState(
            $this->payload + [
                'status' => Message::RECEIVED,
                'direction' => Message::INCOMING
            ]
        );
    }

    /**
     * Create contact record for message
     *
     * @return Entity $contact
     */
    protected function getContactEntity()
    {
        // Is the sender of the message a registered contact?
        $contact = $this->contactRepo->getByContact($this->getPayload('from'), $this->getPayload('contact_type'));
        if (!$contact->getId()) {
            // this is the first time a message has been received by this number, so create contact
            $contact =  $this->contactRepo->getEntity()->setState([
                'contact' => $this->getPayload('from'),
                'type' => $this->getPayload('contact_type'),
                'data_source' => $this->getPayload('data_source'),
            ]);
        }
        return $contact;
    }

    /**
     * Create contact (if its new)
     *
     * @param  Entity $contact
     * @return Int
     */
    protected function createContact(Entity $contact)
    {
        // If contact already existed, just return id.
        if ($contact->getId()) {
            return $contact->getId();
        }

        return $this->contactRepo->create($contact);
    }

    protected function verifyValidContact(Entity $contact)
    {
        // validate contact
        if (!$this->contactValidator->check($contact->asArray())) {
            $this->contactValidatorError($contact);
        }
    }

    /**
     * Throw a ValidatorException
     *
     * @param  Entity $entity
     * @return null
     * @throws ValidatorException
     */
    protected function contactValidatorError(Entity $entity)
    {
        throw new ValidatorException(
            sprintf(
                'Failed to validate %s entity',
                $entity->getResource()
            ),
            $this->contactValidator->errors()
        );
    }

    /**
     * Verifies the current user is allowed receive access on $entity
     *
     * @param  Entity $entity
     * @return void
     * @throws AuthorizerException
     */
    protected function verifyReceiveAuth(Entity $entity)
    {
        $this->verifyAuth($entity, 'receive');
    }
}
