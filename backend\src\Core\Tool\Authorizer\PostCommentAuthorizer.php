<?php

// UJEVMS-52 - SC<PERSON><PERSON> Luca, <EMAIL> - 28/01/2020.

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use Ushahidi\Core\Entity\User;
use Us<PERSON>hidi\Core\Entity\Form;
use <PERSON><PERSON>hidi\Core\Entity\FormRepository;
use <PERSON><PERSON>hidi\Core\Entity\UserRepository;
use Ushahidi\Core\Entity\PostRepository;
use <PERSON>hahidi\Core\Entity\Permission;
use Ushahidi\Core\Tool\Authorizer;
use Ushahidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\OwnerAccess;
use Ushahidi\Core\Traits\ParentAccess;
use Ushahidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivateDeployment;
use Ushahidi\Core\Tool\Permissions\AclTrait;

class PostCommentAuthorizer implements Authorizer
{
  use UserContext;

  use AdminAccess, OwnerAccess, ParentAccess;

  use PrivAccess;

  use PrivateDeployment;

  use AclTrait;

  protected function getAllPrivs()
  {
    return ['read', 'create', 'update', 'delete', 'search'];
  }

  public function __construct()
  {
  }

  public function isAllowed(Entity $entity, $privilege)
  {
    $user = $this->getUser();

      if (!$this->canAccessDeployment($user)) {
        return false;
      }
      // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/09/2020.
      // if ($this->acl->hasPermission($user, Permission::ESCALATE_POSTS)) {
      //   return true;
      // }
      
      if(in_array($privilege, ['create', 'search', 'read'])
      && ($this->acl->hasPermission($user, [Permission::CommentReportConflictividadElectoral,Permission::CommentReportOperacionesElectorales])
      ||  ($this->acl->hasPermission($user, [Permission::ViewIncidentReporterIdentityConflictividadElectora,Permission::EditAndManageReportsOperacionesElectorales])))) {
        return true;
      }

      if($privilege == 'update' && $this->acl->hasPermission($user, [Permission::CommentReportConflictividadElectoral,Permission::CommentReportOperacionesElectorales])) {
        return true;
      }

      if (
        in_array($privilege, ['create', 'update', 'lock'])
        && (!$this->isUserOwner($entity, $user)
        && !$this->acl->hasPermission($user, [Permission::ViewIncidentReporterIdentityConflictividadElectora,Permission::EditAndManageReportsOperacionesElectorales]))
      ) {
          return false;
      }
  
      if ($this->isUserAdmin($user)) {
        return true;
      }
  
      return false;
  }

  protected function getParent(Entity $entity)
  {
    return false;
  }
}
