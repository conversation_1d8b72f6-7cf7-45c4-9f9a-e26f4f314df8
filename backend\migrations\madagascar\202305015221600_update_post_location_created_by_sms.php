<?php

// UEH-146 - Check Location mapping in backend when incidents are created through SMS

use Phinx\Migration\AbstractMigration;

class UpdatePostLocationCreatedBySms extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */

  /**
   * Migrate Up.p
   */
  public function up()
  {
    // Switch the county and district values for existing posts which was created by SMS
    $this->execute("UPDATE posts, messages SET posts.mgmt_lev_1 = posts.mgmt_lev_2, posts.mgmt_lev_2 = posts.mgmt_lev_1 WHERE messages.post_id is not null and messages.post_id = posts.id and posts.mgmt_lev_2 != 'Todee'");
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
