# Defect Management

When a defect is picked up during the smoke or regression testing phase, an issue is reported in GitHub following a bug reporting template found [here](https://github.com/ushahidi/platform/issues/new?template=Bug_report.md). 

The following information is captured in the report.   


* Title - short description of what the issue is. Anyone reading the title should be able to tell what the issue is.
* Describe the bug - more details on what the issue is.
* Expected Behavior - what should have happened.
* Actual behavior - what actually happened. This needs to be a very accurate and detailed description of the behavior experienced.
* Steps to reproduce - actions followed when bug was discovered.
* Environment - Client used, version number
* Operating System
* Additional Information. Could be logs, screenshots, urls.

Before reporting an issue, take the time to look through the [current issues](https://github.com/ushahidi/platform/issues) and make sure it has not been reported. If it hasn’t been reported, proceed to creating a new issue.  


TestPad has a feature that allows you to comment on any failure. Here you can leave a brief of why a test failed, including screenshots. If an issue is created on GitHub from this failure, you can leave a link to the issue in the comment section for easier tracking.   
****

\*\*\*\*

