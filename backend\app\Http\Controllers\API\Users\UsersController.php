<?php

namespace Ushahidi\App\Http\Controllers\API\Users;

use Ushahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use League\Csv\Reader;
use League\Csv\Writer;
use DB;

/**
 * Ushahidi API Users Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */
class UsersController extends RESTController
{
    protected function getResource()
    {
        return 'users';
    }

    public function index(Request $request)
    {   
        $query  =   DB::table('users')
                        ->select('users.*','users.id as uid','roles.short_name as short_name')
                        ->leftjoin('roles','roles.name','=','users.role');

        // Check for filters
        if($request->has('active')){            
            $query = $query->where('active','=', $request->get('active'));
        }
        if($request->has('q')) {
            $query = $query->where('email', 'LIKE', "%" . $request->get('q') . "%");
            $query = $query->orWhere('realname', 'LIKE', "%" . $request->get('q') . "%");
        }
        // print_r($request->q);
        $totalCount = $query->get()->count();
        $query      =   $query->orderBy($request->get('orderby','realname'), $request->get('order','asc'));
        $query      =   $query->offset($request->get('offset'))->take($request->get('limit'));
        $users      =   $query->get()->groupBy('user_id');
        $data 	    =   $this->getUserInfo("list", $users);
        $response   =   [
            "count"         => count($data),
            "total_count"   => $totalCount,
            "results"       => $data
        ];
        return response($response);
    }

    /**
     * Get current user
     * GET /api/users/me
     * @return void
     */
    public function showMe(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'read')
            ->setIdentifiers([
                'id' => $request->user()->id ?: 0
            ]);

        return $this->prepResponse($this->executeUsecase($request), $request);
    }

    /**
     * Get options for /users/me
     * @return void
     */
    public function optionsMe()
    {
        $this->response->status(200);
    }

    /**
     * Update current user
     * PUT /api/users/me
     * @return void
     */
    public function updateMe(Request $request)
    {
        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'update')
            ->setIdentifiers([
                'id' => $request->user()->id ?: 0
            ])
            ->setPayload($request->json()->all());

        return $this->prepResponse($this->executeUsecase($request), $request);
    }
    
    private function getUserInfo($type, $users)
    {
        $data = [];
        $csvdata = [];
        foreach($users as $user){
            foreach($user as $key => $userData){
                // Get user contacts
                $userContacts           = DB::table('contacts')
                                            ->select('contacts.*')
                                            ->where('contacts.user_id','=', $userData->id)
                                            ->get();
                $plainUserContacts      = [];                                            
                foreach($userContacts as $key=>$value){
                    $plainUserContacts[] = $value->contact;
                }                                            
                
                switch($type){
                    case 'list'     :
                    case 'full'     :
                        $data[] = [
                            'id'                => $userData->id,
                            'active'            => $userData->active?true:false,
                            'email'		 	    => $userData->email,
                            'realname' 		    => $userData->realname,
                            'username' 		    => $userData->realname,
                            'role_short' 	    => $userData->short_name,            
                            'role' 		    	=> $userData->role,
                            'mgmt_lev_1'        => isset($userData->mgmt_lev_1)?$userData->mgmt_lev_1:'',
                            'organization'      => $userData->organization,
                            'position'          => $userData->position,
                            'contacts'          => $userContacts
                        ];
                        break;

                    default         :
                    case 'export'   :
                    case 'csv'      :
                        $data[$key] = [
                            'username' 		=> $userData->realname,
                            'email'		 	=> $userData->email,
                            'role' 			=> $userData->short_name,
                            'region'        => isset($userData->mgmt_lev_1)?$userData->mgmt_lev_1:'',
                            'organization'  => $userData->organization,
                            'position'      => $userData->position
                        ];
                        
                        if($userData->contact != '' && isset($userData->contact)) {                        
                            $phoneKey   = isset($data[$userData->uid])?"TELEPHONE2":"TELEPHONE";
                            $data[$key][$phoneKey] = $userData->contact;
                        }
                        $csvdata[] = $data[$key];
                        break;
                }
            }
        }
        return $data;
    }

    private function getUsersList($type, $users)
    {
        $data = [];
        $csvdata = [];
        foreach($users as $user){
            foreach($user as $key => $userData){
                if (isset($data[$userData->email])) {
                    continue;
                }            
                // Get user contacts
                $userContacts           = DB::table('contacts')
                                            ->select('contacts.*')
                                            ->where('contacts.user_id','=', $userData->id)
                                            ->get();
                $plainUserContacts      = [];                                            
                foreach($userContacts as $key=>$value){
                    $plainUserContacts[] = $value->contact;
                }                                            
                
                switch($type){
                    default         :
                    case 'export'   :
                    case 'csv'      :
                        $data[$userData->email] = [
                            'username' 		=> $userData->realname,
                            'email'		 	=> $userData->email,
                            'role' 			=> $userData->short_name,
                            'region'        => isset($userData->mgmt_lev_1)?$userData->mgmt_lev_1:'',
                            'organization'  => $userData->organization,
                            'position'      => $userData->position
                        ];
                        
                        if($userData->contact != '' && isset($userData->contact)) {                        
                                $phoneKey   = "TELEPHONE";
                                $phoneKey1   = "TELEPHONE2";
                                $data[$userData->email][$phoneKey] = $plainUserContacts[0];
                                // if(count($plainUserContacts) > 1) {
                                //    $data[$userData->email][$phoneKey1] = $plainUserContacts[1];
                                // }else{
                                //       $data[$userData->email][$phoneKey1] = '';
                                // }

                        }
                        $csvdata[] = $data[$userData->email];
                        break;
                }
            }
        }
        return $csvdata;
    }

    public function exportJSON() 
    {           
        $users 		= $this->usecaseFactory->userExportData();        
        $data 		= $this->getUserInfo("export",$users);
        return response($data);
    }

    public function exportCSV() 
    {        
        header("Content-Type: application/json; charset=UTF-8");
        $users 		= $this->usecaseFactory->userExportData();
        $csv 		= Writer::createFromFileObject(new \SplTempFileObject());
       // $header 	= ['USER NAME','EMAIL','SHORT ROLE NAME','COUNTY','ORGANZATION','POSITION','TELEPHONE','TELEPHONE2'];
     //  $header   = ['Nom d\'utilisateur','E-mail','Nom De Rôle Abrégé','Région','Organisation','Position','Téléphone','Téléphone2'];

        //  $header     = ['Nombre', 'Email' , 'Abreviación de tipo de usuario' , 'Departamento' , 'Organización' , 'Puesto' , 'Teléfono' , 'Teléfono2', 'Contraseña'];
         $header     = ['Name', 'Email', 'User role abbreviation', 'Region', 'Organization', 'Position', 'Phone','Password'];

		
		$csv->insertOne($header);
		
		$data 		= $this->getUsersList("export",$users);
        $out        = fopen('php://output', 'w');    
        
        $json_response = [];

        if (isset($data) && !empty($data)) {
            $json_response = [
                'success' => true, 
                'headers' => $header,
                'data' => $data
            ];
        } else {
            $json_response = [
                'success' => false
            ];
        }

        return response($json_response);

        // $csv->insertAll($data);
        // return response($csv->output())
        //     ->withHeaders([
        //         'Content-Type'          => 'application/csv',
        //         'Content-Disposition'   => 'attachment; filename="user.csv"',
        //     ]);
    }    
}
