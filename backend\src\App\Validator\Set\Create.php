<?php

/**
 * <PERSON><PERSON>hidi Set Validator
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Validator\Set;

class Create extends Update
{
    protected function getRules()
    {
        return array_merge_recursive(parent::getRules(), [
            'name' => [['not_empty']],
            'user_id' => [
                [[$this->user_repo, 'exists'], [':value']],
            ],
        ]);
    }
}
