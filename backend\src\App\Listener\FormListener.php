<?php

/**
 * <PERSON><PERSON>hidi Form Listener
 *
 * Listens for new posts that are added to a set
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Listener;

use League\Event\AbstractListener;
use League\Event\EventInterface;
use <PERSON><PERSON><PERSON>di\Core\Entity\FormStageRepository;
use Us<PERSON>hidi\Core\Entity\FormAttributeRepository;

class FormListener extends AbstractListener    
{
    protected $form_stage_repo;
    protected $form_attr_repo;

    public function setStageRepository(FormStageRepository $repo)
    {
      $this->form_stage_repo = $repo;
    }

    public function setAttributeRepository(FormAttributeRepository $repo)
    {
      $this->form_attr_repo = $repo;
    }

    public function handle(EventInterface $event, $formId = null, $event_type = null)
    {
        $state = [
            'form_id' => $formId,
            'label' => 'Form stage',
            'type' => $event_type,
            'task_is_internal_only' => false
        ];
        $entity = $this->form_stage_repo->getEntity();
        $entity->setState($state);
        $formStageId = $this->form_stage_repo->create($entity);

        $attrEntity = $this->form_attr_repo->getEntity();
        // Form Title & Description attributes
        $stateArray = [
          [
            'key' => 'uuid()',
            'label' => 'Title',
            'input' => 'text',
            'type' => 'title',
            'required' => 1,
            'priority' => 1,
            'options' => NULL,
            'cardinality' => 0,
            'config' => '[]',
            'form_stage_id' => $formStageId
          ],
          [
            'key' => 'uuid()',
            'label' => 'Description',
            'input' => 'text',
            'type' => 'description',
            'required' => 1,
            'priority' => 2,
            'options' => NULL,
            'cardinality' => 0,
            'config' => '[]',
            'form_stage_id' => $formStageId
          ]            
        ];


        if (isset($formStageId)) {
          foreach($stateArray as $key => $val) {
            $attrEntity->setState($val);
            $this->form_attr_repo->create($attrEntity);
          }
        }
    }
}
