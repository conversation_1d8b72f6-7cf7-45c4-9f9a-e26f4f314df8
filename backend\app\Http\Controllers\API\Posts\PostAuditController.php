<?php

// UJEVMS-65 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 29/05/2020.

namespace Ushahidi\App\Http\Controllers\API\Posts;


use <PERSON>hahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase;

class PostAuditController extends PostsController
{

  protected function getResource()
  {
    return 'audit';
  }

  protected function getIdentifiers(Request $request)
  {
    return $this->getRouteParams($request)+ [
            'user_id' => $request->user()->id
        ];
  }
  /**
   * Get current user last read notification Id
   *
   * Get /api/posts/audit
   *
   */
  public function index(Request $request)
  {
    $this->usecase = $this->usecaseFactory
      ->get($this->getResource(), 'read')
      ->setIdentifiers($this->getIdentifiers($request))
      ->setFormatter(service('formatter.entity.post.audit'));
    return $this->prepResponse($this->executeUsecase($request), $request);
  }

  /**
   * Get audit data for post id given
   *
   * Get /api/posts/{id}/audit
   *
   */

  public function search(Request $request)
  {
    $this->usecase = $this->usecaseFactory
      ->get($this->getResource(), 'search')
      ->setFilters($request->query() + $this->getRouteParams($request))
      ->setFormatter(service('formatter.entity.post.audit'));
    return $this->prepResponse($this->executeUsecase($request), $request);
  }

}
