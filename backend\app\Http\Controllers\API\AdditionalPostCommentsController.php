<?php

namespace Us<PERSON>hidi\App\Http\Controllers\API;

use <PERSON>hahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use DB;
use Ushahidi\App\Models\TenantOption;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

/**
 * Ushahidi API Contacts Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class AdditionalPostCommentsController extends RESTController{

    protected function getResource(){
        return '';
    }

    public function save_post(Request $request){
        
        $this->validate($request,[
            'message' => 'required|string',
        ]);
                   
        $id = DB::table('additional_post_comments')->insertGetId([
            'message' => $request->message,
            'post_id' => $request->post_id,
            'created' => time(),
            'author' => 'public',
        ]);
        if($id){
            return response()->json(["success" => true],200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }else{
            return response()->json(["success" => false],422,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }
    }

    /*
    * Function use for getting all additional comments for perticular post
    */
    public function get_comments(Request $request, $post_id) {

        // Get all the comments for given Post Id
        $post_comments	= DB::table('additional_post_comments')
                            ->select('additional_post_comments.*')
                            ->where('additional_post_comments.post_id','=', $post_id)
                            ->where('additional_post_comments.status','=', 'published')
                            ->orderBy('additional_post_comments.created', 'DESC')
                            ->get();

        if(isset($post_comments) && !empty($post_comments) && (count($post_comments) > 0)){
            return response()->json($post_comments,200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }else{
            return response()->json([],200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }
    }

    /*
    * Function use for Deleting comments
    * Roles allowed: admin, sysadmin, Management User
    */
    public function delete(Request $request, $id){
        $user = service('session')->getUser();
        $allowed_role = ['admin', 'sysadmin', 'Management User'];
        $user_role = $user->role;
        
        if (in_array($user_role, $allowed_role)) {
            $result = DB::table('additional_post_comments')->where('id', $id)->update(['status' => 'deleted']);
            return response()->json(["success" => true],200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        } else {
            return response()->json(["success" => false],422,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }

    }
    
}