<?php

// UJEVMS-69 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 07/21/2020.


use Phinx\Migration\AbstractMigration;

class InsertUserActivityPermission extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

    $this->execute("
    start transaction;
    delete from roles_permissions where permission = 'View User Activity';
    insert into roles_permissions (role, permission) values
        ('sysadmin', 'View User Activity')
      , ('super', 'View User Activity')
      , ('admin', 'View User Activity');    
    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
