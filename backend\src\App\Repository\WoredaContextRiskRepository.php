<?php

// UJEVMS-62 - SC<PERSON><PERSON>, <EMAIL> - 14/02/2020.

namespace <PERSON><PERSON>hidi\App\Repository;

use <PERSON><PERSON><PERSON><PERSON>\Core\Entity;
use Us<PERSON>hidi\Core\Entity\WoredaContextRisk;
use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\WoredaContextRiskRepository as WoredaContextRiskRepositoryContract;
use <PERSON><PERSON><PERSON>di\App\Multisite\OhanzeeResolver;
use <PERSON>hahidi\Core\SearchData;
use Ushahidi\Core\Tool\Permissions\AclTrait;
use Ushahidi\App\Http\Controllers\RESTController;
use Illuminate\Support\Facades\DB;

class WoredaContextRiskRepository extends OhanzeeRepository implements WoredaContextRiskRepositoryContract
{

  use AclTrait;

  public function __construct(
        OhanzeeResolver $resolver
    ) {
      parent::__construct($resolver);
  }

  public function getEntity(array $data = null)
  {
  
    return new WoredaContextRisk($data);
  }

  protected function getTable()
  {
    return 'woreda_context_risk';
  }

    // UET-12 - Fulpa<PERSON>e Pramod, <EMAIL> - 03/11/2021.
  protected function setSearchConditions(SearchData $search)
  {
    $query = $this->search_query;    
    return $query;
  }

  public function getData(SearchData $search) {

    $type = -1;
    if ($search) {
      $type = $search->getFilters(['type']);
    }
    switch($type['type']) {
      case 1:
      $statement = "SELECT distinct(woreda_context_id) as id, date, name as woreda, oneDayAgoExtendedRisk as forecastedRisk FROM woreda_context_risk JOIN woreda_context ON woreda_context_risk.woreda_context_id = woreda_context.id  ORDER BY date DESC LIMIT 1042";
      break;
      case 2:
      $statement = "SELECT distinct(woreda_context_id) as id, date, name as woreda, dailyWoredaRisk as forecastedRisk FROM woreda_context_risk JOIN woreda_context ON woreda_context_risk.woreda_context_id = woreda_context.id ORDER BY date DESC LIMIT 1042";
      break;
      default: 
      $statement = "SELECT distinct(woreda_context_id) as id, date, name as woreda, forecastedRisk FROM woreda_context_risk JOIN woreda_context ON woreda_context_risk.woreda_context_id = woreda_context.id ORDER BY date DESC LIMIT 1042";
      break;
    }

    $results = DB::select($statement);
    if ($type['type'] == 0) {
      foreach ($results as $entry) {
        $query_woread_forecasted = "SELECT max(forecastedRisk) as maxRisk, woreda_context_id
              FROM woreda_context_risk JOIN woreda_context ON woreda_context_risk.woreda_context_id = woreda_context.id
              WHERE woreda_context_risk.woreda_context_id = $entry->id
              GROUP BY woreda_context_id";
              $woreda_forecasted = DB::select($query_woread_forecasted);
        $maxVal = $woreda_forecasted[0]->maxRisk;
        $entry->forecastedRisk = max($maxVal, $entry->forecastedRisk);    
      }
    }  
    return $results;
  }

  public function getSearchFields()
  {
    return [
      'woreda_context_id',
      'date',
      'startDate',
      'endDate',
      'name',
      'type'
    ];
  }  
}
