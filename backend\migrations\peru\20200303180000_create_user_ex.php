<?php

use Phinx\Migration\AbstractMigration;

class CreateUserEx extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('users_ex')
      ->addColumn('user_id', 'integer',  ['null' => true])
      ->addColumn('user_data', 'string', ['null' => true])
      ->addColumn('created', 'integer', ['default' => 0])
      ->addColumn('updated', 'integer', ['default' => 0])
       ->addIndex(['user_id'], ['unique' => true])
      ->addForeignKey('user_id', 'users', 'id', [
        'delete' => 'CASCADE',
        'update' => 'RESTRICT',
      ])
      ->create();
  }
}
