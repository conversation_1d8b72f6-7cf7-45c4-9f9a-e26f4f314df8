<?php

/**
 * Repository for Post Values
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Usecase\Post;

interface ValuesForPostRepository
{
    /**
     * @param  int $post_id
     * @param  Array $include_attributes
     * @param  Array $exclude_stages
     * @param  boolean $excludePrivateValues
     * @return [Ushahidi\Core\Entity\PostValue, ...]
     */
    public function getAllForPost(
        $post_id,
        array $include_attributes = [],
        array $exclude_stages = [],
        $excludePrivateValues = true
    );

    /**
     * @param  int $post_id
     * @return [Ushahidi\Core\Entity\PostValue, ...]
     */
    public function deleteAllForPost($post_id);
}
