<?php

use Phinx\Migration\AbstractMigration;

class UpdateLiberiaLatLonDefaultZoomInTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
        $lat = -18.83619823210135;
        $lon =  45.85406998770115;
        $zoom = 5.5;

        $peru_lat = -7.046374;
        $peru_lon =  -77.042793;
        $peru_zoom = 5;
      
        $malawi_lat = -13.3022125;
        $malawi_lon =  34.0367263;
        $malawi_zoom = 6;
      
        $this->execute("DELETE from tenant_options WHERE tenant_id = 1 AND tenant_key = 'map'");
      //  $this->execute("DELETE from tenant_options WHERE tenant_id = 3 AND tenant_key = 'map'");
      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'map', '{\"lat\":$lat,\"lon\":$lon,\"zoom\":$zoom}')");
//         $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'map', '{\"lat\":$lat,\"lon\":$lon,\"zoom\":$zoom}')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'map', '{\"lat\":$peru_lat,\"lon\":$peru_lon,\"zoom\":$peru_zoom}')");

        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'map', '{\"lat\":$malawi_lat,\"lon\":$malawi_lon,\"zoom\":$malawi_zoom}')");

    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}