<?php

// UJEVMS-69 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 07/09/2020.


use Phinx\Migration\AbstractMigration;

class AddNewRolePermission extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

    $this->execute("
    start transaction;

    set FOREIGN_KEY_CHECKS = 0;
    
    ALTER TABLE permissions
            MODIFY name VARCHAR(100);

    ALTER TABLE roles_permissions
            MODIFY permission VARCHAR(100);

    insert into roles (name, short_name, display_name, description, protected) values
        ('viewer1', 'VU1', 'ViewerUser1', 'Viewer User 1', 8);

    insert into permissions (name, description) values
        ('Request modifications of risks/incidents logged by others', 'Request modifications of risks/incidents logged by others');
    
    insert into roles_permissions (role, permission) values
        ('viewer1', 'Access eview dashboard'), ('regionaluser1', 'Request modifications of risks/incidents logged by others');
        
    set FOREIGN_KEY_CHECKS = 1;

    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
