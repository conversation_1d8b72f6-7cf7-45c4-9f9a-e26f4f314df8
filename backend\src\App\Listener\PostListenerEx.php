<?php

// UJEVMS-62 - SC<PERSON><PERSON> Luca, <EMAIL> - 14/02/2020.

namespace <PERSON><PERSON><PERSON><PERSON>\App\Listener;

use League\Event\AbstractListener;
use League\Event\EventInterface;

use <PERSON><PERSON><PERSON>di\Core\Entity\NotificationExRepository;
use <PERSON><PERSON><PERSON>di\Core\Entity\AuditRepository;

class PostListenerEx extends AbstractListener
{
    protected $notificationsExRepo;
    protected $auditRepo;

    public function setNotificationsExRepo(NotificationExRepository $notificationsExRepo)
    {
        $this->notificationsExRepo = $notificationsExRepo;
    }

    public function setAuditRepo(AuditRepository $auditRepo)
    {
        $this->auditRepo = $auditRepo;
    }

    public function handle(EventInterface $event, $post = null, $event_type = null)
    {
        $supportedEvents = ['createEx', 'updateEx', 'deleteEx'];
        if (!in_array($event_type, $supportedEvents)) return;

        $eventsLabels = ['create', 'update', 'delete'];
        $event_type = $eventsLabels[array_search($event_type, $supportedEvents)];
        $state = [
            'post_id' => $post->id,
            'user_id' => $post->updated_by ?? $post->user_id,
            'event_type' => $event_type
        ];
        
        if ($event_type !== 'delete') {
            $notification = $this->notificationsExRepo->getEntity();
            $notification->setState($state);
            $this->notificationsExRepo->create($notification);
        }
        // getting audit state & create record for audit
        $stateAudit = $this->getStateForAudit($post, $event_type);
        $audit = $this->auditRepo->getEntity();
        $audit->setState($stateAudit);
        $this->auditRepo->create($audit);
    }

    public function getStateForAudit($post, $event_type) {
        if ($post) {
            $value = $post->asArray();
            $stateAudit = [
                'id' => $post->id,
                'entity'  => 'POST',
                'value' => json_encode($value),
                'type' => $event_type
            ];
        }
        return $stateAudit;
    }
}
