<?php

/**
 * Repository for Users
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Entity;

use <PERSON>hahidi\Core\Entity\Repository\EntityGet;
use Ushahidi\Core\Entity\Repository\EntityExists;

interface UserRepository extends
    EntityGet,
    EntityExists
{
    /**
     * @param string $email
     * @return \Ushahidi\Core\Entity\User
     */
    public function getByEmail($email);
}
