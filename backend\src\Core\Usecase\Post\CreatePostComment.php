<?php

// UJEVMS-52 - <PERSON><PERSON><PERSON>, <EMAIL> - 27/01/2020.

namespace Ushahidi\Core\Usecase\Post;

use <PERSON><PERSON>hidi\Core\Entity;
use <PERSON><PERSON><PERSON>di\Core\Usecase\CreateUsecase;

class CreatePostComment extends CreateUsecase
{
  use VerifyParentLoaded;

  protected function getEntity()
  {
    $entity = parent::getEntity();

    if (
      empty($entity->user_id) &&
      empty($entity->author_email) &&
      empty($entity->author_realname) &&
      $this->auth->getUserId()
    ) {
      $entity->setState(['user_id' => $this->auth->getUserId()]);
    }

    if (empty($entity->status)) {
      $entity->setState(['status' => 'pending']);
    }

    return $entity;
  }
}
