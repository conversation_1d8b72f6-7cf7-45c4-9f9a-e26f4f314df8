<?php

namespace Ushahidi\App\Http\Controllers\API;

use Ushahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use DB;

/**
 * Ushahidi API Roles Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class RolesController extends RESTController
{
    // Direct implementation. Too simple
    // Otherwise RESTController generate it dynamically    
    public function index(Request $request)
    {        
        $params     = $this->getRouteParams($request);                
        $entries    = DB::table('roles')->get();
                        
        foreach($entries as $entry){
            if($entry->name=="user"){ continue; }
            $results[]  = [
                "id"            => $entry->id,
                "name"          => $entry->name,
                "real_name"     => $entry->name,
                "short_name"    => $entry->short_name,
                "display_name"  => $entry->display_name,
                "description"   => $entry->description,
                "permissions"   => DB::table('roles_permissions')->where("role","=",$entry->name)->pluck('permission')->toArray()
            ];
        }        
        //return response()->json([ "count" => count($results), "results" => $results ]);
        return response()->json(  [   "success"   => true, 
                                    "count"     => count($results), 
                                    "results"   => $results 
                                ],
                                200,
                                ['Content-type'=> 'application/json; charset=utf-8'], 
                                JSON_UNESCAPED_UNICODE
                            );

        return response()->json(['success' => false,"message" => $e->getMessage()]);                            
    }

    protected function getResource()
    {
        return 'roles';
    }
}
