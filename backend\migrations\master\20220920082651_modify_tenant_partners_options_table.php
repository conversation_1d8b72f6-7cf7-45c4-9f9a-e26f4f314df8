<?php

use Phinx\Migration\AbstractMigration;

class ModifyTenantPartnersOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {

        $liberia_partners = array(
            'partners' => [
                 'partner-1.png',
                 'undp-logo.png',
            ],
            );
        $liberia_partners = json_encode($liberia_partners);

        $madagascar_partners = array(
            'partners' => [
                 'partner-1.png',
                 'logo.png',
                 'partner-2.png',
                 'partner-3.png',
            ],
            );
        $madagascar_partners = json_encode($madagascar_partners);

     //   $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'partners', '$liberia_partners')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'partners', '$madagascar_partners')");
    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
