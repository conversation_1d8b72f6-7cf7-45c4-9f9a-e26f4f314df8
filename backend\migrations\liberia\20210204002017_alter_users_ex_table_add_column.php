<?php

use Phinx\Migration\AbstractMigration;

class AlterUsersExTableAddColumn extends AbstractMigration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->table('users_ex')
            ->addColumn('institution', 'string', [
                'default' => null,
                'after' => 'user_data',
            ])
            ->addColumn('region', 'string', [
                'default' => null,
                'after' => 'user_data',
            ])
            ->addColumn('zone', 'string', [
                'default' => null,
                'after' => 'user_data',
            ])
            ->addColumn('woreda', 'string', [
                'default' => null,
                'after' => 'user_data',
            ])
            ->update();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
