<?php

// UJEVMS-65 - Fu<PERSON><PERSON><PERSON><PERSON> Pramod, <EMAIL> - 10/04/2020.

namespace <PERSON><PERSON><PERSON><PERSON>\App\Listener;

use League\Event\AbstractListener;
use League\Event\EventInterface;
use <PERSON><PERSON><PERSON>di\Core\Entity\NotificationExRepository;

use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\AuditRepository;

class PostCommentListener extends AbstractListener
{
    protected $notificationsExRepo;
    protected $auditRepo;

    public function setNotificationsExRepo(NotificationExRepository $notificationsExRepo)
    {
        $this->notificationsExRepo = $notificationsExRepo;
    }

    public function setAuditRepo(AuditRepository $auditRepo)
    {
        $this->auditRepo = $auditRepo;
    }

    public function handle(EventInterface $event, $post = null, $event_type = null)
    {

            // Send notifications to tagged user
            $value = $post->asArray();

            $supportedEvents = ['create', 'update', 'delete'];
            if (!in_array($event_type, $supportedEvents)) return;

            $eventsLabels = ['create', 'update', 'delete'];
            $event_type = $eventsLabels[array_search($event_type, $supportedEvents)];
            if ($value && !empty($value['users'])) {
                $user = $value['users'][0];
            }
            $state = [
                'post_id' => $post->post_id,
                'user_id' =>  ($value && $value['user_id']) ? $value['user_id'] : $user['id'],
                'event_type' => ($value['type'] === 0) ? 'response' : 'after-action'
            ];
            
            if ($event_type !== 'delete') {
                $notification = $this->notificationsExRepo->getEntity();
                $notification->setState($state);
                $this->notificationsExRepo->create($notification);
            }
        // if ($post && $event_type === 'update') {
            $stateAudit = [
                'id' => $post->id,
                'entity'  => 'POST_COMMENT',
                'value' => json_encode($value),
                'type' => $event_type
            ];
            $audit = $this->auditRepo->getEntity();
            $audit->setState($stateAudit);
            $this->auditRepo->create($audit);
       // }
    }
}
