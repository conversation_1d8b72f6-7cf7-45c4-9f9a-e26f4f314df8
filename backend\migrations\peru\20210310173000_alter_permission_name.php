<?php

// UJEVMS-69 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 07/09/2020.


use Phinx\Migration\AbstractMigration;

class AlterPermissionName extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $password = password_hash('password', PASSWORD_BCRYPT, ['cost' => 12]);

    $this->execute("
    start transaction;
    
    set FOREIGN_KEY_CHECKS = 0;
    
    truncate table roles_permissions;
    truncate table permissions;

    insert into roles_permissions (role, permission) values
        ('super', 'Access eview dashboard')
        , ('super', 'Create report')
        , ('super', 'Access incident risk reports')
        , ('super', 'View incident risk reporter identity')
        , ('super', 'Modify report information')
        , ('super', 'Modify personal incident risk reports')
        , ('super', 'Access analysis and filters section')
        , ('super', 'Access and modify parameters settings')

        , ('admin', 'Access eview dashboard')
        , ('admin', 'Create report')
        , ('admin', 'Access incident risk reports')
        , ('admin', 'View incident risk reporter identity')
        , ('admin', 'Modify report information')
        , ('admin', 'Modify personal incident risk reports')
        , ('admin', 'Access analysis and filters section')
        , ('admin', 'Access and modify parameters settings')
        , ('admin', 'Access Management')
        , ('admin', 'User Management')

        , ('managementuser', 'Access eview dashboard')
        , ('managementuser', 'Create report')
        , ('managementuser', 'Access incident risk reports')
        , ('managementuser', 'View incident risk reporter identity')
        , ('managementuser', 'Modify report information')
        , ('managementuser', 'Modify personal incident risk reports')
        , ('managementuser', 'Access analysis and filters section')

        , ('operatoruser', 'Access eview dashboard')
        , ('operatoruser', 'Create report')
        , ('operatoruser', 'Access incident risk reports')
        , ('operatoruser', 'View incident risk reporter identity')
        , ('operatoruser', 'Modify report information')
        , ('operatoruser', 'Modify personal incident risk reports')

        , ('regionaluser1', 'Access eview dashboard')
        , ('regionaluser1', 'Create report')
        , ('regionaluser1', 'Access incident risk reports')
        , ('regionaluser1', 'Modify report information')
        , ('regionaluser1', 'Modify personal incident risk reports')

        , ('regionaluser2', 'Access eview dashboard')
        , ('regionaluser2', 'Create report')
        , ('regionaluser2', 'Access incident risk reports')
        , ('regionaluser2', 'Modify personal incident risk reports')
        
        , ('sysadmin', 'Access eview dashboard')
        , ('sysadmin', 'Create report')
        , ('sysadmin', 'Access incident risk reports')
        , ('sysadmin', 'View incident risk reporter identity')
        , ('sysadmin', 'Modify report information')
        , ('sysadmin', 'Modify personal incident risk reports')
        , ('sysadmin', 'User Management')
        , ('sysadmin', 'Access and modify parameters settings');


        INSERT INTO permissions (name, description)
        VALUES
            ('Access eview dashboard', 'Access eview dashboard'),
            ('Create report', 'Create report'),
            ('Access incident risk reports', 'Access incident risk reports'),
            ('View incident risk reporter identity', 'View incident risk reporter identity'),
            ('Modify report information', 'Modify report information'),
            ('Modify personal incident risk reports', 'Modify personal incident risk reports'),
            ('Access analysis and filters section', 'Access analysis and filters section'),
            ('Access and modify parameters settings', 'Access and modify parameters settings'),
            ('Access Management', 'Access Management'),
            ('User Management', 'User Management');

    
    set FOREIGN_KEY_CHECKS = 1;
    
    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
