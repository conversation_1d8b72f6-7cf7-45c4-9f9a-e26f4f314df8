<?php

namespace <PERSON><PERSON>hi<PERSON>\App;

use Illuminate\Contracts\Events\Dispatcher;
use <PERSON><PERSON><PERSON><PERSON>\App\Listener\CreatePostFromMessage;
use <PERSON><PERSON><PERSON>di\App\Listener\HandleTargetedSurveyResponse;
use <PERSON><PERSON><PERSON>di\App\Listener\QueueExportJob;
use <PERSON><PERSON><PERSON>di\App\Listener\SendAlertsJob;
class Subscriber
{

    /**
     * Register the listeners for the subscriber.
     *
     * @param  \Illuminate\Events\Dispatcher  $events
     */
    public function subscribe(Dispatcher $events)
    {
        $events->listen(
            'message.receive',
            HandleTargetedSurveyResponse::class
        );

        $events->listen(
            'message.receive',
            CreatePostFromMessage::class
        );

        $events->listen(
            'export_job.create',
            QueueExportJob::class
        );

        $events->listen(
            'alerts.send',
            SendAlertsJob::class
        );
    }
}
