<?php

use Phinx\Migration\AbstractMigration;

class AddPublicNameInTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
       
        $zimbabwe_public_name  = 'iReport Zimbabwe';
        $liberia_public_name  = 'LERN Platform';
        $madagascar_public_name  = 'iReport Madagascar';
        $peru_public_name  = 'iReport Peru';
        $malawi_public_name  = '<PERSON><PERSON>';

        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'publicName', '$zimbabwe_public_name')");
       // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'publicName', '$liberia_public_name')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'publicName', '$madagascar_public_name')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'publicName', '$peru_public_name')");

        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'publicName', '$malawi_public_name')");

    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
