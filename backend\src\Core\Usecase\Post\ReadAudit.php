<?php

/**
 * Ushahidi Platform Post Read Use Case
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Usecase\Post;

use Ushahidi\Core\Usecase\ReadUsecase;

class ReadAudit extends ReadUsecase
{
  public function interact()
  {
    $entity = $this->getEntity();
    return $entity;
  }

  protected function getEntity()
  {
    $user_id = $this->getRequiredIdentifier('user_id');
    $data = $this->repo->getByUserId($user_id);
    return $this->formatter->__invoke($data);
  }
}
