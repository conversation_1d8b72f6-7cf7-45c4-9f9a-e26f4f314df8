<?php
use Phinx\Migration\AbstractMigration;

class UpdateInitialFormAttributeRisk extends AbstractMigration
{

  public function change()
  {
      $connection = $this->getAdapter()->getConnection();
      $adapter = $this->getAdapter();


      
      $title = json_encode(['Electoral','Political','Security','Economy','Social','Environment']);
      $info = json_encode(['Mainstream media','Social media','Word of mouth', 'Other']);
      $credi = json_encode(['High','Medium','Low','Unknown']);
      $like = json_encode(['Certain','Very likely','Likely','Possible','Unlikely','Very unlikely']);
      $impact = json_encode(['Extreme','Critical','Major','Minor','Negligible']);
      $stage = json_encode(['Campaign','Polling and counting','Results']);

      $attrInsert = $connection->prepare(
        "INSERT INTO form_attributes (" . $adapter->quoteColumnName('key') . ",
            label, input, type, required, priority, cardinality, options, config, form_stage_id)
        VALUES
            ( 'title_default', 'Title', 'text', 'title', 1, 1, 1, '[]', '[]', :form_stage_id)
            ,( 'description_default', 'Description', 'text', 'description', 1, 2, 1, '[]', '[]', :form_stage_id)
            ,( 'risk_default', 'Risk type', 'select', 'varchar', 0, 3, 1, '$title', '[]', :form_stage_id)
            ,('location_default', 'Location','location','point',0,4,1,'[]', '[]', :form_stage_id)
            ,('info_default', 'Source of information','select','varchar',0,5,1,'$info','[]',:form_stage_id)
            ,('info_default_other', 'Please specify','text','varchar',0,5,1,'[]','[]',:form_stage_id)
            ,('credibility_default', 'Credibility of the risk','select','varchar',0,6,1,'$credi','[]',:form_stage_id)
            ,('likelihood_default', 'Likelihood of the risk','select','varchar',0,7,1,'$like','[]',:form_stage_id)
            ,('impact_default', 'Impact of the risk','select','varchar',0,8,1,'$impact','[]',:form_stage_id)
            ,('stage_default', 'Stage of electoral process risk applies to','select','varchar',0,9,1,'$stage','[]',:form_stage_id)
        "
    )->execute([ ':form_stage_id' => 2 ]);      

      $rows = $this->fetchAll(
            "SELECT *
                FROM form_attributes WHERE form_attributes.key = 'info_default'"
        );

      foreach ($rows as $row) {
            $this->execute("
            update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"Other\"}}]'
            where form_attributes.key = 'info_default_other';
            ");
      }
 
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
    // Delete  foreign keys to posts_tags table
        $this->table('woreda_context_risk')
            ->dropForeignKey('woreda_context_id')
            ->update()
            ;
  }
}
