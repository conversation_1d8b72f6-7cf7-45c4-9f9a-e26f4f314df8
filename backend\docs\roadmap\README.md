# 🛣️ The Ushahidi Platform Roadmap

## Where to find the roadmap

* We add to our roadmap and modify it on a rolling basis based on the projects we have commited to doing for different grant projects or customer requests, prioritized bugs, and feature requests from the community. As we introduce more open roadmap planning strategies to our workflows, you will be able to access our near term roadmap aggregated in a public location.
* You can find the next features that we are considering adding to our roadmap with the tag [consider for upcoming cycle](https://github.com/ushahidi/platform/issues?q=is%3Aopen+is%3Aissue+label%3A%22consider+for+upcoming+cycle%22)
* You can find the features we are working on by filtering with the tag [https://github.com/ushahidi/platform/issues?q=is%3Aopen+is%3Aissue+label%3A%22Stage%3A+In+progress%22](https://github.com/ushahidi/platform/issues?q=is%3Aopen+is%3Aissue+label%3A%22Stage%3A+In+progress%22)

## How a new item gets accepted into the roadmap

* We evaluate new features in collaboration with engineering, product, design, support and other functions in Ushahidi.
* Ushahidi staff triage issues as they come in. These are general guidelines to ensure your feature request or bug report will be understood properly and can be acted upon:
  * Follow the issue submission guidelines. We provide complete issue templates for Bug reports and Feature requests, [which you can find here ](https://github.com/ushahidi/platform/issues/new/choose).
  * You may receive follow up questions from the team doing triage and prioritization, which you are encouraged to respond to. This will ensure the person triaging the issue can understand the scope of work and needs fully.
  * It helps to have a concise description, from a first-person point of view, of what the problem you are facing and need to solve is.
* It's also worth noting that not all parts of the system are equally funded at any given point for feature development, and that reality also influences when a feature can be prioritized to be worked on by Ushahidi staff. Funding for a specific goal \(ie improve data triage process so that people can work more efficiently in an emergency\) means our focus will generally be in a handful of improvements related to that goal.

## How the decision to prioritize one issue over another is made

* For bugs, it comes down to how critical it is, if it's in the critical path to a function, and how common it is that someone would run into it trying to execute a function. For instance triage issues tend to be prioritized higher than issues in the webhook setup flows, since triage is a much more common action than webhook setup. This is also weighed with the time necessary to fix an issue, the team available to fix it, and the budget available for it.
* For features, we prioritize what we want to see in the platform next with the help of all the Ushahidi team, their inputs as they interact with users, and user testing.
* Alignment to Ushahidi platform goals, resource limits, and needs of users: it's important for us to work on things that align closely with our mission. That means we will often have to say no to things that fall outside the scope or intent of the product. For instance: while a calendar might sound great, we cannot really make a calendar from scratch because it's outside of the scope of our product and what we do best, plus we just don't have the resources to maintain it... however: we could integrate with one if there was enough evidence that it is needed. When requesting a new feature, we recommend providing the problem you are trying to solve so that together we can try to come to a solution that fits the Ushahidi Platform's scope.

## Next steps

We are working on improving how we prioritize move issues into them our roadmap. The next steps on this are first internal discussions with Ushahidi staff on what we believe are the rules of engagement on roadmap decisions, initial documentation about the new roadmap decision making structure, and after initial docs, and requests for comments from the community. As we adjust our workflows to become more open and transparent for everyone, we want to hear from you, and will be opening up our process for comments in the near future.

