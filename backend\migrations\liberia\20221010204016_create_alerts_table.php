<?php

use Phinx\Migration\AbstractMigration;

class CreateAlertsTable extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {
        $this->table('alerts')
            ->addColumn('radius', 'integer', ['default' => 0])
            ->addColumn('latitude', 'string', ['null' => false])
            ->addColumn('longitude', 'string', ['null' => false])
            ->addColumn('email', 'string', ['null' => false])
            ->addColumn('categories', 'string', ['null' => false])
            ->addColumn('status', 'integer', ['default' => 0])
            ->addColumn('created', 'integer', ['default' => 0])
            ->addColumn('updated', 'integer', ['default' => 0])
            ->create();
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        $this->dropTable('alerts');
    }
}
