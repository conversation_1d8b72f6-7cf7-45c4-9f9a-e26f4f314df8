<?php

use Phinx\Migration\AbstractMigration;

class AddContactUsEmailsToTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
        // Contact Us Email
        $zimbabwe_contact_us_emails = array(
            'contact-us-emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            );
        $zimbabwe_contact_us_emails = json_encode($zimbabwe_contact_us_emails);

        $liberia_contact_us_emails = array(
            'contact-us-emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            );
        $liberia_contact_us_emails = json_encode($liberia_contact_us_emails);

        $madagascar_contact_us_emails = array(
            'contact-us-emails' => [
                '<EMAIL>',
                '<EMAIL>'
            ],
            );
        $madagascar_contact_us_emails = json_encode($madagascar_contact_us_emails);

        $this->execute("DELETE from tenant_options WHERE tenant_id = 1 AND tenant_key = 'contact_us_emails'");

    //    $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'contact_us_emails', '$zimbabwe_contact_us_emails')");
    //    $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'contact_us_emails', '$liberia_contact_us_emails')");
    //    $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'contact_us_emails', '$madagascar_contact_us_emails')");

    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}