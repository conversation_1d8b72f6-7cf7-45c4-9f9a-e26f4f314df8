<?php

use Phinx\Migration\AbstractMigration;

class AddLatLonToTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
       
        $lat = 6.300774;
        $lon = -10.797160;
        $zoom = 6.75;
        

     //   $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'map', '{\"lat\":$lat,\"lon\":$lon,\"zoom\":$zoom}')");
        


    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
