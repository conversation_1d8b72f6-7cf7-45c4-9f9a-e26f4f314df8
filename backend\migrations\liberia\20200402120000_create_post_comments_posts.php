<?php
// UJEVMS-67 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 02/04/2020.

use Phinx\Migration\AbstractMigration;

class CreatePostCommentsPosts extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('post_comments_posts')
      ->addColumn('post_comment_id', 'integer',  ['null' => true])
      ->addColumn('post_id', 'integer', ['null' => true])
      ->addColumn('created', 'integer', ['default' => 0])
      ->addColumn('updated', 'integer', ['default' => 0])
      ->addForeignKey('post_comment_id', 'post_comments', 'id', [
        'delete' => 'CASCADE',
        'update' => 'RESTRICT',
      ])
      ->addForeignKey('post_id', 'posts', 'id', [
        'delete' => 'CASCADE',
        'update' => 'RESTRICT',
      ])
      ->create();
  }
}
