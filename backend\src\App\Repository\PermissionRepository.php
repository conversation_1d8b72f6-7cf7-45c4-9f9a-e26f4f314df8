<?php

/**
 * <PERSON><PERSON>hidi Permission Repository
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Repository;

use <PERSON>hahidi\Core\Entity;
use <PERSON>hahidi\Core\SearchData;
use <PERSON><PERSON>hidi\Core\Entity\Permission;
use <PERSON><PERSON>hidi\Core\Entity\PermissionRepository as PermissionRepositoryContract;

class PermissionRepository extends OhanzeeRepository implements
    PermissionRepositoryContract
{
    // OhanzeeRepository
    protected function getTable()
    {
        return 'permissions';
    }

    // OhanzeeRepository
    public function getEntity(array $data = null)
    {
        return new Permission($data);
    }

    public function getSearchFields()
    {
        return ['q', /* LIKE name */];
    }

    // Create Repository
    public function create(Entity $entity)
    {
        $permission = $entity->asArray();
           // Create role
        $id = $this->executeInsert($this->removeNullValues($permission));
        return $id;
    }

    // SearchRepository
    public function setSearchConditions(SearchData $search)
    {
        $query = $this->search_query;

        if ($search->q) {
            $query->where('name', 'LIKE', "%" .$search->q ."%");
        }

        return $query;
    }

     public function delete(Entity $entity)
    {
        parent::delete($entity);
        // TODO: Revist post-Kohana
        // This might be better placed in the usecase but
        // given Kohana's future I've put it here
        //$this->emit($this->event, $entity->id, 'delete');
    }

    // UshahidiRepository
    public function exists($permission)
    {
        return (bool) $this->selectCount(['name' => $permission]);
    }
}
