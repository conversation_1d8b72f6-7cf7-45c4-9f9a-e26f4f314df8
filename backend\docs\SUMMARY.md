# Table of contents

* [👋 Welcome \| README](README.md)
* [Code of Conduct](code-of-conduct.md)
* [Contributing \| Getting Involved](contributing-or-getting-involved/README.md)
  * [Add code to Ushahidi](contributing-or-getting-involved/workflow-for-adding-code.md)
  * [Encouraging contribution from non-developers](contributing-or-getting-involved/encouraging-contribution-from-non-developers.md)
* [Frequently Asked Questions](frequently-asked-questions.md)
* [Join the Ushahidi community](get-in-touch.md)
* [Contributors ✨](contributors-to-ushahidi.md)
* [🛣️ The Ushahidi Platform Roadmap](roadmap/README.md)
  * [V2-V3+ Migration tool](roadmap/v2-v3+-migration-tool.md)
* [Privacy and security best practices](untitled/README.md)
  * [Security as a user](untitled/security-as-a-user.md)
  * [Security for deployment admins](untitled/security-for-deployment-admins.md)
  * [Security for deployment hosts](untitled/security-for-deployment-hosts.md)

## Development & Code

* [Development: Overview](development-and-code/getting-started.md)
* [How to get the source code](development-and-code/how-to-get-the-source-code.md)
* [Setup Guides](development-and-code/setup_alternatives/README.md)
  * [Installing for production environments](development-and-code/setup_alternatives/installing-for-production-environments.md)
  * [Development environment with XAMPP](development-and-code/setup_alternatives/xampp.md)
  * [Development environment setup with Vagrant](development-and-code/setup_alternatives/vagrant-setup.md)
  * [\[Client\] Setting up the Platform Client for development](development-and-code/setup_alternatives/setting-up-the-platform-client.md)
  * [Setting up the Pattern Library for development](development-and-code/setup_alternatives/setting-up-the-pattern-library-for-development.md)
  * [\[API  & Client\] Bundled release install](development-and-code/setup_alternatives/platform_release_install.md)
* [Add code to Ushahidi](development-and-code/add-code-to-ushahidi.md)
* [Development process](development-and-code/development-process.md)
* [Coding Standards](development-and-code/coding-standards.md)
* [Track and submit issues in Github](development-and-code/issue-tracking.md)
* [Upgrading Ushahidi](development-and-code/upgrading-ushahidi/README.md)
  * [Upgrading to latest release](development-and-code/upgrading-ushahidi/upgrading-to-latest-release.md)
  * [Upgrading from V3.x.x to V4.x.x](development-and-code/upgrading-ushahidi/upgrading-from-v3.x.x-to-v4.x.x.md)
* [⚙️ Installation Helper‌](development-and-code/installation-helper.md)

## Tech Stack

* [API Documentation](tech-stack/api-documentation.md)
* [Database \| Tables overview](tech-stack/tables-overview.md)
* [Database \| Database Schema Diagram](tech-stack/database-schema-diagram.md)
* [Database \| Table details](tech-stack/table-details.md)
* [📐 Architecture](tech-stack/architecture.md)
* [Use case internals](tech-stack/use-case-internals.md)

## QA & Testing

* [The QA process](qa-and-testing/the-qa-process.md)
* [How to run QA tests](qa-and-testing/how-to-run-qa-tests.md)
* [Defect Management](qa-and-testing/defect-management.md)
* [How to write QA test scripts](qa-and-testing/how-to-write-qa-test-scripts.md)
* [Hotfixes](qa-and-testing/hotfixes.md)

## Front-end development

* [Changing UI styles: introduction to the pattern library](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/README.md)
  * [File-structure](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/pattern-library.md)
  * [Installing new packages](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/installing-new-packages.md)
  * [How to Apply to the Platform](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/how-to-apply-to-the-platform.md)
  * [Using the changed styles in platform-client](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/using-the-changed-styles-in-platform-client.md)
  * [Syntax and Formatting](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/syntax-and-formatting.md)
  * [Grid, Breakpoints, & Media Queries](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/grid-breakpoints-and-media-queries.md)
  * [Variables](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/variables.md)
  * [Mixins](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/mixins.md)
  * [Helpers](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/helpers.md)
  * [Icons](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/icons.md)
  * [Create a New Component from Scratch](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/create-a-new-component-from-scratch.md)
  * [Read Direction](front-end-development/changing-ui-styles-introduction-to-the-pattern-library/read-direction.md)

## Design

* [🎨 Design: overview](design/design-process.md)
* ['Best practice' design](design/best-practice-design.md)
* [Ushahidi Platform 'Sticker Sheet'](design/ushahidi-platform-sticker-sheet.md)
* [User testing process](design/user-testing-process.md)
* [User testing script examples](design/user-testing-script-examples.md)
* [Synthesising user testing results examples](design/synthesising-user-testing-results-examples/README.md)
  * [Synthesis example 1](design/synthesising-user-testing-results-examples/synthesis-example-1.md)
  * [Synthesis example 2](design/synthesising-user-testing-results-examples/synthesis-example-2.md)
  * [Synthesis example 3](design/synthesising-user-testing-results-examples/synthesis-example-3.md)
  * [Synthesis recommendations example 1](design/synthesising-user-testing-results-examples/synthesis-recommendations-example-1.md)
  * [Synthesis recommendations example 2](design/synthesising-user-testing-results-examples/synthesis-recommendations-example-2.md)
* [Open Source Design](design/open-source-design.md)

## Documentation

* [Documentation](documentation/contributing-documentation.md)
* [Contributing docs via GitHub](documentation/contributing-docs-via-github.md)

## Translation

* [Localization and Translation](translation/software-localization-and-translation.md)

## The Ushahidi Platform Facebook bot

* [The Facebook bot](the-ushahidi-platform-facebook-bot/the-facebook-bot/README.md)
  * [Installing the bot](the-ushahidi-platform-facebook-bot/the-facebook-bot/installing-the-bot-for-development-and-testing.md)
  * [The bot script](the-ushahidi-platform-facebook-bot/the-facebook-bot/the-bot-script.md)

## Hackathon and events

* [Installathon, May 2019](hackathon-and-events/installathon-may-2019/README.md)
  * [Welcome to the hackathon!](hackathon-and-events/installathon-may-2019/welcome-to-the-hackathon.md)
* [Write/Speak/Code 2019](hackathon-and-events/write-speak-code-2019.md)
* [Open Design: Bangalore](hackathon-and-events/open-design-bangalore.md)
* [Open Design: Taipei](hackathon-and-events/open-design-taipei.md)

## Enhancement Proposals

* [Exchange Format](enhancement-proposals/exchange-format.md)

