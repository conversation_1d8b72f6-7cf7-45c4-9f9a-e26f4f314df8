<?php

// UCD-31 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 27/10/2020.


use Phinx\Migration\AbstractMigration;

class UpdatePermissionName extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

    $this->execute("
    start transaction;
    
    set FOREIGN_KEY_CHECKS = 0;
    
    update permissions set name = 'Manage Collections, Saved Searches and Messages', description = 'Manage Collections, Saved Searches and Messages'
    where name = 'Manage Collections and Saved Searches';
    insert into roles_permissions (role, permission) values
        ('super', 'Manage Collections, Saved Searches and Messages');
    insert into roles_permissions (role, permission) values
        ('admin', 'Manage Collections, Saved Searches and Messages');
          
    set FOREIGN_KEY_CHECKS = 1;
    
    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
