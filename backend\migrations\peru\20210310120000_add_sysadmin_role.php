<?php

// UJEVMS-69 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 07/09/2020.


use Phinx\Migration\AbstractMigration;

class AddSysadminRole extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $password = password_hash('password', PASSWORD_BCRYPT, ['cost' => 12]);

    $this->execute("
    start transaction;
    
    set FOREIGN_KEY_CHECKS = 0;
    
    insert into roles (name, display_name, description, protected) values 
      ('sysadmin', 'Sys Admin', 'System Admin', 7);

    insert into roles_permissions (role, permission) values
        ('sysadmin', 'access_eview_dashboard')
        , ('sysadmin', 'create_report')
        , ('sysadmin', 'access_incident_risk_reports')
        , ('sysadmin', 'view_incident_risk_reporter_identity')
        , ('sysadmin', 'modify_report_information')
        , ('sysadmin', 'modify_personal_incident_risk_reports')
        , ('sysadmin', 'access_analysis_and_filters_section')
        , ('sysadmin', 'access_and_modify_parameters_settings');

          
    insert into users (email, realname, password, role, created) values
      ('<EMAIL>', 'Sys Admin', '$password', 'sysadmin', UNIX_TIMESTAMP());
    
    set FOREIGN_KEY_CHECKS = 1;
    
    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
