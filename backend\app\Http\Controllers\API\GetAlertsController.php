<?php

namespace Us<PERSON>hidi\App\Http\Controllers\API;

use <PERSON><PERSON>hidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use DB;
use Ushahidi\App\Models\TenantOption;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

/**
 * Ushahidi API Contacts Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class GetAlertsController extends RESTController{

    protected function getResource(){
        return '';
    }

    public function save_alert(Request $request){

        $this->validate($request,[
            'radius' => 'required|integer',
             'email' => 'required|email',
            'latitude' => 'required|string|max:255',
            'longitude' => 'required|string|max:255',
            'categories' => 'required|array',
        ]);

        $alert_exists = DB::table('alerts')
        ->where('email', strtolower($request->email))
        ->where('radius', $request->radius)
        ->where('longitude', $request->longitude)
        ->whereRaw('JSON_CONTAINS(categories, ?)', [json_encode($request->categories)])
        ->where('status', 1)
        ->first();

       if ($alert_exists) {
        $alert_exists_categories =  $alert_exists->categories;
        $request_categories = (array) $request->categories;
                // Convert to sets
        $alert_email_set = array_unique(json_decode($alert_exists_categories));
        $request_categories_set = array_unique($request_categories);
        
        $alert_exists_categories_array = json_decode($alert_exists_categories, true);

        // Check if decoding was successful
        if ($alert_exists_categories_array === null && json_last_error() !== JSON_ERROR_NONE) {
       } else {
       // Sort arrays
         sort($alert_exists_categories_array);
         sort($request_categories_set);

          // Check if both sets are equal
          if ($alert_exists_categories_array === $request_categories_set) {
            throw new \Exception("email Record already exists!");
          }
         }
       }
                   
        $id = DB::table('alerts')->insertGetId([
            'radius' => $request->radius,
            'email' => strtolower($request->email),
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'categories' => json_encode($request->categories),
            'status' => 1,
            'hash' => substr(md5(mt_rand()), 0, 7),
            'created' => time()
        ]);
        if($id){
            return response()->json(["success" => true],200,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }else{
            return response()->json(["success" => false],422,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);
        }
    }

    
    public function unsubscribeEmail($hash, Request $request){

        $flag = $request->query('flag');
   if (isset($flag) && !empty($flag)) {
       $alert = DB::table('alerts')->where('hash', $hash)->first();
       if (!is_null($alert)) {
        DB::table('alerts')->where('hash', $hash)->delete();
        return "Te has dado de baja correctamente.";
    }
}        
        return "Ya estás dado de baja.";
    }
    
}