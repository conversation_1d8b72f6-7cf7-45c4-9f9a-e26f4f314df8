<?php

namespace <PERSON><PERSON>hi<PERSON>\App\Jobs;

use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\Export\Job\PostCount;
use <PERSON><PERSON>hidi\Core\Entity\ExportJob;
use <PERSON>hahidi\Core\Entity\ExportJobRepository;
use Ushahidi\App\Multisite\MultisiteAwareJob;
use DB;
use Illuminate\Support\Facades\Hash;
use Ushahidi\App\Models\Tenant;

class ImportUserCsv extends Job
{
    //use MultisiteAwareJob;
    use RecordsExportJobFailure;

    protected $batchSize;

    public $file;

    protected $jobId;

    protected $tenant_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($file, $jobId, $tenant_id)
    {    
        $this->file = $file;
        $this->jobId = $jobId;
        $this->batchSize = config('media.csv_batch_size', 200);
        $this->tenant_id = $tenant_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(ExportJobRepository $exportJobRepo)
    {   
        
        DB::setDefaultConnection('tenant');
        $tenant = Tenant::find($this->tenant_id);
        if(!is_null($tenant)){
            $tenant->configure()->use();
        }
        
        $job = DB::table('export_job')->where('id', $this->jobId)->update(['status' => ExportJob::STATUS_QUEUED]);
        
        $file       = $this->file;
        $path       = 'storage/app/public/'.$this->file;
        $data       = array_map('str_getcsv',file($path));
        $csv_data   = array_slice($data,1);
        $insUsers   = [];
        $usersExData= [];
        
        $filename   = $path;
        $csvArray   = $this->ImportCSV2Array($filename);

        if(count(array_filter($csvArray)) != 0){
            DB::transaction(function() use($csvArray){
                // $keyMappings = [
                //     'SHORT ROLE NAME' => 'Nom De Rôle Abrégé',
                //     'EMAIL' => 'E-mail',
                //     'REGION' => 'Région',
                // ];
                // $englishHeaders = array_keys($keyMappings);
                // $frenchHeaders = array_values($keyMappings);
                // foreach($csvArray as $key => $userValues){
                //     if(count(array_filter($userValues)) != 0){
                //         $userRole       = 'super';
                //         $headers = array_map('trim', array_keys($userValues));
                //         $isFrenchHeaders = count(array_intersect($headers, $frenchHeaders)) > 0;
                //         if(isset($userValues["SHORT ROLE NAME"]) && !empty($userValues["SHORT ROLE NAME"]) || $isFrenchHeaders> 0){
                //             $roleKey = $isFrenchHeaders ? $keyMappings["SHORT ROLE NAME"] : "SHORT ROLE NAME";
                //             $roles = DB::table('roles')->where('short_name', '=', strtolower($userValues[$roleKey]))->first();
                //             $userRole = isset($roles->name) && !empty($roles->name) ? $roles->name : 'super';
                //         }
                //         $insUsers = $isFrenchHeaders ? [
                //             'realname'  => (isset($userValues["Nom d'utilisateur"]) && !empty($userValues["Nom d'utilisateur"])) ? $userValues["Nom d'utilisateur"] : '',
                //             'email'     => (isset($userValues["E-mail"]) && !empty($userValues["E-mail"])) ? $userValues["E-mail"] : '',
                //             'password'  => (isset($userValues["Mot de passe"]) && !empty($userValues["Mot de passe" ])) ? Hash::make($userValues["Mot de passe"]) : '',
                //             'role'      => $userRole,
                //             'mgmt_lev_1'=> (isset($userValues["Région"]) && !empty($userValues["Région"])) ? $userValues["Région"] : '',
                //             'mgmt_lev_2'=> (isset($userValues["DISTRICT"]) && !empty($userValues["DISTRICT"])) ? $userValues["DISTRICT"] : '',
                //             'mgmt_lev_3'=> (isset($userValues["WARD"]) && !empty($userValues["WARD"])) ? $userValues["WARD"] : '',
                //             'department'=> (isset($userValues["DEPARTMENT"]) && !empty($userValues["DEPARTMENT"])) ? $userValues["DEPARTMENT"] : '',
                //         ] : [
                //             // Define field names for English headers
                //             'realname'  => (isset($userValues["USER NAME"]) && !empty($userValues["USER NAME"])) ? $userValues["USER NAME"] : '',
                //             'email'     => (isset($userValues["EMAIL"]) && !empty($userValues["EMAIL"])) ? $userValues["EMAIL"] : '',
                //             'password'  => (isset($userValues["PASSWORD"]) && !empty($userValues["PASSWORD" ])) ? Hash::make($userValues["PASSWORD"]) : '',
                //             'role'      => $userRole,
                //             'mgmt_lev_1'=> (isset($userValues["REGION"]) && !empty($userValues["REGION"])) ? $userValues["REGION"] : '',
                //             'mgmt_lev_2'=> (isset($userValues["DISTRICT"]) && !empty($userValues["DISTRICT"])) ? $userValues["DISTRICT"] : '',
                //             'mgmt_lev_3'=> (isset($userValues["WARD"]) && !empty($userValues["WARD"])) ? $userValues["WARD"] : '',
                //             'department'=> (isset($userValues["DEPARTMENT"]) && !empty($userValues["DEPARTMENT"])) ? $userValues["DEPARTMENT"] : '',
                //         ];
                
                //         if((!isset($userValues["PASSWORD"])) && !isset($userValues["Mot de passe"])) {
                //             unset($insUsers['password']);
                //         }
                //         try{
                //             $email = $insUsers['email'];
                //             $checkUser = DB::table('users')->where('email', $email)->first();
                //             if($checkUser) {
                //                 DB::table('users')->where('email', $email)->update($insUsers);
                //                 $userInsert = $checkUser->id;     
                //             } else {
                //                 $userInsert = DB::table('users')->insertGetId($insUsers);
                //             }
                //         } catch(QueryException $e){
                //             continue;
                //         }   
                //         $contactData=[];
                //         $TELEPHONE = $isFrenchHeaders
                //         ? data_get($userValues, $isFrenchHeaders ? 'Téléphone' : 'TELEPHONE', '')
                //         : '';
                    
                //        $TELEPHONE2 = $isFrenchHeaders
                //         ? data_get($userValues, $isFrenchHeaders ? 'Téléphone2' : 'TELEPHONE2', '')
                //         : '';

                //         if(isset($TELEPHONE) && !empty($TELEPHONE)){
                //             $temp = [
                //                 'user_id'   => $userInsert,
                //                 'type'      => 'phone',
                //                 'contact'   => str_replace('"','', $TELEPHONE),
                //                 'data_source'   => 'web'
                //             ];
                //             array_push($contactData, $temp);
                //         }
                //         if(isset($TELEPHONE2) && !empty($TELEPHONE2)){
                //             $temp = [
                //                 'user_id'   => $userInsert,
                //                 'type'      => 'phone',
                //                 'contact'   => str_replace('"','', $TELEPHONE2),
                //                 'data_source'   => 'web'
                //             ];
                //             array_push($contactData, $temp);
                //         }

                //         if($contactData && count($contactData) > 0) {
                //             foreach ($contactData as $key => $userValues) {
                //                 $user_id        = $userValues['user_id'];
                //                 DB::table('contacts')->where('user_id', $user_id)->delete();
                //                 if($user_id) {
                //                     // DB::table('contacts')->where('user_id', $user_id)->update($userValues);
                //                     DB::table('contacts')->insert($contactData);
                //                 }
                //             }

                //         }
                //         /*
                //         $usersExData = [
                //             'user_id'       => $userInsert,
                //             'institution'   => (isset($userValues["INSTITUTION" ]) && !empty($userValues["INSTITUTION"  ])) ? $userValues["INSTITUTION" ] : ''
                //         ];
                //         $userExUserId   = $usersExData['user_id'];
                //         $checkUserEx    = DB::table('users_ex')->where('user_id', $userExUserId)->first();
                //         if($checkUserEx) {
                //             DB::table('users_ex')->where('user_id', $userExUserId)->update($usersExData);
                //         } else {
                //             DB::table('users_ex')->insert($usersExData);
                //         }*/
                //     }   
                // }
                foreach($csvArray as $key => $userValues){
                    if(count(array_filter($userValues)) != 0){
                        $userRole       = 'super';
                        if(isset($userValues["User role abbreviation"]) && !empty($userValues["User role abbreviation"])){
                            $roles = DB::table('roles')->where('short_name','=',strtolower(trim($userValues["User role abbreviation"])))->first();
                            if(!(isset($roles->name) && !empty($roles->name))) {
                                continue;
                            }
                            $userRole = (isset($roles->name) && !empty($roles->name)) ? $roles->name : 'super';
                        }
                        $insUsers = [
                            'realname'  => (isset($userValues["Name"]) && !empty($userValues["Name"])) ? trim($userValues["Name"]) : '',
                            'email'     => (isset($userValues["Email"]) && !empty($userValues["Email"])) ? trim($userValues["Email"]) : '',
                            'password' => (isset($userValues["Password"]) && !empty($userValues["Password" ])) ? Hash::make(trim($userValues["Password"])) : '',
                            'role'      => $userRole,
                            'mgmt_lev_1'=> (isset($userValues["Region"]) && !empty($userValues["Region"])) ? strtoupper(trim($userValues["Region"])) : '',
                            'mgmt_lev_2'=> (isset($userValues["DISTRICT"]) && !empty($userValues["DISTRICT"])) ? $userValues["DISTRICT"] : '',
                            'mgmt_lev_3'=> (isset($userValues["WARD"]) && !empty($userValues["WARD"])) ? $userValues["WARD"] : '',
                            'department'=> (isset($userValues["DEPARTMENT"]) && !empty($userValues["DEPARTMENT"])) ? $userValues["DEPARTMENT"] : '',
                            'organization' => (isset($userValues["Organization"]) && !empty($userValues["Organization"])) ? trim($userValues["Organization"]) : '',
                            'position' => (isset($userValues["Position"]) && !empty($userValues["Position"])) ? trim($userValues["Position"]) : '',
                        ];
                        if((!isset($userValues["Password"]))) {
                            unset($insUsers['password']);
                        }
                        try{
                            $email = $insUsers['email'];
                            $checkUser = DB::table('users')->where('email', $email)->first();
                            if($checkUser) {
                                DB::table('users')->where('email', $email)->update($insUsers);
                                $userInsert = $checkUser->id;     
                            } else {
                                $userInsert = DB::table('users')->insertGetId($insUsers);
                            }
                        } catch(QueryException $e){
                            continue;
                        }   
                        $contactData=[];
                        if(isset($userValues["Phone"]) && !empty($userValues["Phone"])){
                            $temp = [
                                'user_id'   => $userInsert,
                                'type'      => 'phone',
                                'contact'   => str_replace('"','', trim($userValues["Phone"])),
                                'data_source'   => 'web'
                            ];
                            array_push($contactData, $temp);
                        }
                        // if(isset($userValues["Phone 2"]) && !empty($userValues["Phone 2"])){
                        //     $temp = [
                        //         'user_id'   => $userInsert,
                        //         'type'      => 'phone',
                        //         'contact'   => str_replace('"','', trim($userValues["Phone 2"])),
                        //         'data_source'   => 'web'
                        //     ];
                        //     array_push($contactData, $temp);
                        // }

                        if($contactData && count($contactData) > 0) {
                            foreach ($contactData as $key => $userValues) {
                                $user_id        = $userValues['user_id'];
                                DB::table('contacts')->where('user_id', $user_id)->delete();
                                if($user_id) {
                                    // DB::table('contacts')->where('user_id', $user_id)->update($userValues);
                                    DB::table('contacts')->insert($contactData);
                                }
                            }

                        }
                        /*
                        $usersExData = [
                            'user_id'       => $userInsert,
                            'institution'   => (isset($userValues["INSTITUTION" ]) && !empty($userValues["INSTITUTION"  ])) ? $userValues["INSTITUTION" ] : ''
                        ];
                        $userExUserId   = $usersExData['user_id'];
                        $checkUserEx    = DB::table('users_ex')->where('user_id', $userExUserId)->first();
                        if($checkUserEx) {
                            DB::table('users_ex')->where('user_id', $userExUserId)->update($usersExData);
                        } else {
                            DB::table('users_ex')->insert($usersExData);
                        }*/
                    }   
                }
            });
            $job = DB::table('export_job')->where('id', $this->jobId)->update(['status' => ExportJob::STATUS_SUCCESS]);
        }
    }

    public function ImportCSV2Array($filename)
    {
        $row = 0;
        $col = 0;
    
        $handle = @fopen($filename, "r");
        if ($handle) {
            while (($row = fgetcsv($handle, 4096)) !== false) {
                $row = preg_replace('/^\xEF\xBB\xBF/', '', $row);
                if (empty($fields)) {
                    $fields = $row;
                    continue;
                }
                foreach ($row as $k=>$value) {
                    if (isset($fields[$k])) {
                        $results[$col][$fields[$k]] = $value;
                    }
                }
                $col++;
                unset($row);
            }
            if (!feof($handle)) {
                echo "Error: unexpected fgets() failn";
            }
            fclose($handle);
        }
    
        return $results;
    }
}