<?php

require_once __DIR__ . '/../vendor/autoload.php';

try {
    (new Dotenv\Dotenv(__DIR__ . '/../'))->load();
} catch (Dotenv\Exception\InvalidPathException $e) {
    //
}

use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\Filesystem;
use Ushahidi\Core\Tool\UploadData;
use Ushahidi\Core\Tool\FileData;

// Ushahidi: load transitional code
require_once __DIR__ . '/../src/Init.php';

$app = require __DIR__ . '/lumen.php';

// UJEVMS-100 - SCALA Luca, <EMAIL> - 17/03/2020.
$app->middleware([
    Ushahidi\App\Http\Middleware\CORS::class
]);
$app->singleton(
    Illuminate\Contracts\Filesystem\Factory::class,
    function ($app) {
        return new Illuminate\Filesystem\FilesystemManager($app);
    }
);

$key = env('STORAGE_TYPE');
if($key == "local") {
    $json = new \Monolog\Formatter\JsonFormatter();
    $stdouthandler = new \Monolog\Handler\StreamHandler('php://stdout', 'info');
    $stdouthandler->setFormatter($json);
    Log::pushHandler($stdouthandler);
}


// if($key !== "local") {
// $s3Handler = new class extends \Monolog\Handler\AbstractProcessingHandler {
//     protected function write(array $record): void
//     {   

//         // Log data and filename
//         $logData = $this->getFormatter()->format($record);
//         $filename = 'logs/' . date('Y/m/d/') . $record['datetime']->format('Y-m-d-H-i-s') . '.log';

//         // Exponential backoff parameters
//         $retryCount = 0;
//         $maxRetries = 5;

//         do {
//             try {
//                 // Upload log data to S3
//                 $path = Storage::disk('s3')->put($filename, $logData);


//                 // Break the loop if successful
//                 break;
//             } catch (AwsException $e) {
//                 if ($e->getStatusCode() == 503 && $retryCount < $maxRetries) {
//                     // Exponential backoff: Wait for 2^$retryCount seconds
//                     sleep(pow(2, $retryCount));
//                     $retryCount++;
//                 } else {
//                     // Handle other exceptions or break the loop for non-retryable errors
//                     break;
//                 }
//             }
//         } while (true);
//     }
// };

// //// // Log::pushHandler($s3Handler);
// // }

return $app;
