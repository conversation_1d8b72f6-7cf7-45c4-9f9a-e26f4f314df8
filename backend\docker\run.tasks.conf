platform.artisan.queue_listen.service: {
	type: simple,
	directory: "/var/www",
	command: "./artisan queue:listen",
	enabled: "$(ENABLE_QUEUE_LISTEN:-false)",
	restart: true
}

platform.datasource.outgoing.service: {
	type: cron,
	directory: "/var/www",
	command: "./artisan datasource:outgoing",
	interval: "* * * * */5",
	enabled: "$(ENABLE_PLATFORM_TASKS:-false)"
}

platform.datasource.incoming.service: {
	type: cron,
	directory: "/var/www",
	command: "./artisan datasource:incoming",
	interval: "* * * * */5",
	enabled: "$(ENABLE_PLATFORM_TASKS:-false)"
}

platform.savedsearch.service: {
	type: cron,
	directory: "/var/www",
	command: "./artisan savedsearch:sync",
	interval: "* * * * */5",
	enabled: "$(ENABLE_PLATFORM_TASKS:-false)"
}

platform.notification.queue.service: {
	type: cron,
	directory: "/var/www",
	command: "./artisan notification:queue",
	interval: "* * * * */5",
	enabled: "$(ENABLE_PLATFORM_TASKS:-false)"
}

platform.webhook.send.service: {
	type: cron,
	directory: "/var/www",
	command: "./artisan webhook:send",
	interval: "* * * * */5",
	enabled: "$(ENABLE_PLATFORM_TASKS:-false)"
}

platform.artisan.schedule.service: {
	type: cron,
	directory: "/var/www",
	command: "./artisan schedule:run",
	interval: "* * * * *",
	enabled: "$(ENABLE_QUEUE_LISTEN:-false)",
}

