<?php

/**
 * Us<PERSON><PERSON>di CountryCode Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use Ushahidi\Core\Entity\User;
use Us<PERSON>hidi\Core\Entity\UserRepository;
use Ushahidi\Core\Entity\Permission;
use Ushahidi\Core\Tool\Authorizer;
use Ushahidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Tool\Permissions\AclTrait;

class CountryCodeAuthorizer implements Authorizer
{
    // The access checks are run under the context of a specific user
    use UserContext;

    // It uses `AdminAccess` to check if the user has admin access
    use AdminAccess;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;

    // Check that the user has the necessary permissions
    // if roles are available for this deployment.
    use AclTrait;

    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {
        // These checks are run within the `User` context.
        $user = $this->getUser();

        // Only read and search-usecases can be performed on country-codes
        if ($privilege === 'read' || $privilege === 'search') {
            // Allow role with the right permissions to do everything else
            // $this->acl->hasPermission($user, Permission::MANAGE_SETTINGS)
            if ($this->acl->hasPermission($user, Permission::ACCESS_AND_MODIFY_PARAMETERS_SETTINGS)) {
                return true;
            }

            // If a user has the 'admin' role, they can do pretty much everything else
            if ($this->isUserAdmin($user)) {
                return true;
            }
        }

        // If no other access checks succeed, we default to denying access
        return false;
    }
}
