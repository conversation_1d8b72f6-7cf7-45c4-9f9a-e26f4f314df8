<?php

use Phinx\Migration\AbstractMigration;

class AddObserverChecklistConstituencyWardPollingTags extends AbstractMigration
{

    /**
     * Migrate Up. To add new attributes to the observer checklist form and also constituency tags
     */
    public function up()
    {


        include 'observer_checklist_form_attributes_polling_stations.php';
        include 'observer_checklist_form_attributes_wards.php';
//        include 'observer_checklist_form_attributes_constituencies.php';

$constituency_array = [
"BLantyre Central", "Balaka Bwaila", "Balaka Mulunguzi", "Balaka Ngwangwa", "Balaka Rivirivi", "Balaka Ulongwe", "Blantyre City Chichiri-Misesa", "Blantyre City Chigumula BCA-Club Banana", "Blantyre City Chilomoni Kabula Nancholi", "Blantyre City Mapanga-Mpingwe-Mzedi", "Blantyre City Mbayani- Mussa Magasa", "Blantyre City Michiru-Chirimba", "Blantyre City Ndirande Malabada", "Blantyre City Nkolokoti-Ndirande Matope", "Blantyre City Soche-Zingwangwa", "Blantyre City South Lunzu", "Blantyre North", "Blantyre North East", "Blantyre South East", "Blantyre South West", "Blantyre West", "Chikwawa Central", "Chikwawa Central West", "Chikwawa East", "Chikwawa Mkombedzi", "Chikwawa North", "Chikwawa South", "Chikwawa West", "Chiradzulu Masanjala", "Chiradzulu Midima", "Chiradzulu Nguludi", "Chiradzulu Nyungwe", "Chiradzulu Thumbwe", "Chitipa Central", "Chitipa Chendo", "Chitipa East", "Chitipa North", "Chitipa South", "Dedza Boma", "Dedza Chikoma", "Dedza Dzalanyama", "Dedza Golomoti", "Dedza Kasina", "Dedza Linthipe", "Dedza Mayani", "Dedza Mlunduni", "Dedza Mphunzi", "Dedza Mtakataka", "Dowa Central", "Dowa Central East", "Dowa East", "Dowa Kasangazi", "Dowa Mndolera", "Dowa Mphudzu", "Dowa Ngala", "Dowa North East", "Dowa South East", "Dowa West", "Karonga Central", "Karonga Lufilya", "Karonga Nyungwe", "Karonga Songwe", "Karonga South", "Karonga Town", "Kasungu Central", "Kasungu East", "Kasungu Municipal", "Kasungu North", "Kasungu North East", "Kasungu North North East", "Kasungu North West", "Kasungu South", "Kasungu South East", "Kasungu South West", "Kasungu West", "Likoma", "Lilongwe Bunda", "Lilongwe Central", "Lilongwe Chilobwe", "Lilongwe Chiwamba", "Lilongwe City Bwaila", "Lilongwe City Centre", "Lilongwe City Chipala-Nafisi", "Lilongwe City Dzenza", "Lilongwe City Kamphuno", "Lilongwe City Lumbadzi", "Lilongwe City Masintha", "Lilongwe City Mlodza", "Lilongwe City Mtandire-Mtsiliza", "Lilongwe City M\'buka", "Lilongwe City Nankhaka", "Lilongwe City Ngwenya", "Lilongwe Demera", "Lilongwe East", "Lilongwe Likuni", "Lilongwe Machenga", "Lilongwe Mapuyu South", "Lilongwe Mapuyu North", "Lilongwe Mapuyu South", "Lilongwe Mpenu", "Lilongwe Mphande", "Lilongwe Msinja North", "Lilongwe Msinja South", "Lilongwe Msozi", "Lilongwe Mude", "Lilongwe Nkhoma", "Lilongwe Nyanja", "Lilongwe Phirilanjuzi", "Luchenza Municipal", "Machinga Central", "Machinga Central East", "Machinga East", "Machinga Likwenu", "Machinga Mikoko", "Machinga North East", "Machinga South", "Machinga South East", "Mangochi Central", "Mangochi East", "Mangochi Lutende", "Mangochi Malombe", "Mangochi Masongola", "Mangochi Monkey Bay", "Mangochi Municipal", "Mangochi Nkungulu", "Mangochi North", "Mangochi North East", "Mangochi South", "Mangochi South West", "Mangochi West", "Mchinji Central East", "Mchinji East", "Mchinji North", "Mchinji North East", "Mchinji South", "Mchinji South West", "Mchinji West", "Mulanje Bale", "Mulanje Central", "Mulanje Limbuli", "Mulanje North", "Mulanje Pasani", "Mulanje South", "Mulanje South East", "Mulanje South West", "Mulanje West", "Mwanza Central", "Mwanza West", "Mzimba Central", "Mzimba East", "Mzimba Hora", "Mzimba Kafukule", "Mzimba Luwerezi", "Mzimba North", "Mzimba North East", "Mzimba Perekezi", "Mzimba Solola", "Mzimba South", "Mzimba South East", "Mzimba South West", "Mzimba West", "Mzuzu City North", "Mzuzu City South East", "Mzuzu City South West", "Neno East", "Neno North", "Neno South", "Nkhata Bay Central", "Nkhata Bay Chintheche", "Nkhata Bay Mpamba", "Nkhata Bay North", "Nkhata Bay South", "Nkhata Bay West", "Nkhotakota Central", "Nkhotakota Chia", "Nkhotakota Dwangwa", "Nkhotakota Liwaladzi", "Nkhotakota Mkhula", "Nsanje Central", "Nsanje Lalanje", "Nsanje North", "Nsanje South", "Nsanje South West", "Ntcheu Bwanje", "Ntcheu Central", "Ntcheu Central Central East", "Ntcheu Central East", "Ntcheu Dzonzi-Mvai", "Ntcheu North", "Ntcheu North West", "Ntcheu South", "Ntchisi Central East", "Ntchisi East", "Ntchisi North", "Ntchisi South", "Ntchisi West", "Phalombe East", "Phalombe Machemba", "Phalombe North", "Phalombe North East", "Phalombe South", "Rumphi Central", "Rumphi East", "Rumphi North", "Rumphi West", "Salima Central", "Salima Central East", "Salima Central West", "Salima North", "Salima South", "Salima South Linthipe", "Thyolo Bvumbwe-Masenjere", "Thyolo Central", "Thyolo Goliati", "Thyolo Khonjeni-Mangunda", "Thyolo Masambanjati", "Thyolo Mikolongwe", "Thyolo Thava", "Thyolo Thekerani", "Zomba Changalume", "Zomba Chikomwe", "Zomba Chingale", "Zomba City North", "Zomba City South", "Zomba Likangala", "Zomba Malosa", "Zomba Matiya", "Zomba Nsondole", "Zomba Ntonya", "Zomba Thondwe",  
 ];

     
          $tag_string = '';    
          $tag_string = $this->getTagString('constituency', $constituency_array);
          $tag_string =  rtrim($tag_string, ",");
      
          $this->execute("
            start transaction;
      
            -- Setting tags.
            set FOREIGN_KEY_CHECKS = 0;
      
                          INSERT INTO tags (tag, slug, type) values " . $tag_string . ";
      
            set FOREIGN_KEY_CHECKS = 1;
      
            commit;
            ");
      
            $tag_string = '';    
            $tag_string = $this->getTagString('ward', $wards_array);
            $tag_string =  rtrim($tag_string, ",");
        
            $this->execute("
              start transaction;
        
              -- Setting tags.
              set FOREIGN_KEY_CHECKS = 0;
        
                            INSERT INTO tags (tag, slug, type) values " . $tag_string . ";
        
              set FOREIGN_KEY_CHECKS = 1;
        
              commit;
              ");

              $tag_string = '';    
              $tag_string = $this->getTagString('polling-station', $polling_station_array);
              $tag_string =  rtrim($tag_string, ",");
          
              $this->execute("
                start transaction;
          
                -- Setting tags.
                set FOREIGN_KEY_CHECKS = 0;
          
                              INSERT INTO tags (tag, slug, type) values " . $tag_string . ";
          
                set FOREIGN_KEY_CHECKS = 1;
          
                commit;
                ");
                  
      
        }
      
      public function getTagString($key, $array) {
         $tag_string = '';
      
        foreach ($array as $value) {
            $str_value = str_replace(" ", "-", str_replace("'", "" , str_replace("(", "" , str_replace(")", "", strtolower($value)))));
            $tag_string .= "('" . addslashes($value) . "', '" . $str_value . "', '". $key . "'),";
      }
      
      return $tag_string;
          
      }

      /**
     * Migrate Down.
     */
    public function down()
    {
        // Noop - too risky to delete a post type
    }
}
