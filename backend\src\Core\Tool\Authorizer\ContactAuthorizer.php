<?php

/**
 * <PERSON><PERSON><PERSON>di Contact Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use <PERSON>hahidi\Core\Tool\Authorizer;
use Us<PERSON>hidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\OwnerAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Traits\PrivateDeployment;
use Ushahidi\Core\Tool\Permissions\AclTrait;
use Ushahidi\Core\Entity\Permission;

class ContactAuthorizer implements Authorizer
{
    // The access checks are run under the context of a specific user
    use UserContext;

    // It uses methods from several traits to check access:
    // - `OwnerAccess` to check if a user owns the contact
    // - `AdminAccess` to check if the user has admin access
    use AdminAccess, OwnerAccess;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;

    // It uses `PrivateDeployment` to check whether a deployment is private
    use PrivateDeployment;

    use AclTrait;

    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {
        // These checks are run within the user context.
        $user = $this->getUser();

        // Only logged in users have access if the deployment is private
        if (!$this->canAccessDeployment($user)) {
            return false;
        }

        if($this->acl->hasPermission($user, Permission::ACCESS_AND_MODIFY_PARAMETERS_SETTINGS)) {
            return true;
        }

        // Then we check if a user has the 'admin' role. If they do they're
        // allowed access to everything (all entities and all privileges)
        if ($this->isUserAdmin($user)) {
            return true;
        }

        // Allow create, read and update if owner.
        // Contacts should not be deleted.
        if ($this->isUserOwner($entity, $user)
            and in_array($privilege, ['create', 'read', 'update'])) {
            return true;
        }

        // Logged in users can search contacts
        if ($user->getId() and in_array($privilege, ['search'])) {
            return true;
        }

        // If no other access checks succeed, we default to denying access
        return false;
    }
}
