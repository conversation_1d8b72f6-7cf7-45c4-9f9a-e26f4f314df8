<?php

// Permissions

// UJEVMS-69 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 07/06/2020.

$router->group([
    'prefix' => 'permissions',
    'middleware' => ['scope:roles', 'expiration']
], function () use ($router) {
    // Public access
    resource($router, '/', 'PermissionsController', [
        'only' => ['index', 'show'],
    ]);

    // Restricted access
    resource($router, '/', 'PermissionsController', [
        'middleware' => ['auth:api'],
        'only' => ['store', 'update', 'destroy'],
    ]);
});

// resource($router, 'permissions', 'PermissionsController', [
//     'middleware' => ['auth:api', 'scope:permissions', 'expiration'],
//     'only' => ['index', 'show'],
// ]);

