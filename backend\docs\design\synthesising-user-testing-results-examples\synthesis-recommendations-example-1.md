# Synthesis recommendations example 1

## HDX Usertesting Insights

We tested on 8 individuals - 5 on very first early concepts, 2 on a further developed IA logic and 1 on the process of using the API. We have 2 further test scripts to conduct and we aim for 5 testers for each script. We have 17 more people to test on over the rest of the year. The final test script will be on heavy Ushahidi users and those that are very familiar with working with complex datasets. Ideally, they will also have prior knowledge of HDX.

General thoughts from User testers:

Users don’t read text. They skim it at best.

Tagging = Labelling people and/or pictures, Keywords, Anchors for making data easier/better to search, Grouping items into an identifiable cohort, Putting things in buckets related to each other and categorised. In maybe more than one bucket with a goal in mind to retrieve, filtering, User driven tagging. Categories. A useful way of sorting data.

One user talked about targeted tags. The idea that you can tag to ‘raise’ the profile of a certain post and/or data.

Uses tags in analytics to categorise different things in order to make ‘predictions’ about ‘trends’

Where? = Blog posts, facebook, wordpress, Evernote, social media, facebook, coding, Tags used in a governance process, Twitter, Instagram, Sharepoint information management reports, finder of mac, NHS internal systems.

HXL = _“A fancy name for tags?”_

HXL = A way to tag goings on in the world. Events/Places/Time.

_“What is hxl used in?”_ - This user assumed it is a language used in other systems.

User starts describing this as a ‘multidimensional database’

_“What’s the difference between tags and attributes?”_

Tags = tag names

Attributes = a data format

Suggests it might be something about rows and columns or the relation of fields to tags.

No idea what attributes means in this context. The term ‘attribute’ was generally confusing while ‘tag’ was not. The use of the ‘+’ was slightly confusing for some users whereas the ‘\#’ made sense \(especially to coders\) the ‘+’ made much more sense when the user saw the ‘tag preview’ device:

![](https://lh4.googleusercontent.com/bGwNHLEMrhoahzPKh_fKbnD7gEs8puB4xQBHH45gDq-LSK0nLa9RVlry3e2XI8sDieAc3CyJnofpFqbWmdRloOJsTTX_O5l4gYhgX2OvFpSC1l0tIJLfFQ4tIIx7VTICX-vECfUI)

One user expected an underscore or a hyphen.

One user refers to the tag now as a ‘supertag’ because it’s grown.

Says that the \# and + are confusing and that + looks like a letter \(t\) so they found it hard to read down the list.

There are some descriptions but not all of them have them and therefore they are unsure on some of the list items.

One task I asked users to complete was after exporting, could they remember all the tags they had selected to be exported and what they mean’t. Many users did not remember what all the tags mean if they aren’t descriptive. This was especially difficult for the users that spoke English as a second/third/fourth language

A lot of confusion over the \#admin tags too. 

User has no idea what ‘virulance’ means

_“Admin term is confusing. Does loc mean location?”_ - This user didn’t understand why location was abbreviated.

_“What is idps? Or iso? Start and end? Does that mean someone dying? Birth and death?!”_

Plenty of people thought that ‘HDX’ was a file format.

Has no idea what export to HDX will do. Say’s it must be a file format but is worried about it ‘uploading somewhere’ Users that are not 

_“Can the programs/software I open the hxl tagged csv in handle the mapping/tags I added?”_ A fair few users though you needed ‘special software’ to open a file with hxl tags added.

Users automatically expected the tags they are using to be shared with other team members on the same system \(Ushahidi\) and the custom tags to als be shared.

When reading HDX/HXL documentation

85-90% of user tester want to read documentation provided on Ushahidi via blue ‘exit’ link but many of them fear ‘losing their place’ or ‘getting lost’. Most users expect the Ushahidi system to ‘teach’ them in some way. The majority of users said they would spend multiple hours going through the documentation before attempting the export/upload within Ushahidi. 

There is a reluctance to ‘have a go’ by uploading/exporting data.

HXL \(After viewing documentation\) = Sophisticated way of describing data

_“Now I have to read all of this in order to understand!”_

_“Wow - lots of information. It doesn’t look simple”_

_“Very high-level”_

User described feeling ‘down’ and this info being ‘beyond them’ and ‘disempowered’

Has raised more questions than it has answered.

_“This is wordy…”_

_“It’s too much”_ - after scrolling a bit

User talks about this not seeming like it’ll be relevant to their specific tags for their data.

Wants a help section like ‘how to tag’

User calls the hdx sites ‘tech spec’ rather than help for a user.

_“This is documentation rather than training materials”_

User says it looks like xml and some kind of developer standard.

Wonders if ‘postcard’ is a technical term

Sees term ‘JSON’ - confirms a software-y type of info

When Investigating Ushahidi’s HDX integration

Users want to see tagged CSV example files

Custom tags and attributes were tested in every user testing scenario. Users were asked _“If you couldn’t find what ‘tag’ you were looking for to describe your data in the drop down list, what would you look for or what would you expect to be able to do?”_

Most users found the \#x\_ or +custom confusing but mostly due to not knowing ‘what will happen to that tag’ after the dataset has been uploaded or exported. Will it save in the Ushahidi system? What if there’s already a default tag/attribute that is most used for that? This backs up users wanting to read documentation and understand the ‘best practice’ on how to adhere to the standard.

_“Why custom? I don’t need this”_ - one user expected all tags to be in the suggested list and couldn’t understand why there was the ability to create a custom one.

Potential function for users in Ushahidi to suggest you custom tag or attribute to be added to the hxl standard.

We suspect a high volume of ‘\#x\_survey’ tag or ‘+survey’ attribute due to the fact that Ushahidi platform is seen as a surveying tool mostly. We may even see users creating custom tags ‘\#x\_surveyhealth’ or ‘\#x\_surveyjobs’. We intend to user test this.

_“If I can only choose one tag, which is best to use? I’m not sure...”_ Users want to know how flexible and dynamic the tagging system can be.

_“Am I getting the right data?”_ users are concerned they are ‘getting it right’

User wonders if once in HDX it looks like a CSV? Users that have not yet investigated datasets in HDX wonder what it looks like.  


### **API config**

When setting up the API this user assumed HDX = Brand name of API. User describes wanting to click the ‘guide’ link right away and being disappointed by the information given. User says they would google the answer, look for a forum or ask a developer friend.

_“This gives me two confusing answers and I don’t know which to use!”_

_“It’s not helpful and the heading is misleading. This is geared towards experienced tech people”_

There is actually guiding text on Ushahidi’s side here but this user did this: 

\(On the HDX homepage\) User describes that they would look through the ‘quick links’ section for information on API keys

Then then move on to the ‘data’ section.

_“I really have no clue where to look. I’m actually looking for a ‘dashboard’ or something like that”_

User describes easily missing the profile account name on the HDX website. Describes that if they’d gone through the process of logging in it’d be more clear.

User would go to ‘dashboard’ NOT ‘profile’ but find the API key when clicking around.

## HDX Dev insights

\[Platform API\] HDX slug creation should use a library and include the org name \#2973

[https://github.com/ushahidi/platform/issues/2973](https://github.com/ushahidi/platform/issues/2973)

## HDX Datasets should be created and validated when the user requests an export job \#3108

[https://github.com/ushahidi/platform/issues/3108](https://github.com/ushahidi/platform/issues/3108)

Essentially when a user has finished tagging/attributing a dataset we take them to a page where there is some essential information required before the dataset can be uploaded to HDX.

![](https://lh4.googleusercontent.com/_foCd9Kh0sqClOEhvbOzuacJwD78DOQ6zF7WbUAgsLdnLRm6pF7EH7io1nnAqaRx_0CbvY5CiMcBHhXaZx9Ks3lkIiCY1scrAmyEMgh2KsDaJ3p7Wc0xlMJQuFG2LFDTubtq_6BI)

## HDX M&E insights

When talking with Hazel \(M&E at Ushahidi\) interest in ‘notifying’ the wider development space was expressed. Not only it being available and searchable but ‘broadcastable’

Using the same ‘ids’ or being able to merge the language/conventions already used within platform or have it seamlessly port over was of critical concern.

Having a process to which the wider development network could ‘validate’ the data to lend it credibility within the wider development network was important for grassroots orgs and those M&E professionals who work alongside grassroots orgs. 

There was concern around current projects being heavy on the qualitative or ‘open-ended’ responses which could be hard to tag. They are currently hard to draw insight from within the current Ushahidi platform but if hxl tagging can improve the way that these qualitative insights are used then that would be great. These often require prior context. We could get a data point that simply says ‘I hate my job’ but this is in the context of receiving training, further education and certificates for a healthcare program that aims to provide better lifestyles for young girls.

This follows on to a concept such as ‘theming’ for M&E workers so that they can tag and draw down the same data but across different themes. They expressed the interest in just downloading a ‘theme’ and the tags/attributes associated with it and not necessarily the accompanying data. Also the ability to download just the number of data points \(or posts in Ushahidi platform\) alongside the tagged information was important.

Are current platform runs on ‘categories’ and ‘collections’ tags and attributes are a more sophisticated form of this. Our M&E staff use the categories often to inform the surveys that are sent out using the platform \(to collect data\) and then cyclically build more categories when the initial set is seen as insufficient. The ability to consistently rework the data and way that it is described/tagged is key to those drawing insights for reporting to ‘higher powers’   


