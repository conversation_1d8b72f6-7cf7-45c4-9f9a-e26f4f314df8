<?php

namespace <PERSON><PERSON><PERSON><PERSON>\App\Providers;

use <PERSON><PERSON>\Lumen\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        '<PERSON><PERSON><PERSON><PERSON>\App\Events\SendToHDXEvent' => [
            'Us<PERSON>hi<PERSON>\App\Listeners\SendToHDXEventListener',
        ],
    ];

    /*
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        '<PERSON>hahidi\App\Subscriber',
    ];
}
