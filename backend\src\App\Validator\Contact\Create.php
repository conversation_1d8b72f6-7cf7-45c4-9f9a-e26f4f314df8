<?php

/**
 * <PERSON><PERSON><PERSON>di Contact Validator
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Validator\Contact;

class Create extends Update
{
    protected function getRules()
    {
        return array_merge_recursive(parent::getRules(), [
            'type' => [
                ['not_empty'],
            ],
            'contact' => [
                ['not_empty'],
            ]
        ]);
    }
}
