# Synthesis example 2

## Subject 1 - HDX Export Prototype Test Notes

**Test Date:** 20th April 2018

**User:** General user, works as a web designer, relatively new to the platform. 

----------------------------------------------------------------------------------

**Task 1:** Previous experience with ‘tagging’ \(no screen required\)

**Scenarios:** 

1. Ask the users to describe what they think when they here the term ‘tagging’ or ‘labelling’ means in relation to websites, digital products, software etc.
2. Ask users when they have previously had experience of ‘tagging’. What was the nature of the content? Was there any extraction of data afterwards? Whether there was anything they wish they could have done that they couldn’t do. 

<table>
  <thead>
    <tr>
      <th style="text-align:left">Scenario</th>
      <th style="text-align:left">Observation</th>
      <th style="text-align:left">Category</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="text-align:left">1</td>
      <td style="text-align:left">
        <p>Labelling people and/or pictures.</p>
        <p>Keywords.</p>
        <p>Anchors for making data easier/better to search.</p>
      </td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">2</td>
      <td style="text-align:left">Blog posts, facebook, wordpress, Evernote (used for personal notes and
        research for projects)</td>
      <td style="text-align:left">#Activity</td>
    </tr>
  </tbody>
</table>**Task 2:** Locating in the information architecture \(Prototype needed - [https://adobe.ly/2vmIZJo](https://adobe.ly/2vmIZJo)\) 

The prototype \(URL\) opens on Settings view.

**Scenarios:**

1. Can you show me where you’d look to download CSV spreadsheet files?
2. \(On Export page with 3 CTAs\) Without clicking, can you explain to me what options you think are available from here?
3. What would you need to do in order to feel comfortable choosing one of the three buttons?
4. Can you tell me what you expect ‘hxl tags or attributes’ to be? Don’t worry if you don’t know, we’re looking for your best guess if you’ve never heard of them before.
5. If you haven’t already, click a blue, underlined link.
6. Were you expecting a page like this? If not how does it differ from your expectations?
7. Spend a minute or so reading the information here. \[Pause\] Can you give me a summary of your understanding of hxl tags. \(There are no wrong answers!\)
8. How would you get back to where you were?
9. If you haven't already, please click the back button \(you may need to click a few times on a ‘real’ website.
10. Imagine you want your volunteer data to include hxl tags. 

\(We can briefly explain here that hxl tags are a way of ‘assigning’ a ‘term’ to a piece of information. Use Justin’s analogy of Instagram/Twitter hashtags!\)  


Which option would you choose? \(they can click at this point\)  


1. As you choose this option, what are you thinking? What is your first impression of this page? \(Should be on the first view of the export table\)

<table>
  <thead>
    <tr>
      <th style="text-align:left">Scenario</th>
      <th style="text-align:left">Observation</th>
      <th style="text-align:left">Category</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="text-align:left">1</td>
      <td style="text-align:left">Export data - easily found in settings page</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">2</td>
      <td style="text-align:left">
        <p>User looks at top paragraph of text but only &#x2018;sees&#x2019; the
          blue links. Is irritated because they &#x2018;will take them away for the
          page&#x2019; and they don&#x2019;t want to.</p>
        <p>User assumes they are already locked into an &#x2018;export&#x2019; and
          fears losing progress already.
          <br />
        </p>
        <p>User thought they may get a tooltip hover over but didn&#x2019;t so confirm
          their suspicions around &#x2018;exit&#x2019; links.
          <br />
          <br />
        </p>
        <p>Export all = Main button, will give them everything
          <br />
        </p>
        <p>Select fields = Select and choose categories
          <br />
        </p>
        <p>Export to HDX = Specialised option. &#x201C;I don&#x2019;t know what it
          is - is it a data structure? Or a fancy name for tags?&#x201D; Also later
          says it may be &#x2018;all data but with tags and attributes included&#x2019;
          <br
          />
        </p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
          <br />
          <br />
          <br />
        </p>
        <p>#Activity
          <br />
          <br />
          <br />
        </p>
        <p>#Activity
          <br />
          <br />
          <br />
        </p>
        <p>#Quote</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">3&amp;4</td>
      <td style="text-align:left">
        <p>Assume hxl means = sort data and search pre-tagged data.</p>
        <p>List of best tags or official list of tags.</p>
        <p>No idea what attributes means in this context. User says they need more
          info.
          <br />
        </p>
        <p>User says they would feel most comfortable choosing &#x2018;Select fields&#x2019;
          <br
          />
        </p>
        <p>User makes a comment on the placement of buttons being odd. Assumes that
          &#x2018;Export all&#x2019; and &#x2018;Export with hxl&#x2026;&#x2019;
          should be closer together.</p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
          <br />
          <br />
        </p>
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Activity</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">5, 6, 7, 8, 9</td>
      <td style="text-align:left">
        <p>(Clicking on a blue link and exploring the HDX documentation)
          <br />
        </p>
        <p>User was not expecting this.</p>
        <p>Has raised more questions than it has answered.
          <br />
        </p>
        <p>&#x201C;Now I have to read all of this in order to understand!&#x201D;
          <br
          />
        </p>
        <p>User wonders how they would get back.
          <br />
        </p>
        <p>User says they are now doing an intense reading task as well as a exporting
          process. &#x201C;It&#x2019;s too much&#x201D;
          <br />
        </p>
        <p>User reads the tagging conventions.</p>
        <p>&#x201C;Wow - lots of information. It doesn&#x2019;t look simple&#x201D;</p>
        <p>&#x201C;Very high-level&#x201D;
          <br />
        </p>
        <p>User described feeling &#x2018;down&#x2019; and this info being &#x2018;beyond
          them&#x2019; and &#x2018;disempowered&#x2019;</p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Quote
          <br />
        </p>
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Quote
          <br />
          <br />
        </p>
        <p>#Quote</p>
        <p>#Quote
          <br />
        </p>
        <p>#Quote</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">10</td>
      <td style="text-align:left">Would choose the hxl option</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">11</td>
      <td style="text-align:left">
        <p>This looks well sorted. &#x201C;I like tables!&#x201D;
          <br />
        </p>
        <p>Presentation is easy and user has seen before so is familiar.
          <br />
        </p>
        <p>User said the checkboxes are too small for them to click.</p>
        <p>Red stars are also too small.</p>
      </td>
      <td style="text-align:left">
        <p>#Quote
          <br />
        </p>
        <p>#Activity
          <br />
        </p>
        <p>#Activity</p>
      </td>
    </tr>
  </tbody>
</table>  
**Task 3:** Going through assigning hxl tags in the table   
screen required - prototype: [https://adobe.ly/2HecW3O](https://adobe.ly/2HecW3O)

**Scenarios:**

1. Is it clear how you progress? What are the steps you would take to start this section?
2. Can you show me how you would know if a certain section was going to be included in the eventual CSV download?
3. Can you show me how you would add a HXL tag?
4. \(If they click on the ‘Leave empty’ drop down\) What do you think of the options presented in this drop-down list? What kinds of information does it give you?
5. Can you add the hxl tag ‘activity’
6. Can you show me \(without clicking\) how you might add a hxl attribute?
7. \(If they click on the ‘Leave empty’ attribute drop down\) What do you think of the options presented in this section? What kinds of information does it give you in relation to the tags? 

\(Is the difference between tags and attributes clearer for the user at this point?\)  


1. Can you now show me how you would add a custom hxl attribute? \(Encourage clicking now\)
2. \(on the ‘please type an attribute’ field\) What do you expect to be able to do here? \(Go through the clicks in order to add the custom attribute\)
3. Has any information changed? Can you explain what you are seeing/understand?
4. Can you now add a tag to ‘description’. Can you point to a tag you might choose if you wanted to capture information relating to a ‘flyer’ \(like a handout/leaflet/pamphlet\) There are no wrong answers!
5. \(If they haven’t already\) Please click ‘item’ and then add a custom attribute for ‘flyer’.
6. Can you show me how you would add a second attribute? \(Once user find blue link\) Can you now add the attribute ‘adults’
7. Take a moment to pause, Can you describe your current thoughts on the process? Anything that has stood out to you?
8. Please continue with the section ‘Location’ and add a tag ‘geo’
9. \(Pause - see if they notice the list is less populated\) Why do you think this list has less options?

Q: \(if they don’t notice\) Is there anything you notice about the drop down list? Why do you think this list has less options?  


1. Can you now add two hxl attributes ‘longitude’ and ‘latitude’. \(these are the last tags/attributes to add during the test the ‘export to CSV and HDX buttons should be clickable\)
2. Take a moment to pause, Can you take a look at this screen and explain what you have just completed.

 Can you point out the HXL tags and attributes?

 Do you remember what they mean? If not can you give a good guess?

1. Can you now show me how you would get a CSV file? What would you expect to see happen next?
2. Can you tell me what you think ‘Export to HDX’ means? \(Do people assume this is a file extension type? Do they understand it’s a whole system?\) Go ahead and click ‘Export to HDX’. Take me through your impressions of this message.

<table>
  <thead>
    <tr>
      <th style="text-align:left">Scenario</th>
      <th style="text-align:left">Observation</th>
      <th style="text-align:left">Category</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="text-align:left">1</td>
      <td style="text-align:left">
        <p>User looks at which fields to include.</p>
        <p>Wants to click checkbox first (prototype error won&#x2019;t allow this)
          <br
          />
        </p>
        <p>User describes working from left to right. Doesn&#x2019;t read any top
          text (or say they did)</p>
      </td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">2</td>
      <td style="text-align:left">Easy - gestures to the checkbox</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">3</td>
      <td style="text-align:left">Easy - finds the drop down and interacts to look at the list.</td>
      <td
      style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">4</td>
      <td style="text-align:left">
        <p>Describes the list content as clear until &#x2018;impact&#x2019;
          <br />
        </p>
        <p>Admin tags mean nothing to this user.</p>
        <p>Think this might be here by accident - like the user accidentally has
          access to an admin account. &#x201C;Am I seeing something I shouldn&#x2019;t&#x201D;
          <br
          />
        </p>
        <p>Descriptive tags are fine but some are unclear
          <br />
        </p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
          <br />
        </p>
        <p>#Quote
          <br />
          <br />
        </p>
        <p>#Activity</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">5</td>
      <td style="text-align:left">Easy - finds.</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">6</td>
      <td style="text-align:left">Easy - interacts with the attribute drop down</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">7</td>
      <td style="text-align:left">
        <p>User is unclear why there are +&#x2019;s instead of a # here.
          <br />
        </p>
        <p>&#x201C;It doesn&#x2019;t look right&#x201D;
          <br />
        </p>
        <p>Expecting maybe an underscore or a hyphen.</p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
        </p>
        <p>#Quote
          <br />
        </p>
        <p>#Activity</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">8</td>
      <td style="text-align:left">Easily finds the way to add a custom tag</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">9</td>
      <td style="text-align:left">
        <p>User describes typing in the field and then clicking on the plus to add
          a custom attribute.
          <br />
        </p>
        <p>&#x201C;It&#x2019;s weird because there&#x2019;s a double plus. The plus
          in the &#x2018;attributes&#x2019; and the plus button. Plus has already
          been used&#x201D;</p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Quote</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">10</td>
      <td style="text-align:left">
        <p>Tag preview = user says its not clear how this relates to what&#x2019;s
          been completed.
          <br />
        </p>
        <p>Text is too small for this user.
          <br />
        </p>
        <p>Unsure on how to delete an attribute or tag or both.
          <br />
        </p>
        <p>User describes the kind of type ahead text fields that add tags as a user
          types and brings up a list. Describes this as more widely used.</p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Activity
          <br />
          <br />
          <br />
        </p>
        <p>#Activity</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">11</td>
      <td style="text-align:left">User says they might choose &#x2018;activity&#x2019; or &#x2018;output&#x2019;
        but wants the ability to add multiple tags to be sure.</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">12</td>
      <td style="text-align:left">Easy action</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">13</td>
      <td style="text-align:left">Easy - talks about tag preview still being too small</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">14</td>
      <td style="text-align:left">Skipped question</td>
      <td style="text-align:left"></td>
    </tr>
    <tr>
      <td style="text-align:left">15</td>
      <td style="text-align:left">Easy to add tags and attribute now. User is used to process.
        <br />
      </td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">16</td>
      <td style="text-align:left">
        <p>User suspects that less tags to choose from suggests a &#x2018;more precise&#x2019;
          type of data.
          <br />
        </p>
        <p>&#x201C;Why is meta there?&#x201D;
          <br />
        </p>
        <p>&#x201C;Why custom? I don&#x2019;t need this&#x201D;</p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Quote
          <br />
        </p>
        <p>#Quote</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">17</td>
      <td style="text-align:left">&#x201C;Why is it not the full word? (longitude) there&apos;s enough space!&#x201D;</td>
      <td
      style="text-align:left">#Quote</td>
    </tr>
    <tr>
      <td style="text-align:left">18</td>
      <td style="text-align:left">
        <p>User is able to point out tags and attributes that they have completed.
          Described fields they have interacted with.
          <br />
        </p>
        <p>User does not remember what all the tags mean if they aren&#x2019;t descriptive.
          <br
          />
        </p>
        <p>&#x201C;I understand real words&#x201D;</p>
      </td>
      <td style="text-align:left">
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Activity
          <br />
          <br />
        </p>
        <p>#Quote</p>
      </td>
    </tr>
    <tr>
      <td style="text-align:left">19</td>
      <td style="text-align:left">Export to CSV would bring up a download pop up</td>
      <td style="text-align:left">#Activity</td>
    </tr>
    <tr>
      <td style="text-align:left">20</td>
      <td style="text-align:left">
        <p>&#x201C;I don&#x2019;t know (in relation to HDX) I would guess it&#x2019;s
          a file format that is specific to this dataset&#x201D;
          <br />
        </p>
        <p>When modal popped up the user leaned back &#x201C;This is scary&#x201D;</p>
        <p>The red is intense, overpowering and &#x2018;screaming at me&#x2019;
          <br
          />
        </p>
        <p>&#x201C;I immediately doubt all my previous actions&#x201D;
          <br />
        </p>
        <p>I&#x2019;m not ready - creates nervousness</p>
      </td>
      <td style="text-align:left">
        <p>#Quote
          <br />
          <br />
        </p>
        <p>#Quote
          <br />
          <br />
        </p>
        <p>#Quote
          <br />
        </p>
        <p>#Activity</p>
      </td>
    </tr>
  </tbody>
</table>**General Feedback**

1.  Any final thoughts?

UI was easy and familiar.

User wanted more normal dialog that guide them along the process and reassures.

User would have spent much more time reading through all the documentation but expressed a desire for a ‘condensed’ version or a ‘tutorial’

User didn’t understand the term ‘deployment’

User wants a progress bar in certain areas.

Wants more illustrations or diagrams to help them along like a clock when the ‘export’ is happening.  
  


