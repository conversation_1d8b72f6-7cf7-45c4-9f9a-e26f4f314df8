<?php

// UET-12 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 03/11/2021.

namespace <PERSON>hahidi\Core\Entity;

use <PERSON><PERSON><PERSON><PERSON>\Core\StaticEntity;

class WoredaContextRisk extends StaticEntity
{
  protected $id;
  protected $date;
  protected $dailyWoredaRisk;
  protected $yesterdayRisk;
  protected $twoDaysAgoRisk;
  protected $threeDaysAgoRisk;
  protected $fourDaysAgoRisk;
  protected $fiveDaysAgoRisk;
  protected $sixDaysAgoRisk;
  protected $sevenDaysAgoRisk;
  protected $aggPastRisk;
  protected $maxPastRisk;
  protected $aggPastZoneRisk;
  protected $maxPastZoneRisk;
  protected $aggAllPastRisk;
  protected $maxAllPastRisk;
  protected $inclusivityRisk;
  protected $transparencyRisk;
  protected $accountabilityRisk;
  protected $competitivenessRisk;
  protected $confidenceInInstitutionRisk;
  protected $forecastedRisk;
  protected $woreda_context_id;
  protected $woreda;
  
  public function getResource()
  {
    return 'woreda_context_risk';
  }

  protected function getDefinition()
  {
    return [
      'id'                        => 'int',
      'date'                      => 'string',
      'dailyWoredaRisk'           => 'int',
      'yesterdayRisk'             => 'int',
      'twoDaysAgoRisk'            => 'int',
      'threeDaysAgoRisk'          => 'int',
      'fourDaysAgoRisk'           => 'int',
      'fiveDaysAgoRisk'           => 'int',
      'sixDaysAgoRisk'            => 'int',
      'sevenDaysAgoRisk'          => 'int',
      'aggPastRisk'               => 'int',
      'maxPastRisk'               => 'int',
      'aggPastZoneRisk'           => 'int',
      'maxPastZoneRisk'           => 'int',
      'aggAllPastRisk'            => 'int',
      'maxAllPastRisk'            => 'int',
      'inclusivityRisk'           => 'int',
      'transparencyRisk'          => 'int',
      'accountabilityRisk'        => 'int',
      'competitivenessRisk'       => 'int',
      'confidenceInInstitutionRisk' => 'int',
      'forecastedRisk'            => 'int',
      'woreda_context_id'         => 'int',
      'woreda'               => 'string'
    ];
  }
}
