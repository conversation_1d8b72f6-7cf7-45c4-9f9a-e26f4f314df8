<?php

// UJEVMS-90 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 03/03/2020.

namespace <PERSON><PERSON><PERSON>di\Core\Entity;

use <PERSON><PERSON><PERSON>di\Core\StaticEntity;

class UsersExtra extends StaticEntity
{
  protected $id;
  protected $user_id;
  protected $user_data;
  protected $created;
  
  public function getResource()
  {
    return 'users_ex';
  }

  protected function getDefinition()
  {
    return [
      'id'                => 'int',
      'user_id'         => 'int',
      'user_data'           => 'string',
      'created'           => 'int',
      'updated'           => 'int',
    ];
  }
}
