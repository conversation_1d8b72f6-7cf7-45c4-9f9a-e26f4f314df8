<?php

// UJEVMS-65 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 29/05/2020.

namespace <PERSON><PERSON>hidi\App\Formatter\Post;

use <PERSON><PERSON><PERSON><PERSON>\App\Formatter\API;
use <PERSON><PERSON><PERSON>di\Core\Tool\Formatter;
use <PERSON><PERSON><PERSON>di\Core\SearchData;
use <PERSON><PERSON><PERSON><PERSON>\Core\Traits\FormatterAuthorizerMetadata;

class PostAudit implements Formatter
{
  use FormatterAuthorizerMetadata;

   public function __invoke($audit)
    {

        // prefer doing it here until we implement parent method for filtering results
        // - mixing and matching with metadata is just plain ugly
        // print_r($audit);
        // $data = parent::__invoke($audit);
        // $data->value = json_($data->value, true);

        return $audit;
    }

    public function setSearch(SearchData $search, $total = null)
    {
        $this->search = $search;
        return $this;
    }
}
