<?php

namespace Us<PERSON>hidi\App\DataSource\FrontlineSMS;

/**
 * FrontlineSms Callback controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    DataSource\FrontlineSms
 * @copyright  2013 Ushahidi
 * @license    http://www.gnu.org/copyleft/gpl.html GNU General Public License Version 3 (GPLv3)
 */

use Ushahidi\App\DataSource\DataSourceController;
use Illuminate\Http\Request;
use <PERSON><PERSON>hidi\App\DataSource\Message\Type as MessageType;
use <PERSON>hahidi\App\DataSource\Message\Status as MessageStatus;
use Ushahidi\Core\Entity\Contact;
use Log;

class FrontlineSMSController extends DataSourceController
{

    protected $source = 'frontlinesms';

    public function handleRequest(Request $request)
    {
        // Authenticate the request
        if (!$this->source->verifySecret($request->input('secret'))) {
            return abort(403, 'Incorrect or missing secret key');
        }

        $from = trim($request->input('from'));

        if (empty($from)) {
            abort(400, 'Missing from');
        }

        $message_text = $request->input('message');
        $message_title = $request->input('title');
        $form_type = $request->input('type');
        $form_id;
        if(empty($form_type)) {
            abort(400, 'Missing Form Type');
        } else if( trim($form_type) == 'OP' ) {
            $form_id = 2;
        }else if(trim($form_type) == 'CO') {
            $form_id = 1;
        }
        // if (empty($message_text)) {
        //     abort(400, 'Missing message');
	    // }
        $count_semicolon = substr_count($message_text, ';');
        if($form_id == 2 && $count_semicolon !== 5) {
             // If the count is less than 5, add semicolons to make it 5
           if ($count_semicolon < 5) {
               $additional_semicolons = 5 - $count_semicolon;
               $message_text .= str_repeat(';', $additional_semicolons);
           }
        } else if($form_id == 1 && $count_semicolon !== 6) {
            // If the count is less than 5, add semicolons to make it 5
            if ($count_semicolon < 6) {
                $additional_semicolons = 6 - $count_semicolon;
                $message_text .= str_repeat(';', $additional_semicolons);
            } 
        }

	Log::info('Running CreatePostFromMessage - ' . $form_type . $form_id);

        // Allow for Alphanumeric sender
        $from = preg_replace("/[^0-9A-Za-z+ ]/", "", $from);

        $this->save([
            'type' => MessageType::SMS,
            'from' => $from,
            'contact_type' => Contact::PHONE,
            'message' => $message_text,
            'title' => (!empty($message_title)) ? $message_title: null,
	    'data_source' => 'frontlinesms',
	     'form_id' => $form_id
        ]);

        return ['payload' => [
            'success' => true,
            'error' => null
        ]];
    }
}
