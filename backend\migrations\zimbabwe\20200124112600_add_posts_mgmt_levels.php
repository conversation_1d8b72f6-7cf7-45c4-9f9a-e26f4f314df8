<?php

use Phinx\Migration\AbstractMigration;

class AddPostsMgmtLevels extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('posts')
      ->addColumn('mgmt_lev_1', 'string', [
        'limit' => 100,
        'default' => '',
        'comment' => 'Management Level 1',
      ])
      ->addColumn('mgmt_lev_2', 'string', [
        'limit' => 100,
        'default' => '',
        'comment' => 'Management Level 2',
      ])
      ->addColumn('mgmt_lev_3', 'string', [
        'limit' => 100,
        'default' => '',
        'comment' => 'Management Level 3',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
