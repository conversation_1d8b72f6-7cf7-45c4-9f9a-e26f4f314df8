<?php

return array(
    'isSlugAvailable' => ':field :value is already in use',
    'role' => array(
        'isRoleValid' => 'Role must match the parent category',
        'exists' => 'Role :value does not exist'
    ),
    'description.regex' => 'The description must contain only letters, numbers, spaces and punctuation',
    'regex' => 'The category name must contain only letters, numbers, spaces and punctuation'
);
