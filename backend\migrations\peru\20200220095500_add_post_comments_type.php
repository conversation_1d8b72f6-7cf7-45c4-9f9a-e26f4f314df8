<?php

use Phinx\Migration\AbstractMigration;

class AddPostCommentsType extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('post_comments')
      ->addColumn('type', 'integer', [
        'default' => 0,
        'comment' => 'Define comment type (response, after-action report...)',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
