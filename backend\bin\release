#!/bin/bash

# if someone invokes this with bash
set -e

# build release tarball
usage() {
    echo usage: $0 VERSION
    exit 2
}

test $# -eq 1 || usage
VERSION=$1
CWD=$(pwd)

TMP_DIR=$(mktemp -d 2>/dev/null || mktemp -d -t platform-build)
WORK_DIR=$TMP_DIR/ushahidi-platform-bundle-${VERSION}
mkdir $WORK_DIR

if [ -z "$DEST_DIR" ]; then
    DEST_DIR="${CWD}/build"
fi

echo "Copy to temp dir"
cp -R ./ $WORK_DIR

pushd $WORK_DIR

if [ -d "./.git" ]; then
    echo "Clean out extra files"
    git clean -fdx
fi

popd
pushd $TMP_DIR

# Tar it up.
echo "Building tarball"
if [ ! -d "$DEST_DIR" ]; then
    mkdir -p "$DEST_DIR"
fi
TARFILE="${DEST_DIR}/ushahidi-platform-bundle-${VERSION}.tar"

tar -cf $TARFILE \
    --exclude 'storage' \
    --exclude '.htaccess' \
    --exclude 'build' \
    --exclude 'tests' \
    --exclude 'spec' \
    --exclude 'codeship*' \
    --exclude 'deployment.env*' \
    --exclude '.travis.yml' \
    --exclude '.librarian' \
    --exclude 'tmp' \
    --exclude '.tmp' \
    --exclude 'pre-commit' \
    --exclude 'phpspec.yml.dist' \
    --exclude 'behat.yml.dist' \
    --exclude 'phpunit.xml.dist' \
    --exclude '.arc*' \
    --exclude '.git' \
    --exclude '.git*' \
    ushahidi-platform-bundle-${VERSION}/


gzip -f $TARFILE
echo "Release tarball: ${TARFILE}.gz"

ls -la ${TARFILE}.gz

popd

rm -rf $TMP_DIR
