name: Deploy
on:
  push:
    branches:
      - peru-dev
# env:
#   SMTP_HOST: ${{ secrets.IREPORT_DEV_SMTP_HOST }}
#   SMTP_PASSWORD: ${{ secrets.IREPORT_DEV_SMTP_PASSWORD }}   
#   SMTP_FROM: ${{ secrets.IREPORT_DEV_SMTP_FROM }}
#   SMTP_FROM_NAME: ${{ secrets.IREPORT_DEV_SMTP_FROM_NAME }}    

jobs:
  deploy_all:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Cache modules
        uses: actions/cache@v1
        id: yarn-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

        # Check for changes in the frontend directory
      - name: Determine frontend changes
        id: frontend-changes
        run: |
          CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})

          if echo "$CHANGED_FILES" | grep -q "frontend/"; then
            echo "Frontend changes detected."
            echo "::set-output name=frontend-changes::true"
          else
            echo "No frontend changes detected."
            echo "::set-output name=frontend-changes::false"
          fi

      # Check for changes in the backend directory
      - name: Determine backend changes
        id: backend-changes
        run: |
          CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})

          if echo "$CHANGED_FILES" | grep -q "backend/"; then
            echo "Backend changes detected."
            echo "::set-output name=backend-changes::true"
          else
            echo "No backend changes detected."
            echo "::set-output name=backend-changes::false"
          fi

      # - name: Install dependencies
      #   if: steps.frontend-changes.outputs.frontend-changes == 'true'
      #   working-directory: ./frontend
      #   run: |
      #     npm install -g @angular/cli@8.3.20
      #     npm install --legacy-peer-deps
      #     ng build hybrid-app -c=prod --output-path=./dist
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Build, tag, and push the frontend image to Amazon 
        if: steps.frontend-changes.outputs.frontend-changes == 'true'
        id: build-image-ui
        working-directory: ./frontend
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ireport-web-api
          IMAGE_TAG_UI: peru-ui
        run: |
          # Build a docker container and push it to ECR 
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG_UI -f Dockerfile-static .
          echo "Pushing image to ECR..."
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG_UI
          echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG_UI"  
      - name: Build, tag, and push the backend image to Amazon 
        if: steps.backend-changes.outputs.backend-changes == 'true'
        id: build-image
        working-directory: ./backend
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ireport-platform-api
          IMAGE_TAG_BACKEND: peru-backend
        run: |
          # Build a docker container and push it to ECR 
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG_BACKEND .
          echo "Pushing image to ECR..."
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG_BACKEND
          echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG_BACKEND"
      - name: Deploy frontend Docker image to Amazon EC2
        if: steps.frontend-changes.outputs.frontend-changes == 'true'
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ireport-web-api
          IMAGE_TAG_UI: peru-ui
          PRIVATE_KEY: ${{ secrets.AWS_IREPORT_SSH_KEY_PRIVATE_DEV }}
          HOSTNAME: ${{ secrets.HOST_IP_IREPORT_DEV }}
          USER_NAME: ec2-user
        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOSTNAME} '
          cd ireport-new &&
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 427524370246.dkr.ecr.us-east-1.amazonaws.com &&
          (docker-compose stop > /dev/null ) 2>/dev/null || true &&
          docker pull 427524370246.dkr.ecr.us-east-1.amazonaws.com/ireport-web-api:peru-ui &&
          (docker-compose -f docker-compose-image.yml up -d  > /dev/null )'

      - name: Deploy backend Docker image to Amazon EC2
        if: steps.backend-changes.outputs.backend-changes == 'true'
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ireport-platform-api
          IMAGE_TAG_BACKEND: peru-backend
          PRIVATE_KEY: ${{ secrets.AWS_IREPORT_SSH_KEY_PRIVATE_DEV }}
          HOSTNAME: ${{ secrets.HOST_IP_IREPORT_DEV }}
          USER_NAME: ec2-user
        run: |
          echo "$PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${USER_NAME}@${HOSTNAME} '
          cd ireport-new/backend &&
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 427524370246.dkr.ecr.us-east-1.amazonaws.com &&
          (docker stop eview-uz-api-core > /dev/null && docker rm  eview-uz-api-core ) 2>/dev/null || true &&
          docker pull 427524370246.dkr.ecr.us-east-1.amazonaws.com/ireport-platform-api:peru-backend &&
          (docker-compose -f docker-compose-image.yml up -d > /dev/null )'