<?php

namespace Us<PERSON>hidi\App\Http\Controllers\API;

use <PERSON><PERSON>hidi\App\Http\Controllers\Controller;
use <PERSON><PERSON>hidi\App\Models\Tenant;
use Illuminate\Http\Request;
use DB;
use <PERSON><PERSON><PERSON><PERSON>le\Shapefile;
use <PERSON><PERSON><PERSON><PERSON>le\ShapefileException;
use Shapefile\ShapefileReader;


/**
 * Ushahidi API Index Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class LernDataImportController extends Controller
{

    /**
     * Retrieve a basic information about the API
     *
     * GET /api
     *
     * @return void
     */
    public function store(Request $request){   

        try{
        
        if(app('tenant')->unique_name != 'liberia'){
            return response()->json(['success' => false, 'message' => 'unauthorized for this country'])->setStatusCode(401);
        }
       
       if(!is_null($request->incident_title) && !is_null($request->incident_description) && !is_null($request->locale)
             && !is_null($request->incident_date) &&!is_null($request->incident_dateadd)){
               
        $post = DB::connection('tenant')->table('posts')->where('type','mace')->where('title', $request->incident_title)->first();
     
        if($post == NULL){
            
            $status = 'published';

            $incident_titles = [
                'Citizens in Quardu Gbnni District in Lofa County are refusing  to follow covid-19 protocol',
                'Motorcycle accident in foya district',
                'A 45 years man hang himself.',
                'Repealing Of Road Is Ongoing In The County, Borbo Junction Tahn.',
                'A  Man Killed Himself In Nimb County, Tappita',
                'A 6 years old child went missing',
                'Rape accusation on the Senator of Grand cape mount county',
                'In Maryland county a fetch act was performed.',
                'In Grand cape mount, Damballa clinic a girl give birth and was unable to pay the hospital fee.',
                'In Lofa county the Sierra Leone Ambassador met the Liberia commander.',
                '20 Years Old Boy Rescue from alleged Killers',
                'Conflict Between Town Chief And  Chairlady',
                'An Old woman died in Nimba county.',
                'A man die while Sawing Trees in Gbapolu forest.',
                'Lofa : Motor Cyclist Dies from heavy windstorm along LPMC road.',
                'The stolen Chinese materials were being found',
                'A 20 years Boy was alledge killing his mother',
                'A missing child found in Bopolu City, Gbarpolu County',
                'Accident caused by loaded cement vehicle'
            ];

            
            if(in_array($request->incident_title, $incident_titles)){
                $status = 'archived';
            }
            
            $post_id = DB::connection('tenant')->table('posts')->insertGetId([
                'type' => 'mace',
                'form_id' => 6,
                'title' => $request->incident_title,
                'content' => $request->incident_description,
                'locale' => $request->locale,
                'post_date' => $request->incident_date,
                'created' => @strtotime($request->incident_dateadd),
                'status' => $status
            ]);
           
            if(!empty($request->latitude) && !empty($request->longitude)){
                
                $point = $request->longitude.' '.$request->latitude;
                DB::connection('tenant')->table('post_point')->insert([ 
                    'post_id' => $post_id,
                    'form_attribute_id' => 108,
                    'value' => \DB::raw("ST_GEOMFROMTEXT('POINT(".$point.")')"),
                    'created' => time()
                ]);

                $location = $this->check_location($request->latitude,$request->longitude);
                if($location != 'error'){
                    if(is_array($location) && !empty($location)){
                        DB::connection('tenant')->table('posts')->where('id',$post_id)->update([
                            'mgmt_lev_1' => @$location['mgmt_lev_1'],
                            'mgmt_lev_2' => @$location['mgmt_lev_2'],
                        ]);
                    }
                }
                }
                echo "post ". $post_id." inserted \n";

          

        }else{
            echo "post ". $post->id." already exists \n";
        }
       }

           
    }catch(\Exception $e){
        echo $e->getMessage()."\n";
    }
    }
/*
    public function update_location(){
        $posts = DB::connection('tenant')->table('posts')->where('type','lern')->orderBy('id','desc')->get();
        foreach($posts as $post){
            $point =  DB::connection('tenant')->table('post_point')->select(
                [DB::raw('ST_Y(value) as latitude'), DB::raw('ST_X(value) as longitude')]
            )->where('post_id',$post->id)->first();

            if($point != null){
                $location = $this->check_location($point->latitude,$point->longitude);
                if($location != 'error'){
                    if(is_array($location) && !empty($location)){
                        DB::connection('tenant')->table('posts')->where('id',$post->id)->update([
                            'mgmt_lev_1' => @$location['mgmt_lev_1'],
                            'mgmt_lev_2' => @$location['mgmt_lev_2'],
                        ]);
                    }
                }
            }

        }
    }*/

    public function check_location($lat,$long){
        try {
            $Shapefile = new ShapefileReader(storage_path('Shapefiles/liberia/County2'));
            $response = [];
             while ($Geometry = $Shapefile->fetchRecord()) {
                if ($Geometry->isDeleted()) {
                    continue;
                }
                $temp = json_decode($Geometry->getGeoJSON());
                $res = $temp->bbox;
                if (($lat >= $res[1]) && ($lat <= $res[3]) 
                        && ($long >= $res[0]) && ($long <= $res[2])) {
                        $adminData = $Geometry->getDataArray();
                        $response['mgmt_lev_1'] = (isset($adminData['ADM2_EN']) && !empty($adminData['ADM2_EN'])) ? $adminData['ADM2_EN'] : '';
                        $response['mgmt_lev_2'] = $adminData['ADM1_EN'];
                        break;
                }
            }

            return $response;

        } catch (ShapefileException $e) {
           return 'error';            
        }
     }
   /*
    public function update_status(Request $request){
        $limit = $request->input('limit');
        $offset = $request->input('offset');
        if(is_null($limit) || is_null($offset)){
            echo 'limit and offset are required'; die;
        }
        $posts = DB::connection('tenant')->table('posts')->where('type','lern')->limit($limit)->offset($offset)->get();
        foreach($posts as $post){

            DB::connection('tenant')->table('posts')->where('id',$post->id)->update([
                'status' => 'published'
            ]);

            $incident_titles = [
                'Citizens in Quardu Gbnni District in Lofa County are refusing  to follow covid-19 protocol',
                'Motorcycle accident in foya district',
                'A 45 years man hang himself.',
                'Repealing Of Road Is Ongoing In The County, Borbo Junction Tahn.',
                'A  Man Killed Himself In Nimb County, Tappita',
                'A 6 years old child went missing',
                'Rape accusation on the Senator of Grand cape mount county',
                'In Maryland county a fetch act was performed.',
                'In Grand cape mount, Damballa clinic a girl give birth and was unable to pay the hospital fee.',
                'In Lofa county the Sierra Leone Ambassador met the Liberia commander.',
                '20 Years Old Boy Rescue from alleged Killers',
                'Conflict Between Town Chief And  Chairlady',
                'An Old woman died in Nimba county.',
                'A man die while Sawing Trees in Gbapolu forest.',
                'Lofa : Motor Cyclist Dies from heavy windstorm along LPMC road.',
                'The stolen Chinese materials were being found',
                'A 20 years Boy was alledge killing his mother',
                'A missing child found in Bopolu City, Gbarpolu County',
                'Accident caused by loaded cement vehicle'
            ];
            $the_post = DB::connection('tenant')->table('posts')->where('type','lern')->whereIN('title', $incident_titles)->first();
            if(!is_null($the_post)){
                DB::connection('tenant')->table('posts')->where('title',$the_post->title)->update([
                    'status' => 'archived'
                ]);
            }
        }
   }*/

     
}
