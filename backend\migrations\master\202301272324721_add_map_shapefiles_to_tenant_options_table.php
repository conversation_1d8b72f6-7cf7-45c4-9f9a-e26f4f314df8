<?php

use Phinx\Migration\AbstractMigration;

class AddMapShapefilesToTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {

        $zimbabwe_shapefile = array(
            'map' => [
                'levels' => 2,
                'shapeFiles' => [
                    [
                        'uri' => 'assets/shapefiles/zimbabwe/administrative-levels.zip',
                        'config' => [
                            [
                                'fileName' => 'Zim_Province',
                                'altName' => 'Province',
                                'base' => false,
                                'selected' => true,
                                'options' => [
                                    'style' => [
                                        'color' => '#087407',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => ''
                                    ]
                                ]
                            ],
                            [
                                'fileName' => 'Zim_District',
                                'altName' => 'District',
                                'base' => true,
                                'selected' => false,
                                'options' => [
                                    'style' => [
                                        'color' => 'grey',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => '1'
                                    ]
                                ],
                                'listenForClick' => [
                                    'enabled' => true,
                                    'dispatchProperties' => true
                                ],
                                'levelsMap' => [

                                    [
                                        'name' => 'province',
                                        'nameShapeFile' => 'ADM1_NA'
                                    ],
                                    [
                                        'name' => 'district',
                                        'nameShapeFile' => 'ADM2_NA'
                                    ]
                                ]
                            ],
                        ]
                    ]
                ],
            ]
        );
        $zimbabwe_shapefile = json_encode($zimbabwe_shapefile);

        $liberia_shapefile = array(
            'map' => [
                'levels' => 2,
                'shapeFiles' => [
                    [
                        'uri' => 'assets/shapefiles/liberia/administrative-levels.zip',


                        'config' => [


                            [
                                'fileName' => 'Clan2',
                                'altName' => 'Clan',
                                'base' => false,
                                'selected' => false,
                                'options' => [
                                    'style' => [
                                        'color' => 'grey',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => ''
                                    ]
                                ]
                            ],
                            [
                                'fileName' => 'District2',
                                'altName' => 'District',
                                'base' => true,
                                'selected' => true,
                                'options' => [
                                    'style' => [
                                        'color' => '#087407',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => '1'
                                    ]
                                ],
                                'listenForClick' => [
                                    'enabled' => true,
                                    'dispatchProperties' => true
                                ],
                                'levelsMap' => [

                                    [
                                        'name' => 'province',
                                        'nameShapeFile' => 'ADM1_EN'
                                    ],
                                    [
                                        'name' => 'district',
                                        'nameShapeFile' => 'ADM2_EN'
                                    ]
                                ]
                            ]

                        ]



                    ]
                ],
            ]
        );

        $liberia_shapefile = json_encode($liberia_shapefile);

        $madagascar_shapefile = array(
            'map' => [
                'levels' => 2,
                'shapeFiles' => [
                    [
                        'uri' => 'assets/shapefiles/madagascar/administrative-levels.zip',


                        'config' => [


                            [
                                'fileName' => 'Clan2',
                                'altName' => 'Clan',
                                'base' => false,
                                'selected' => false,
                                'options' => [
                                    'style' => [
                                        'color' => 'grey',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => ''
                                    ]
                                ]
                            ],
                            [
                                'fileName' => 'District2',
                                'altName' => 'District',
                                'base' => true,
                                'selected' => true,
                                'options' => [
                                    'style' => [
                                        'color' => '#087407',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => '1'
                                    ]
                                ],
                                'listenForClick' => [
                                    'enabled' => true,
                                    'dispatchProperties' => true
                                ],
                                'levelsMap' => [

                                    [
                                        'name' => 'province',
                                        'nameShapeFile' => 'ADM1_EN'
                                    ],
                                    [
                                        'name' => 'district',
                                        'nameShapeFile' => 'ADM2_EN'
                                    ]
                                ]
                            ]

                        ]



                    ],
                ],
                'mapLevelMap' => [
                    'mgmt_lev_1' => [
                        'name' => 'County',
                        'key' => 'ADM1_EN'
                    ],
                    'mgmt_lev_2' => [
                        'name' => 'District',
                        'key' => 'ADM2_EN'
                    ]
                ]
            ]
        );
        $madagascar_shapefile = json_encode($madagascar_shapefile);

        $peru_shapefile = array(
            'map' => [
                'levels' => 2,
                'shapeFiles' => [
                    [
                        'uri' => 'assets/shapefiles/peru/administrative-levels.zip',


                        'config' => [

                            [
                                'fileName' => 'Department2',
                                'altName' => 'Departmentos',
                                'base' => false,
                                'selected' => false,
                                'options' => [
                                    'style' => [
                                        'color' => 'grey',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => ''
                                    ]
                                ]
                            ],
                            [
                                'fileName' => 'District2',
                                'altName' => 'Distritos',
                                'base' => true,
                                'selected' => true,
                                'options' => [
                                    'style' => [
                                        'color' => '#087407',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => '1'
                                    ]
                                ],
                                'listenForClick' => [
                                    'enabled' => true,
                                    'dispatchProperties' => true
                                ],
                                'levelsMap' => [

                                    [
                                        'name' => 'province',
                                        'nameShapeFile' => 'NOMPRO'
                                    ],
                                    [
                                        'name' => 'district',
                                        'nameShapeFile' => 'NOMDIS'
                                    ],
                                    [
                                        'name' => 'department',
                                        'nameShapeFile' => 'NOMDEP'
                                    ]
                                ]
                            ],
                            [
                                'fileName' => 'Clan2',
                                'altName' => 'Provincials',
                                'base' => false,
                                'selected' => false,
                                'options' => [
                                    'style' => [
                                        'color' => 'grey',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => ''
                                    ]
                                ]
                            ],

                        ]



                    ],
                ],
                'mapLevelMap' => [
                    'mgmt_lev_1' => [
                        'name' => 'Provincials',
                        'key' => 'NOMPRO'
                    ],
                    'mgmt_lev_2' => [
                        'name' => 'District',
                        'key' => 'NOMDIS'
                    ],
                    'mgmt_lev_3' => [
                        'name' => 'Department',
                        'key' => 'NOMDEP'
                    ]
                ]
            ]
        );
        $peru_shapefile = json_encode($peru_shapefile);

        $malawi_shapefile = array(
            'map' => [
                'levels' => 2,
                'shapeFiles' => [
                    [
                        'uri' => 'assets/shapefiles/malawi/administrative-levels.zip',


                        'config' => [
                            [
                                'fileName' => 'Regions',
                                'altName' => 'Regions',
                                'base' => false,
                                'selected' => false,
                                'options' => [
                                    'style' => [
                                        'color' => 'grey',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => ''
                                    ]
                                ]
                            ],
                            [
                                'fileName' => 'Districts',
                                'altName' => 'Districts',
                                'base' => false,
                                'selected' => false,
                                'options' => [
                                    'style' => [
                                        'color' => '#087407',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => ''
                                    ]
                                ]
                            ],

                            [
                                'fileName' => 'Districts_Cities',
                                'altName' => 'Districts_Cities',
                                'base' => true,
                                'selected' => true,
                                'options' => [
                                    'style' => [
                                        'color' => 'grey',
                                        'opacity' => '1',
                                        'weight' => '1',
                                        'dashArray' => '1'
                                    ]
                                ],
                                'listenForClick' => [
                                    'enabled' => true,
                                    'dispatchProperties' => true
                                ],
                                'levelsMap' => [

                                    [
                                        'name' => 'region',
                                        'nameShapeFile' => 'ADM1_EN'
                                    ],
                                    [
                                        'name' => 'district',
                                        'nameShapeFile' => 'ADM2_EN'
                                    ]
                                ]
                            ],

                        ]



                    ],
                ],
                'mapLevelMap' => [
                    'mgmt_lev_1' => [
                        'name' => 'Region',
                        'key' => 'ADM1_EN'
                    ],
                    'mgmt_lev_2' => [
                        'name' => 'District',
                        'key' => 'ADM2_EN'
                    ]
                ]
            ]
        );
        $malawi_shapefile = json_encode($malawi_shapefile);


        $this->execute("DELETE from tenant_options WHERE tenant_id = 1 AND tenant_key = 'map_shapefiles'");
        $this->execute("DELETE from tenant_options WHERE tenant_id = 2 AND tenant_key = 'map_shapefiles'");
        $this->execute("DELETE from tenant_options WHERE tenant_id = 3 AND tenant_key = 'map_shapefiles'");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'map_shapefiles', '$zimbabwe_shapefile')");
        //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'map_shapefiles', '$liberia_shapefile')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'map_shapefiles', '$madagascar_shapefile')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'map_shapefiles', '$peru_shapefile')");

        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'map_shapefiles', '$malawi_shapefile')");



    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}