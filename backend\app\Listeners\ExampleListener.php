<?php

namespace <PERSON><PERSON><PERSON><PERSON>\App\Listeners;

use <PERSON><PERSON><PERSON><PERSON>\App\Events\ExampleEvent;

class ExampleListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ExampleEvent  $event
     * @return void
     */
    public function handle(ExampleEvent $event)
    {
        //
    }
}
