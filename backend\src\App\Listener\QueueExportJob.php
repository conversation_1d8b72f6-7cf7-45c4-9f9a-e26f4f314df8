<?php

namespace <PERSON><PERSON><PERSON><PERSON>\App\Listener;

use <PERSON><PERSON><PERSON><PERSON>\Core\Entity;
use <PERSON><PERSON><PERSON><PERSON>\App\Jobs\ExportPostsJob;

class QueueExportJob
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param int $id
     * @param Entity $entity
     * @return void
     */
    public function handle($id, Entity $entity)
    {
        dispatch(new ExportPostsJob($id));
    }
}
