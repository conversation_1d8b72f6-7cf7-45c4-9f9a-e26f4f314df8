<?php
namespace <PERSON><PERSON><PERSON><PERSON>\App\Listener;

use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\PostRepository;
use <PERSON><PERSON><PERSON>di\Core\Entity\Message;
use <PERSON><PERSON><PERSON>di\Core\Entity\MessageRepository;
use <PERSON><PERSON>hidi\Core\Entity\FormAttributeRepository;
use <PERSON><PERSON>hidi\Core\Entity\TargetedSurveyStateRepository;
use Illuminate\Http\Request;
use Germanazo\CkanApi\CkanApiClient;
use Illuminate\Support\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Query;
use Ushahidi\App\Models\Tenant;
use DB;
use Log;

// Register autoloader
// require_once(app_path() .'/php-shapefile/src/Shapefile/ShapefileAutoloader.php');
php-shapefile\src\Shapefile\ShapefileAutoloader::register();

// Import classes
use Shapefile\Shapefile;
use Shape<PERSON>le\ShapefileException;
use Shapefile\ShapefileReader;


class CreatePostFromMessage
{
    protected $messageRepo;
    protected $contactRepo;
    protected $postRepo;
    protected $formRepo;
    protected $tagRepo;
    protected $formId = 2;
    protected $fieldsOrderForNumbering = "Status;Type of incident;Target;Alleged perpetrators;";
    protected $fieldsIndicatorsForm = "";
    protected $attributeList;
    protected $numberFieldsList;
    protected $delimiter;
    protected $timezone;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(
        MessageRepository $messageRepo,
        TargetedSurveyStateRepository $targetedSurveyStateRepo,
        PostRepository $postRepo,
        FormAttributeRepository $formRepo
    ) {
        $this->targetedSurveyStateRepo = $targetedSurveyStateRepo;
        $this->messageRepo = $messageRepo;
        $this->postRepo = $postRepo;
        $this->formRepo = $formRepo;
        $this->numberFieldsList = [];
        $timezone = env('TIME_ZONE');
        $key = env('SMS_FORMAT');
        $orderFormat = env('SMS_FORMAT_NEW');
        $delimiter = env('SMS_FORMAT_DELIMITER');
        $this->timezone = isset($timezone)? $timezone : 'UTC';
        $this->delimiter = isset($delimiter)? $delimiter : ';';
        if (isset($key)) {
            $this->attributeList = explode($this->delimiter, $key);
        } else {
            $this->attributeList = explode($this->delimiter, $this->fieldsOrderForNumbering);
        }
    }

    /**
    * Retrieve a lantitude & longitude of string using API
    *
    * GET /api
    *
    * @return data
    */
    public function getLatLongLocation($searchKey)
    {
        $key = env('MAP_QUEST_KEY');
        $url = env('MAP_QUEST_URL');                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  
        $countryCode = env('MAP_QUEST_COUNTRY_CODE');
        $referrer = env('APP_URL');
        $params['key'] = isset($key) ? $key : '';
        //$url = isset($url) ? $url : 'https://open.mapquestapi.com/nominatim/v1/search.php';
        $url = 'https://nominatim.openstreetmap.org';
        $params['format'] = 'json';
        $params['limit'] = 3;
        $params['addressdetails'] = 1;
        //$params['countrycodes'] = isset($countryCode) ? $countryCode : 'zw';

        // Get country code based on tenant unique name
        $country_name = app('tenant')->unique_name;
        $country_object = DB::connection('tenant')->table('country_codes')->where('country_name',$country_name )->first();
        if (isset($country_object) && !empty($country_object)) {
            $params['countrycodes'] = $country_object->country_code;
        }
        $params['q'] = trim($searchKey);

        $queryString = http_build_query($params);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
        $client = new Client();
        $ch = curl_init();

        $ch = curl_init($url . "?" . $queryString);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_REFERER, $referrer);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Ignore cert errors?
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        $output = curl_exec($ch);
        // exit;
        // $response = $client->get($url . "?" . $queryString, array('allow_redirects'=> false, 'track_redirects' => true));
        // $statusCode = $response->getStatusCode();
        // $output = $response->getBody()->getContents();
        return [
            'data' => $output
        ];
    }

    /**
     * Fetch inboud fields from Message
     * @return void
     */

    public function setFieldsNumebringKeys($tags) {
        $number = 1;
        $flag = true;
        $data = $this->formRepo->getByForm($this->formId);

        //foreach ($this->attributeList as $key => $attr) {

            if (!empty($data)) {
                foreach ($data as $key => $attribute) {
    //   Log::info('Running CreatePostFromMessage - 35 - ' . strtolower($attribute->type));


                    if (strtolower($attribute->type) === 'varchar' ) { //&& strtolower($attribute->label) === strtolower($attr)

                        if (isset($attribute->options)) {
                            $temp = [];
                            foreach($attribute->options as $key => $value) {
//                                  Log::info('Running CreatePostFromMessage - 45 - ' . $key . ' and ' . @$attribute->config[0]['target']['value']);
                                if (str_contains(strtolower($value), 'other') === false) {
                                    $temp['type'] = $attribute->key;
                                    $temp['value'] = $value;
                                    $temp['index'] = $key;
                                    $temp['tag_id'] = @$attribute->config[0]['target']['value'];
                                    $this->numberFieldsList[$number] = $temp;
                                    $number += 1;
                                }
                            }
                        }
                    }

                    if (strtolower($attribute->type) === 'tags') { // strtolower($attr) === 'type of incident' &&
                        $temp = [];
                        foreach ($tags as $value) {
                            if (str_contains(strtolower($value->tag), 'other') === false) {
                                $temp['type'] = $attribute->key;
                                $temp['value'] = $value->tag;
                                $temp['id'] = $value->id;
                                $this->numberFieldsList[$number] = $temp;
                                $number += 1;
                            }

                        }
                    }

                    if (strtolower($attribute->type) === 'int') { // strtolower($attr) === 'type of incident' &&
                        $temp = [];
                        foreach ($tags as $value) {
                            if (str_contains(strtolower($value->tag), 'other') === false) {
                                $this->numberFieldsList[@$attribute->config[0]['target']['key']] = $attribute->key;
                            }

                        }
                    }

                    if (strtolower($attribute->type) === 'point') {
                            $this->numberFieldsList['location'] = $attribute->key;
                    }

                    if (strtolower($attribute->type) === 'datetime') { //(strtolower($attr) === 'date/time') &&
                            $this->numberFieldsList['datetime'] = $attribute->key;
                    }
                }

            }
       // }
      //print_r($this->numberFieldsList);
    }

    function ToLL($north, $east, $utmZone)
    {
        // This is the lambda knot value in the reference
        $LngOrigin = Deg2Rad($utmZone * 6 - 183);

        // The following set of class constants define characteristics of the
        // ellipsoid, as defined my the WGS84 datum.  These values need to be
        // changed if a different dataum is used.

        $FalseNorth = 10000000;   // South or North?
        //if (lat < 0.) FalseNorth = 10000000.  // South or North?
        //else          FalseNorth = 0.

        $Ecc = 0.081819190842622;       // Eccentricity
        $EccSq = $Ecc * $Ecc;
        $Ecc2Sq = $EccSq / (1. - $EccSq);
        $Ecc2 = sqrt($Ecc2Sq);      // Secondary eccentricity
        $E1 = ( 1 - sqrt(1-$EccSq) ) / ( 1 + sqrt(1-$EccSq) );
        $E12 = $E1 * $E1;
        $E13 = $E12 * $E1;
        $E14 = $E13 * $E1;

        $SemiMajor = 6378137.0;         // Ellipsoidal semi-major axis (Meters)
        $FalseEast = 500000.0;          // UTM East bias (Meters)
        $ScaleFactor = 0.9996;          // Scale at natural origin

        // Calculate the Cassini projection parameters

        $M1 = ($north - $FalseNorth) / $ScaleFactor;
        $Mu1 = $M1 / ( $SemiMajor * (1 - $EccSq/4.0 - 3.0*$EccSq*$EccSq/64.0 - 5.0*$EccSq*$EccSq*$EccSq/256.0) );

        $Phi1 = $Mu1 + (3.0*$E1/2.0 - 27.0*$E13/32.0) * sin(2.0*$Mu1);
            + (21.0*$E12/16.0 - 55.0*$E14/32.0)           * sin(4.0*$Mu1);
            + (151.0*$E13/96.0)                          * sin(6.0*$Mu1);
            + (1097.0*$E14/512.0)                        * sin(8.0*$Mu1);

        $sin2phi1 = sin($Phi1) * sin($Phi1);
        $Rho1 = ($SemiMajor * (1.0-$EccSq) ) / pow(1.0-$EccSq*$sin2phi1,1.5);
        $Nu1 = $SemiMajor / sqrt(1.0-$EccSq*$sin2phi1);

        // Compute parameters as defined in the POSC specification.  T, C and D

        $T1 = tan($Phi1) * tan($Phi1);
        $T12 = $T1 * $T1;
        $C1 = $Ecc2Sq * cos($Phi1) * cos($Phi1);
        $C12 = $C1 * $C1;
        $D  = ($east - $FalseEast) / ($ScaleFactor * $Nu1);
        $D2 = $D * $D;
        $D3 = $D2 * $D;
        $D4 = $D3 * $D;
        $D5 = $D4 * $D;
        $D6 = $D5 * $D;

        // Compute the Latitude and Longitude and convert to degrees
        $lat = $Phi1 - $Nu1*tan($Phi1)/$Rho1 * ( $D2/2.0 - (5.0 + 3.0*$T1 + 10.0*$C1 - 4.0*$C12 - 9.0*$Ecc2Sq)*$D4/24.0 + (61.0 + 90.0*$T1 + 298.0*$C1 + 45.0*$T12 - 252.0*$Ecc2Sq - 3.0*$C12)*$D6/720.0 );

        $lat = Rad2Deg($lat);

        $lon = $LngOrigin + ($D - (1.0 + 2.0*$T1 + $C1)*$D3/6.0 + (5.0 - 2.0*$C1 + 28.0*$T1 - 3.0*$C12 + 8.0*$Ecc2Sq + 24.0*$T12)*$D5/120.0) / cos($Phi1);

        $lon = Rad2Deg($lon);

        // Create a object to store the calculated Latitude and Longitude values
        $PC_LatLon['lat'] = $lat;
        $PC_LatLon['lon'] = $lon;

        // Returns a PC_LatLon object
        return $PC_LatLon;
    }

    /**
     * Fetch inboud fields from Message
     * @return array
     */

    public function getAdministrativeDataByLatLang($lat, $long) {

        // echo $lat . '-' . $long;
        try {
                // Open Shapefile
                $Shapefile = new ShapefileReader(storage_path('Shapefiles/peru/District2'));
                $response = [];
                $count = 0;
                 while ($Geometry = $Shapefile->fetchRecord()) {
                    // Skip the record if marked as "deleted"
                    if ($Geometry->isDeleted()) {
                        continue;
                    }

                    $temp = json_decode($Geometry->getGeoJSON());
                    $res = $temp->bbox;

                    // $latlon1 = $this->ToLL($res[1], $res[0], 35);
                    // $latlon2 = $this->ToLL($res[3], $res[2], 35);

                    if (($lat >= $res[1]) && ($lat <= $res[3])
                            && ($long >= $res[0]) && ($long <= $res[2])) {
                            $adminData = $Geometry->getDataArray();
                            $response['mgmt_lev_2'] = (isset($adminData['NOMDIS']) && !empty($adminData['NOMDIS'])) ? $adminData['NOMDIS'] : '';
                            $response['mgmt_lev_1'] = $adminData['NOMPRO'];
                            $response['mgmt_lev_3'] = $adminData['NOMDEP'];
                            if($count == 1){
                                break;
                           }
                           $count++;
                    }
                }

                return $response;

            } catch (ShapefileException $e) {
                // Print detailed error information
                echo "Error Type: " . $e->getErrorType()
                    . "\nMessage: " . $e->getMessage()
                    . "\nDetails: " . $e->getDetails();
            }
    }


    /**
     * Return timestamp from datestring
     * @return boolean
     */
    public function checkValidDate($dateString) {
        $dateFraction = explode(' ', $dateString);
        $monDayYear = explode('/', $dateFraction[0]);
        if (is_array($monDayYear) && sizeof($monDayYear) === 3) {
            return true;
        }
        return false;
    }

    /**
     * Fetch inboud fields from Message
     * @return Array
     */
    public function fetchInboundFieldsFromMessage($msg, $tags, $inbound_form_id) {
        $inbound_fields = [];
        $output = [];
        $this->setFieldsNumebringKeys($tags);
        $note = 'Note: This post was created using an improperly formatted SMS.';
        $STORAGE_TYPE = env('STORAGE_TYPE');
        $inbound_fields['description'] = '';
        $womenKey = '';  
        $MinoritiesKey = '';
        $LGTBIQGroups ='';
        if($STORAGE_TYPE == 'local') {
            $womenKey = '3a600158-7d91-4bbf-ac0d-58b177ac747a';  // Mujeres 
            $MinoritiesKey = 'c80f31d1-de52-4437-8ef6-8ccb812b6d52'; //Miembros de Grupos étnicos 
            $LGTBIQGroups ='bf186d92-95ba-4fd8-ba0c-e663a6d5721e'; // Población LGTBIQ
        } else {
            $womenKey = '3545541d-b56e-4089-a695-456eed5ef4e0';
            $MinoritiesKey = 'a7731376-2525-4d90-953b-ed12cb98cf9f';
            $LGTBIQGroups ='725dba94-4e3c-443a-891f-1020f040e5eb';
        }
        if (is_string($msg->message) && strlen($msg->message) > 0) {
            $smsFractions = explode($this->delimiter, $msg->message);
            if ($inbound_form_id == 1 && count($smsFractions) != 7) {
                if (strpos($inbound_fields['description'], $note) === false) {
                    // The note is not present, take appropriate action
                    // For example, you can append it:
                    $inbound_fields['description'] .= ' ' . $note;
                }
            } else if ($inbound_form_id == 2 && count($smsFractions) != 6) {
                if (strpos($inbound_fields['description'], $note) === false) {
                    // The note is not present, take appropriate action
                    // For example, you can append it:
                    $inbound_fields['description'] .= ' ' . $note;
                }
            } 
            // if (empty(trim($smsFractions[0]))) {
            //     abort(400, 'Missing title'); 
            // }              
            $inbound_fields['title'] = trim($smsFractions[0]);

            if (empty(trim($smsFractions[2])) && empty(trim($smsFractions[3])) && empty(trim($smsFractions[4]))) {
                $inbound_fields['description'] = trim($smsFractions[1]) . ' ' . $inbound_fields['description'];
            } else {
                $inbound_fields['description'] = trim($smsFractions[3]) . ' ' . $inbound_fields['description'];
            }

              // Check if $inbound_fields['title'] is empty
            if (empty($inbound_fields['title'])) {
                // If it's empty, assign the text 'This is wrong format'
                if (strpos($inbound_fields['description'], $note) === false) {
                    // The note is not present, take appropriate action
                    // For example, you can append it:
                    $inbound_fields['description'] .= ' ' . $note;
                }
            }

            // Check if $inbound_fields['description'] is empty
             if (empty($inbound_fields['description'])) {
              // If it's empty, assign the text 'This is wrong format'
              if (strpos($inbound_fields['description'], $note) === false) {
                // The note is not present, take appropriate action
                // For example, you can append it:
                $inbound_fields['description'] .= ' ' . $note;
            }
            }
            $result = NULL;
            if($inbound_form_id == 1) {
               $result = $this->getLatLongLocation($smsFractions[6]);
            } else {
               $result = $this->getLatLongLocation($smsFractions[5]);
            }
            if (isset($result['data'])) {
                $latlngData = json_decode($result['data'], true);
                if (isset($latlngData) && !empty($latlngData)) {
                    $obj = [
                    'lon' => $latlngData[0]['lon'],
                    'lat' => $latlngData[0]['lat']
                    ];
                    $inbound_fields[$this->numberFieldsList['location']] = [$obj];


                    $administrativeData = $this->getAdministrativeDataByLatLang($latlngData[0]['lat'], $latlngData[0]['lon']);

                    $inbound_fields['mgmt_lev_1'] = isset($administrativeData['mgmt_lev_1']) ? $administrativeData['mgmt_lev_1'] : '';
                    $inbound_fields['mgmt_lev_2'] = isset($administrativeData['mgmt_lev_2']) ? $administrativeData['mgmt_lev_2'] : '';
                    $inbound_fields['mgmt_lev_3'] = isset($administrativeData['mgmt_lev_3']) ? $administrativeData['mgmt_lev_3'] : '';
                }
            }

            // Store Type of incident field
            $type_of_incident_id = NULL;
        if(!empty(trim($smsFractions[1])) && ctype_digit(trim($smsFractions[1])) ) {
            if($inbound_form_id == 1) {
                          foreach($this->numberFieldsList as $key => $value){
                                //   Log::info('Running CreatePostFromMessage - 55 - '  .  $smsFractions[1] . 'and' . @$value['id']);
                if(@$value['id'] == trim($smsFractions[1]) + 38){
                    $type_of_incident_id = $key;
                    break;
                }
            }
            } else {
                foreach($this->numberFieldsList as $key => $value){
                    if(@$value['id'] == trim($smsFractions[1]) + 54){
                        $type_of_incident_id = $key;
                        break;
                    }
                }
            }
        } else {
            if (strpos($inbound_fields['description'], $note) === false) {
                // The note is not present, take appropriate action
                // For example, you can append it:
                $inbound_fields['description'] .= ' ' . $note;
            }        
        }

            if(!is_null($type_of_incident_id)){
                    $data = $this->numberFieldsList[$type_of_incident_id];
                    Log::info('Running CreatePostFromMessage - 5 - ' . implode(', ', $data) . $type_of_incident_id . $data['type']);
                $inbound_fields[$data['type']] = [$data['value']];
            }


            // Store indicator fields
            $indicator_id = NULL;
            if((!empty(trim($smsFractions[2])) && ctype_digit(trim($smsFractions[2])) ) && !is_null($type_of_incident_id)  ) {
           if($inbound_form_id == 1) {
                                foreach($this->numberFieldsList as $key => $value){
//                      Log::info('Running CreatePostFromMessage - 25 - ' . @$value['index'] . ' and ' . @$value['tag_id'] . ' and ' . ($smsFractions[2] - 1));
              if (is_numeric(trim($smsFractions[2]))) {
                if(@$value['tag_id'] == trim($smsFractions[1]) + 38 && @$value['index'] == (trim($smsFractions[2])-1) ){
                    $indicator_id = $key;
                    break;
                }
              }
            }
           } else {
                   foreach($this->numberFieldsList as $key => $value){
//                          Log::info('Running CreatePostFromMessage - 15 - '  . @$value['index'] . @$value['tag_id']);
               if (is_numeric(trim($smsFractions[2]))) {
                if(@$value['tag_id'] == trim($smsFractions[1]) + 54 && @$value['index'] == (trim($smsFractions[2])-1) ){
                    $indicator_id = $key;
                    break;
                }
               }
            }
           }
  //            Log::info('Running CreatePostFromMessage - 6 - ' . $indicator_id);

            if(!is_null($indicator_id)){
                    $data = $this->numberFieldsList[$indicator_id];
//                   Log::info('Running CreatePostFromMessage - 6 - ' . implode(', ', $data) . $indicator_id);
                $inbound_fields[$data['type']] = [$data['value']];
            }
        }else {
            if (strpos($inbound_fields['description'], $note) === false) {
                // The note is not present, take appropriate action
                // For example, you can append it:
                $inbound_fields['description'] .= ' ' . $note;
            }
        }



            // form = 1 New questions will be added  women , minorities , LGTBIQ groups
            if($inbound_form_id == 1) {
                $affected_field = trim($smsFractions[4]);
                if (empty($affected_field)) {
                    // If it's empty, assign '0,0,0'
                    $affected_field = '0,0,0';
                }
                if (empty($affected_field) || !preg_match('/^\s*\d+\s*,\s*\d+\s*,\s*\d+\s*$/', $affected_field)) {
                    $affected_field = '0,0,0';
                  //  abort(400, 'wrong insert');
                  if (strpos($inbound_fields['description'], $note) === false) {
                    // The note is not present, take appropriate action
                    // For example, you can append it:
                    $inbound_fields['description'] .= ' ' . $note;
                }  
              }
                // Explode the string into an array based on commas
                $numbersArray = explode(',', $affected_field);
                $womenAffected = intval(trim($numbersArray[0]));
                $MinoritiesAffected  = intval(trim($numbersArray[1]));
                $LGTBIQGroupsAffected  = intval(trim($numbersArray[2]));

                $type_of_category_id = NULL;
                foreach($this->numberFieldsList as $key => $value){
                    //   Log::info('Running CreatePostFromMessage - 55 - '  .  $smsFractions[1] . 'and' . @$value['id']);
                    $type_of_category_id = NULL;
                    if($womenAffected == 0) { 
                        if(@$value['id'] == 61  && @$value['type'] == $womenKey ){
                            $type_of_category_id = $key;
                            break;
                        }
                    } else {
                        if(@$value['id'] == 60 &&  @$value['type'] == $womenKey){
                            $type_of_category_id = $key;
                            break;
                        }
                    }

                }

                if(!is_null($type_of_category_id)){
                    $data = $this->numberFieldsList[$type_of_category_id];
                   // $count = $this->numberFieldsList[$data['type']];
                    $inbound_fields[$data['type']] = [$data['id']];
                    if($womenAffected > 0) {
                            $count = $this->numberFieldsList[$data['type']];
                    $inbound_fields[$count] = [$womenAffected];
                   }
                }

                $type_of_category_id = NULL;
                 // $MinoritiesAffected
                foreach($this->numberFieldsList as $key => $value){
                    //   Log::info('Running CreatePostFromMessage - 55 - '  .  $smsFractions[1] . 'and' . @$value['id']);
                    if($MinoritiesAffected == 0) {
                        if(@$value['id'] == 63 && @$value['type'] == $MinoritiesKey){
                            $type_of_category_id = $key;
                            break;
                        }
                    } else {
                        if(@$value['id'] == 62 && @$value['type'] == $MinoritiesKey){
                            $type_of_category_id = $key;
                            break;
                        }
                    }

                }

                if(!is_null($type_of_category_id)){
                    $data = $this->numberFieldsList[$type_of_category_id];
                    //$count = $this->numberFieldsList[$data['type']];
                    $inbound_fields[$data['type']] = [$data['id']];

                    if($MinoritiesAffected > 0) {
                    $count = $this->numberFieldsList[$data['type']];
                    $inbound_fields[$count] = [$MinoritiesAffected];
                    }
                }

                $type_of_category_id = NULL;
                  //$LGTBIQGroupsAffected
                foreach($this->numberFieldsList as $key => $value){
                    //   Log::info('Running CreatePostFromMessage - 55 - '  .  $smsFractions[1] . 'and' . @$value['id']);
                    if($LGTBIQGroupsAffected == 0) {
                        if(@$value['id'] == 65 && @$value['type'] ==  $LGTBIQGroups){
                            $type_of_category_id = $key;
                            break;
                        }
                    } else {
                        if(@$value['id'] == 64 && @$value['type'] ==  $LGTBIQGroups){
                            $type_of_category_id = $key;
                            break;
                        }
                    }

                }

                if(!is_null($type_of_category_id)){
                    $data = $this->numberFieldsList[$type_of_category_id];
                  //  $count = $this->numberFieldsList[$data['type']];
                    $inbound_fields[$data['type']] = [$data['id']];
                    //   $inbound_fields[$count] = $LGTBIQGroupsAffected;
                    if($LGTBIQGroupsAffected > 0) {
                    $count = $this->numberFieldsList[$data['type']];

                    $inbound_fields[$count] = [$LGTBIQGroupsAffected];
                    }

                }

             }
            // Store Date/Time Field
            $dateIndex = ($inbound_form_id == 1) ? 5 : 4;
            if (!empty(trim($smsFractions[$dateIndex]))) { 
            if (isset($smsFractions[$dateIndex]) && !empty($smsFractions[$dateIndex])) {
                $post_date = trim($smsFractions[$dateIndex]);
                if (preg_match('/[a-zA-Z]/', $post_date)) {
                    // Error: Date string contains letters
                   // abort(400, 'Date string cannot contain letters');
                    $post_date = '';
                    if (strpos($inbound_fields['description'], $note) === false) {
                        // The note is not present, take appropriate action
                        // For example, you can append it:
                        $inbound_fields['description'] .= ' ' . $note;
                    }
                }
                if (!empty($post_date)) {    
                    $cleanedString = preg_replace('/\s+/', ' ', $post_date);
                    if (preg_match('/(\d{2})\s*\/\s*(\d{2})\s*\/\s*(\d{4})\s+(\d{2})\s*:\s*(\d{2})/', $cleanedString, $matches)) {
                        $formattedString = $matches[1] . '/' . $matches[2] . '/' . $matches[3] . ' ' . $matches[4] . ':' . $matches[5];
                        $post_date = $formattedString;
                }
                if ($this->checkValidDate($post_date)) {
                    $post_date = str_replace('/', '-', $post_date);
                    $date_after = date_create($post_date, new \DateTimeZone($this->timezone));

                    // Get Timezone - Los Angeles
                    $dateValue = $date_after->format(\DateTime::ATOM);
                    $datetimeKey = $this->numberFieldsList['datetime'];
                    $inbound_fields[$datetimeKey] = [$dateValue];
                }

              }else {
                //abort(400, 'wrong date formate');
                if (strpos($inbound_fields['description'], $note) === false) {
                    // The note is not present, take appropriate action
                    // For example, you can append it:
                    $inbound_fields['description'] .= ' ' . $note;
                }
              }  
            }
          } else{
            if (strpos($inbound_fields['description'], $note) === false) {
                // The note is not present, take appropriate action
                // For example, you can append it:
                $inbound_fields['description'] .= ' ' . $note;
            }          } 

            // $form_attributes = $this->formRepo->getAll();
            // $form_attribute_id = NULL;
            // foreach($form_attributes as $form_attribute){
            //     if(!empty($form_attribute->config)){
            //        if($form_attribute->config[0]['target']['value'] == $smsFractions[3]){
            //          $indicatr = $form_attribute->options[($smsFractions[4]-1)];
            //          $inbound_fields[$data['type']] = [$data['value']];

            //        }
            //     }
            // }




            // This is the old implementation
            /*
            foreach ($smsFractions as $value) {
                $value = trim($value);

                if (is_numeric($value)) {
                    if (isset($this->numberFieldsList[$value])) {
                        $data = $this->numberFieldsList[$value];
                        $inbound_fields[$data['type']] = [$data['value']];
                    }
                } elseif ($this->checkValidDate($value)) {
                    $value = str_replace('/', '-', $value);
                    $date_after = date_create($value, new \DateTimeZone($this->timezone));
                    // Get Timezone - Los Angeles
                    $dateValue = $date_after->format(\DateTime::ATOM);
                    $datetimeKey = $this->numberFieldsList['datetime'];
                    $inbound_fields[$datetimeKey] = [$dateValue];
                } else {
                    // Handle title & location
                    $text = explode('-', trim($value));
                    $textKey = strtolower($text[0]);

                    if ($textKey === 'title') {
                        $inbound_fields['title'] = $text[1];
                    } elseif($textKey === 'description') {
                        $inbound_fields['description'] = $text[1];
                    } elseif ($textKey === 'location') {
                        $result = $this->getLatLongLocation($text[1]);
                        if (isset($result['data'])) {
                            $latlngData = json_decode($result['data'], true);
                            if (isset($latlngData) && !empty($latlngData)) {
                                $obj = [
                                'lon' => $latlngData[0]['lon'],
                                'lat' => $latlngData[0]['lat']
                                ];
                                $inbound_fields[$this->numberFieldsList['location']] = [$obj];
                                $administrativeData = $this->getAdministrativeDataByLatLang($latlngData[0]['lat'], $latlngData[0]['lon']);

                                $inbound_fields['mgmt_lev_1'] = isset($administrativeData['mgmt_lev_1']) ? $administrativeData['mgmt_lev_1'] : '';
                                $inbound_fields['mgmt_lev_2'] = isset($administrativeData['mgmt_lev_2']) ? $administrativeData['mgmt_lev_2'] : '';
                                $inbound_fields['mgmt_lev_3'] = isset($administrativeData['mgmt_lev_3']) ? $administrativeData['mgmt_lev_3'] : '';
                            }
                        }
                    }
                }
            }
            */
        }
//       Log::info('Running CreatePostFromMessage - 5 - ' . implode(', ',  $inbound_fields) );
        return $inbound_fields;
    }



    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle($id, $message, $tags, $inbound_form_id, $inbound_fields)
    {
  //       Log::info('Running CreatePostFromMessage-3' .$inbound_form_id);
        if ($this->targetedSurveyStateRepo->isContactInActiveTargetedSurveyAndReceivedMessage($message->contact_id)) {
            return;
        }
  $this->formId = $inbound_form_id;

        if ($inbound_form_id > 2) {
            $this->formId = $inbound_form_id;
            if (isset($orderFormat)) {
                $this->attributeList = explode($this->delimiter, $orderFormat);
            } else {
                $this->attributeList = explode($this->delimiter, $this->fieldsIndicatorsForm);
            }
            $tags = array_filter($tags, function($v, $k) {
                return ($v->parent_id !== null && $v->parent_id > 14);
            }, ARRAY_FILTER_USE_BOTH);
        }
        $inbound_fields  = $this->fetchInboundFieldsFromMessage($message, $tags, $inbound_form_id);
        $post_id = $this->createPost(
            $message,
            $inbound_form_id,
            $inbound_fields
        );

        $message->setState(compact('post_id'));

        $this->messageRepo->update($message);

        // Prevent targeted survey listener running
        return false;
    }

    /**
     * Create post for message
     *
     * @param  Entity $message
     * @return Int
     */
    protected function createPost(Message $message, $form_id = 2, $inbound_fields)
    {
        $values = [];
        $title = isset($inbound_fields['title'])? $inbound_fields['title'] : '';
        $mgmt_lev_1 = isset($inbound_fields['mgmt_lev_1'])? $inbound_fields['mgmt_lev_1'] : '';
        $mgmt_lev_2 = isset($inbound_fields['mgmt_lev_2'])? $inbound_fields['mgmt_lev_2'] : '';
        $mgmt_lev_3 = isset($inbound_fields['mgmt_lev_3'])? $inbound_fields['mgmt_lev_3'] : '';
        $description = isset($inbound_fields['description'])? $inbound_fields['description'] : '';
        // $description = $description . ' « Original SMS text » : ' . $message->message . "";
        unset($inbound_fields['title']);
        unset($inbound_fields['mgmt_lev_1']);
        unset($inbound_fields['mgmt_lev_2']);
        unset($inbound_fields['mgmt_lev_3']);
        unset($inbound_fields['description']);
        // First create a post
        $post = $this->postRepo->getEntity()->setState([
                'title'     => $title,
                'content'   => $description,
                'values'    => $inbound_fields,
                'form_id'   => $form_id,
                'user_id'   => $message->user_id,
                'post_date' => $message->datetime,
                'mgmt_lev_1' => $mgmt_lev_1,
                'mgmt_lev_2' => $mgmt_lev_2,
                'mgmt_lev_3' => $mgmt_lev_3,
            ]);
        return $this->postRepo->create($post);
    }
}
