<?php

namespace Ushahidi\App\Http\Controllers\API\CSV;

use Us<PERSON>hidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
//use Illuminate\Http\Response;
use Laravel\Passport\Bridge\User;
use <PERSON>hahidi\App\Models\User As UserData;
use League\Csv\Reader;
use League\Csv\Writer;
use Ushahidi\App\Http\Controllers\API\CSV\SplTempFileObject;
use DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\QueryException;
use Validator;
use League\Csv\CannotInsertRecord;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;
use League\Csv\Exception\InvalidRowException;
use ArrayIterator;
use Ushahidi\App\Jobs\ImportUserCsv;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON><PERSON>di\Core\Traits\UserContext;
use Ushahidi\Core\Entity\ExportJob;

/**
 * Ushahidi API CSV Import
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2013 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class CSVImportController extends RestController
{
    protected function getResource()
    {
        return 'posts';
    }

    public function store(Request $request, $id = null)
    {
        /**
         * Step two of import.
         * Support all line endings without manually specifying it
         * (primarily added because of OS9 line endings which do not work by default )
         */
        ini_set('auto_detect_line_endings', 1);

        // Get payload from CSV repo
        $csv		= service('repository.csv')->get($id);
        $fs 		= service('tool.filesystem');
        $reader 	= service('filereader.csv');
        $transformer= service('transformer.csv');

        // Read file
        $file 		= new \SplTempFileObject();
        $contents 	= $fs->read($csv->filename);
        $file->fwrite($contents);

        // Get records
        // @todo read up to a sensible offset and process the rest later
        $records 	= $reader->process($file);

        // Set map and fixed values for transformer
        $transformer->setMap($csv->maps_to);
        $transformer->setFixedValues($csv->fixed);

        $this->usecase = $this->usecaseFactory
            ->get($this->getResource(), 'import')
            ->setPayload($records)
            ->setCSV($csv)
            ->setTransformer($transformer);
        $response = $this->prepResponse($this->executeUsecase($request), $request);
        return $response;
    }
    
    public function usersExport() 
    {                
        $users 		= $this->usecaseFactory->userExportData();
        $csv 		= Writer::createFromFileObject(new \SplTempFileObject());
       // $header 	= ['USER NAME','EMAIL','SHORT ROLE NAME','PROVINCE','TELEPHONE','TELEPHONE2'];
	   //	$header         = ['Nom d\'utilisateur','E-mail','Nom De Rôle Abrégé','Région','Organisation','Position','Téléphone','Téléphone2'];
       $header     = ['Nombre', 'Email' , 'Abreviación de tipo de usuario' , 'Departamento' , 'Organización' , 'Puesto' , 'Teléfono' , 'Teléfono2'];
        
		$csv->insertOne($header);
		
		$data 		= [];
        $out        = fopen('php://output', 'w');        
        
        foreach($users as $user){
            foreach($user  as $key => $userData){                
                $data[$key] = [                        
                    'username' 		=> $userData->realname,
                    'email'		 	=> $userData->email,
                    'role' 			=> isset($userData->short_name)?$userData->short_name:'',
                    'region'        => isset($userData->region)? $userData->region:'',
                ];
                
                if($userData->contact != '' && isset($userData->contact)) {                        
                    $phoneKey               = isset($data[$userData->uid])?"TELEPHONE2":"TELEPHONE";
                    $data[$key][$phoneKey]  = $userData->contact;
                }             
            }
        }

        $csv->insertAll($data);
        $csv->output();
        return;         
    }    

    public function usersImport(Request $request) {
        try{
            $validator = Validator::make(
                [
                   	'file'      		=> $request->csv,
                   	'extension' 		=> strtolower($request->csv->getClientOriginalExtension()),
                ],[
                   	'file'       		=> 'required',
                   	'extension'  		=> 'required|in:csv',
                ],[
                	'extension.in' 		=> 'file must be CSV.'
                ]
            );
            if($validator->fails()) {
                return response()->json(['success' => false,"message" => $validator->errors()->all()],422);
            }else{
                $state = [
                    'created'           => time(),
                    'status'            => ExportJob::STATUS_PENDING,
                    'user_id'           => $request->user_id,
                    'entity_type'       => 'import_user',
                    // Don't save this now, we need to generate it properly
                    'hxl_heading_row'   => null
                ];
        
                $job        = DB::table('export_job')->insert($state);
                $id         = DB::getPdo()->lastInsertId();
                $contents   = $request->csv->store('temp-files');
                dispatch(new ImportUserCsv($contents, $id, app('tenant')->id));
            }
            return response()->json(['success' => true,"message" => "Your request has been queued. The file will be downloaded automatically.", "id" => $id]);
        }catch(Exception $e){
            return response()->json(['success' => false,"message" => $e->getMessage()]);
        }
    }
}
