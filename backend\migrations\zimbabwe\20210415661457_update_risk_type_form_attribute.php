<?php

use Phinx\Migration\AbstractMigration;

class UpdateRiskTypeFormAttribute extends AbstractMigration
{

    public function up()
    {
        $pdo = $this->getAdapter()->getConnection();

        $title = json_encode(['Political','Security','Economy','Social','Environment']);

        $insert = $pdo->prepare("
            UPDATE form_attributes
            SET options='".$title."'
            WHERE label = 'Risk type'
            ;");

        $insert->execute();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
