<?php

use Phinx\Migration\AbstractMigration;

class AddPostsMgmtLevel extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('posts')
      ->addColumn('mgmt_lev', 'integer', [
        'limit' => 100,
        'default' => 0,
        'comment' => 'Management Level',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
