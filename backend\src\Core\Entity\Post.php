<?php

/**
 * <PERSON><PERSON><PERSON><PERSON> Post
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Entity;

use <PERSON>hahidi\Core\StaticEntity;
use <PERSON><PERSON>hidi\Core\Traits\Permissions\ManagePosts;
use <PERSON><PERSON><PERSON>di\Core\Tool\Permissions\Permissionable;

class Post extends StaticEntity
{
    protected $id;
    protected $parent_id;
    protected $form_id;
    protected $user_id;
    protected $message_id;
    // Color is taken from the asscoiated form entity
    protected $color;
    protected $type;
    protected $title;
    protected $slug;
    protected $content;
    protected $author_email;
    protected $author_realname;
    protected $author_contact;
    protected $status;
    protected $created;
    protected $updated;
    protected $locale;
    protected $values;
    protected $post_date;
    protected $tags;
    protected $published_to;
    protected $completed_stages;
    protected $sets;
    // UJEVMS-67 - Fu<PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 25/03/2020.
    protected $linked_posts;
    protected $lock;
    // Source when from external provider: SMS, Email, etc
    protected $source;
    // When originating in an SMS message
    protected $contact_id;
    protected $data_source_message_id;

    // UJEVMS-49 - SCALA Luca, <EMAIL> - 21/01/2020.
    protected $priority;
    // UJEVMS-33 - SCALA Luca, <EMAIL> - 21/01/2020.
    protected $mgmt_lev_1;
    protected $mgmt_lev_2;
    protected $mgmt_lev_3;
    // UJEVMS-55 - SCALA Luca, <EMAIL> - 11/02/2020.
    protected $mgmt_lev;
    // UJEVMS-62 - SCALA Luca, <EMAIL> - 14/02/2020.
    protected $updated_by;
    // UJEVMS-60 - SCALA Luca, <EMAIL> - 19/02/2020.
    protected $icon;
    // UEH-35 - Yaghi Ahmed, <EMAIL> - 20/10/2022.
    protected $published;

    // StatefulData
    protected function getDefaultData()
    {
        return [
            'type' => 'report',
            'locale' => 'en_US',
            'published_to' => [],
            // UJEVMS-49 - SCALA Luca, <EMAIL> - 21/01/2020.
            'priority' => 'standard',
            // UJEVMS-33 - SCALA Luca, <EMAIL> - 21/01/2020.
            'mgmt_lev_1' => '',
            'mgmt_lev_2' => '',
            'mgmt_lev_3' => '',
            // UJEVMS-55 - SCALA Luca, <EMAIL> - 11/02/2020.
            'mgmt_lev' => 0
        ];
    }

    // StatefulData
    protected function getDerived()
    {
        return [
            'slug'    => function ($data) {
                if (array_key_exists('title', $data)) {
                    // Truncate the title to 137 chars so that the
                    // 13 char uniqid will fit
                    $slug = $data['title'];
                    if (strlen($slug) >= 137) {
                        $slug = substr($slug, 0, 136);
                    }
                    return $slug . ' ' . uniqid();
                }
                return false;
            },
            'form_id'   => ['form', 'form.id'], /* alias */
            'user_id'   => ['user', 'user.id'], /* alias */
            'parent_id' => ['parent', 'parent.id'], /* alias */
        ];
    }

    // DataTransformer
    protected function getDefinition()
    {
        return [
            'id'                        => 'int',
            'parent_id'                 => 'int',
            'form'                      => false, /* alias */
            'form_id'                   => 'int',
            'user'                      => false, /* alias */
            'user_id'                   => 'int',
            'type'                      => 'string',
            'title'                     => 'string',
            'slug'                      => '*slug',
            'content'                   => 'string',
            'author_email'              => 'string', /* @todo email filter */
            'author_realname'           => 'string', /* @todo redundent with user record */
            'author_contact'            => 'string', /* @todo redundent with user record */
            'status'                    => 'string',
            'created'                   => 'int',
            'updated'                   => 'int',
            'post_date'                 => '*date',
            'locale'                    => '*lowercasestring',
            'values'                    => 'array',
            'tags'                      => 'array',
            'published_to'              => '*json',
            'completed_stages'          => '*arrayInt',
            'sets'                      => 'array',
            // UJEVMS-67 - Fulpagare Pramod, <EMAIL> - 25/03/2020.
            'linked_posts'              => 'int',
            'lock'                      => 'array',
            'data_source_message_id'    => 'string',
            // UJEVMS-49 - SCALA Luca, <EMAIL> - 21/01/2020.
            'priority'                  => 'string',
            // UJEVMS-33 - SCALA Luca, <EMAIL> - 21/01/2020.
            'mgmt_lev_1'                => 'string',
            'mgmt_lev_2'                => 'string',
            'mgmt_lev_3'                => 'string',
            // UJEVMS-55 - SCALA Luca, <EMAIL> - 11/02/2020.
            'mgmt_lev'                  => 'int',
            // UJEVMS-62 - SCALA Luca, <EMAIL> - 14/02/2020.
            'updated_by'                => 'int',
            // UEH-35 - Yaghi Ahmed, <EMAIL> - 20/10/2022.
            'published'                => 'boolean'
        ];
    }

    // Entity
    public function getResource()
    {
        return 'posts';
    }

    // StatefulData
    protected function getImmutable()
    {
        return array_merge(parent::getImmutable(), ['type', 'form_id']);
    }
}
