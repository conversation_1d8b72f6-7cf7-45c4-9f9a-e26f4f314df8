<?php

// UET-12 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 03/11/2021.

/**
 * Repository for woreda_context_risk
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Entity;

use Ushahidi\Core\Entity\Repository\EntityGet;
use Ushahidi\Core\Entity\Repository\EntityExists;
use Ushahidi\Core\Usecase\SearchRepository;

interface WoredaContextRiskRepository extends
    EntityGet,
    EntityExists,
    SearchRepository
{
    
}
