<?php

namespace <PERSON><PERSON>hidi\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class Tenant extends Model{

    protected $connection = 'master';

    protected $fillable = [
        'name', 'unique_name', 'backend_url', 'frontend_url', 'db'
    ];

    public function configure(){

        config([
            'database.connections.tenant.database' => $this->db,
        ]);
    
        DB::purge('tenant');

        DB::reconnect('tenant');

        Schema::connection('tenant')->getConnection()->reconnect();

        return $this;
    }

    /**
     *
     */
    public function use(){

        app()->forgetInstance('tenant');

        app()->instance('tenant', $this);

        return $this;
    }

}