<?php

// UCD-31 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 27/10/2020.


use Phinx\Migration\AbstractMigration;

class ALterRoles extends AbstractMigration
{

  public function change()
  {
    $this->table('roles')
      ->addColumn('short_name', 'string', [
          'default' => null,
          'after' => 'name',
      ])
      ->update();

      $this->execute("
      start transaction;

      set FOREIGN_KEY_CHECKS = 0;
      
      update roles set short_name='SU' where name='super';
      update roles set short_name='AU' where name='admin';
      update roles set short_name='MU' where name='managementuser';
      update roles set short_name='OU' where name='operationuser';
      update roles set short_name='RU1' where name='regionaluser1';
      update roles set short_name='RU2' where name='regionaluser2';
      update roles set short_name='SY' where name='sysadmin';

      set FOREIGN_KEY_CHECKS = 1;

      commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
