<?php

use Phinx\Migration\AbstractMigration;

class DefaultAdminUser extends AbstractMigration
{
    /**
     * Migrate Up.
     */
    public function up()
    {
        $query = $this->getAdapter()->getConnection()->prepare(
            'INSERT INTO users (username,email , password, role, created) VALUES (:username,:email ,:password, :role, :created)'
        );
        $query->execute([
            ':username' => 'admin',
            ':email' => '<EMAIL>',
            ':password' => '$2y$12$kIl4b3aZvQhghhPJXgG/QevZA4cdvXuzu1AZiAomphzLfOuAkFEfG', // password is "admin"
            ':role'     => 'admin',
            ':created'  => time(),
        ]);
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        $this->execute("DELETE FROM users WHERE username = 'admin'");
    }
}
