<?php

// UJEVMS-65 - Fulpa<PERSON>e Pramod, <EMAIL> - 09/04/2020.

namespace <PERSON><PERSON>hidi\App\Repository;

use Oh<PERSON>zee\DB;
use Ushahidi\Core\Entity;
use <PERSON><PERSON>hidi\Core\Entity\Audit;
use <PERSON><PERSON><PERSON>di\Core\Entity\AuditRepository as AuditRepositoryContract;
use <PERSON><PERSON>hidi\Core\Entity\Permission;
use Ushahidi\Core\SearchData;
use <PERSON>hahidi\Core\Tool\Permissions\AclTrait;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\App\Http\Controllers\RESTController;

class AuditRepository extends OhanzeeRepository implements AuditRepositoryContract
{
  use UserContext;

  use AclTrait;

  private $queryVar;

  public function getEntity(array $data = null)
  {
    return new Audit();
  }

  protected function getTable()
  {
    return 'audit';
  }

  public function getSearchFields()
  {
    return [
      'id',
      'user_id'
    ];
  }

  // SearchRepository
    public function setSearchParams(SearchData $search)
    {

        $this->search_query = $this->selectQuery();

        $sorting = $search->getSorting();

        if (!empty($sorting['orderby'])) {
            $order = isset($sorting['order']) ? strtoupper($sorting['order']) : 'ASC';
            $this->search_query->order_by(
                $this->getTable() . '.' . $sorting['orderby'],
                ($order == 'DESC' ? 'DESC' : 'ASC')
            );
        }

        if (!empty($sorting['offset'])) {
            $this->search_query->offset(intval($sorting['offset']));
        }

        if (
            array_key_exists('limit', $sorting)
            && $sorting['limit'] >= 0
            && strlen($sorting['limit'])
        ) {
            $this->search_query->limit(intval($sorting['limit']));
        }
        // apply the unique conditions of the search
    }

  public function getAuditByParams($search)
  {
    $data = $search->getFilters(['id', 'user_id', 'limit', 'offset', 'orderby', 'order']);

    $query = DB::select('audit.*', 'users.realname')
      ->from('audit');

    $query->join('users', 'LEFT')->on('audit.user_id', '=', 'users.id');
    if (isset($data['user_id'])) {
      $query->where('audit.user_id', '=', $data['user_id']);
    } else {
      $query->where('audit.id', '=', $data['id']);
    }

    $this->queryVar = clone $query;

    if ($data['offset']) {
        $query->offset($data['offset']);
    }

    if ($data['limit']) {
        $query->limit($data['limit']);
    }

    if (isset($data['orderby'])) {
        $query->order_by($data['orderby'], $data['order']) ;
    }

    $entityAsArray = $query->execute($this->db())->as_array();
    if (count($entityAsArray) === 0) return [];
    return $entityAsArray;
  }

  // public function getByUserId($user_id)
  // {
  //   $entityAsArray = DB::select('*')
  //     ->from('audit')
  //     ->where('audit.user_id', '=', $user_id)
  //     ->execute($this->db())
  //     ->as_array();
  //   if (count($entityAsArray) === 0) return [];
  //   return $entityAsArray;
  // }

  // SearchRepository
  public function getSearchTotal()
  {
      // Assume we can simply count the results to get a total
      $query = $this->queryVar
          ->resetSelect()
          ->select([DB::expr('COUNT(audit.id)'), 'total']);

      // Fetch the result and...
      $results = $query->execute($this->db());
      // ... return the total.
      $total = 0;

      foreach ($results->as_array() as $result) {
          $total += array_key_exists('total', $result) ? (int) $result['total'] : 0;
      }
      return $total;
  }

  public function create(Entity $entity)
  {
    $user = $this->getUser();
    $record = array_filter($entity->asArray());
    $value = json_decode($record['value']);
    $record['created'] = time();
    $record['user_id'] = $user->id;
    $value->created = time();
    $record['value'] = json_encode($value);
    $id = $this->executeInsert($this->removeNullValues($record));
    return $id;
  }
}

?>