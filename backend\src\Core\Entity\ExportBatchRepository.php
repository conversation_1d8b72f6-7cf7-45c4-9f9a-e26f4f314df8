<?php

/**
 * Repository for export jobs
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package   Ushahidi\Platform
 * @copyright 2014 Ushahidi
 * @license   https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Entity;

use <PERSON>hahidi\Core\Usecase\CreateRepository;
use Ushahidi\Core\Usecase\UpdateRepository;

interface ExportBatchRepository extends
    CreateRepository,
    UpdateRepository
{
    /**
     * Get all batches for job id
     * @param  int $jobId
     * @param  string $status
     * @return \Illuminate\Support\Collection
     */
    public function getByJobId($jobId, $status = ExportBatch::STATUS_COMPLETED);
}
