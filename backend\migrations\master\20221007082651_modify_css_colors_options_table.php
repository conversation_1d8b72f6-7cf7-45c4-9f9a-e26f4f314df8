<?php

use Phinx\Migration\AbstractMigration;

class ModifyCssColorsOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {

        $liberia_colors = array(
            'colors' => [
                'primary_color' => '#264952',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#087407',
                'text_color' => '#264952',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#264952',
                'bg_color' => '#f4f4f4',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#400843D',
                'btn_dark_color' => '#00843D',
                'btn_dark_hover_bg_color' => '#f4f4f4',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'
            ],
            );
        $liberia_colors = json_encode($liberia_colors);
    
        $madagascar_colors = array(
            'colors' => [
                'primary_color' => '#264952',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#087407',
                'text_color' => '#264952',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#264952',
                'bg_color' => '#f4f4f4',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#400843D',
                'btn_dark_color' => '#00843D',
                'btn_dark_hover_bg_color' => '#f4f4f4',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'
            ],
            );
        $madagascar_colors = json_encode($madagascar_colors);

        $peru_colors = array(
            'colors' => [
                'primary_color' => '#003770',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#087407',
                'text_color' => '#003770',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#003770',
                'bg_color' => '#f4f4f4',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#400843D',
                'btn_dark_color' => '#003770',
                'btn_dark_hover_bg_color' => '#f4f4f4',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'
            ],
            );
        $peru_colors = json_encode($peru_colors);

     //   $this->execute("UPDATE tenant_options SET tenant_value = '$liberia_colors' WHERE tenant_id = '2' AND tenant_key = 'colors'");
        // $this->execute("UPDATE tenant_options SET tenant_value = '$madagascar_colors' WHERE tenant_id = '1' AND tenant_key = 'colors'");
        // $this->execute("UPDATE tenant_options SET tenant_value = '$peru_colors' WHERE tenant_id = '2' AND tenant_key = 'colors'");
    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
