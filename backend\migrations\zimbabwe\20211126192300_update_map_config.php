<?php

// UZ-152 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 11/02/2020.
// This migration will initialize the configuration.

use Phinx\Migration\AbstractMigration;

class UpdateMapConfig extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $deploymentName = 'CEWER Zimbabwe';
    $private = true;
    $mapInitialLat = -18.800;
    $mapInitialLon = 29.990;
    $mapInitialZoom = 6.75;

    $filterOption = array(
    'management_level' => array("label" => "Management Level", "value" => array(3 => "National", 2 => "Regional", 1 => "Sub-regional")),
    'duration' => array("label" => "Report Duration", "value" => array(1 => "Day", 2 => "Week", 3 => "Month", 4 => "Custom")),
    'format' => array("label" => "Format", "value" => array(1 => "Table", 2 => "Bar Chart", 3 =>"Line chart", 4 => "Map"))
    );
    $filterOption = json_encode($filterOption);
    $this->execute("
      start transaction;

      truncate table config;
      
      insert into config (id, group_name, config_key, config_value, updated) values 
         (1, 'site', 'name', '\"$deploymentName\"', now())
       , (2, 'site', 'private', '$private', now())
       , (3, 'map', 'default_view', '{\"lat\":$mapInitialLat,\"lon\":$mapInitialLon,\"zoom\":$mapInitialZoom}', now())
       , (4, 'filters', 'filters', '$filterOption', now());
       commit;
      ");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
