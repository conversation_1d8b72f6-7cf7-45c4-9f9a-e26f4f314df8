# Contributing docs via GitHub

Ushahidi Platform's documentation is kept in GitHub and GitBook. Both of these documentation repositories are kept synchronised and they should contain exactly the same information.

For most of the users, the preference will be to browse the documentation via GitBook. It tends to look prettier and easier to navigate.

However, at the moment of this writing, GitBook doesn't have a system that can accept contributions from any reader. However, contributions are accepted from anyone via GitHub. This document walks you through the steps of contributing via GitHub.

The only requirement to start is to have a user account in GitHub.

Further down the road, once you have proposed some changes, we will ask you to agree to a [contributors agreement](https://docs.google.com/forms/d/e/1FAIpQLScqz_EQbz_CYlSHffnGx7p2GdqP23FmbACwocIWejEHYLyzdg/viewform).

## Finding edit on GitHub button

While browsing GitBook, you will find a "Edit on GitHub" button

![](../.gitbook/assets/github-1.png)

This will take you to github.com , where you will see the same document, along with some action buttons. You would want to click the one looking like a pencil, as illustrated below:

![](../.gitbook/assets/github-2.png)

Clicking on this will take you to an editing view, where you will be able to apply your changes. Please bear in mind that this is a plain text editor. Formatting \(headers, links, lists, highlighted text, etc..\) is achieved by following the Markdown rules. If you are not familiar with this, please take a look at [this guide](https://guides.github.com/features/mastering-markdown/) first.

![](../.gitbook/assets/github-3.png)

In this guide, as an example, we are going to make a \(rather silly\) addition to the document. At any point, we can click the "Preview changes" button.

![](../.gitbook/assets/github-4.png)

In the preview mode, we can see the formatted result of our changes. In the left margin, we'll see that the blocks of text we have modified are marked with red and green lines. Red lines mean deletion and green lines mean addition.

![](../.gitbook/assets/github-5.png)

Once we are happy with our modifications, we _scroll back to the bottom of the window_. We will find a "**Propose file change**" section, with two boxes below it.

The first box you may use for typing a summary of your changes \(or you may choose not to type anything and accept the default "Upgrade _filename_" template. GitHub will encourage you to keep it short, and you should.

The second box is for providing more details about your modifications. You may type a longer text here.

This information provided in these two boxes is useful for the administrators so as to understand your motivations and goals when you chose to make the contribution. It is highly encouraged that you provide this information in a clear and concise way.

Once you are happy with your changes and proposal details. You would click the "Propose file change" button.

![](../.gitbook/assets/github-6.png)

You will be dropped into yet another summary view of your changes. There's really not much else you should review here, if you have reviewed your changes in previous steps. We suggest just to locate and click the "Create pull request" button.

![](../.gitbook/assets/github-7.png)

You will be dropped into the pull request opening screen. A pull request is the way developers refer to the petition of someone to apply changes to files in the repository. A pull request wraps together the whole process of requesting the changes, discussing them, and finally approving or rejecting the petition.

The pull request window will show by default the title and description that you gave to your changes initially. If you feel the need to do so, you may provide even further detail, but you may safely ignore the content in the template description. The reason for that is that the template is intended for code changes.

Click on the "Create pull request" button once you agree with what you see on the screen.

![](../.gitbook/assets/github-8.png)

Once you click that button, your pull request will be submitted and available for project administrators to review. The administrators may be getting back to you with questions or suggestions.

At some point they will also take the decision to accept \(or more unlikely, reject\) your contribution.

![](../.gitbook/assets/github-9.png)

In case this is your **first contribution**, you will be requested to sign the CLA \(Contributor License Agreement\). You will find a comment in the pull request, with the necessary instructions:

![](../.gitbook/assets/github-10.png)

And this is it, at this point your documentation changes have been submitted and visible to administrators. Thank you very much!



