<?php

// UJEVMS-52 - <PERSON><PERSON><PERSON>, <EMAIL> - 27/01/2020.

namespace <PERSON><PERSON>hidi\Core\Entity;

use <PERSON><PERSON><PERSON><PERSON>\Core\StaticEntity;

class PostComment extends StaticEntity
{
  protected $id;
  protected $parent_id;
  protected $post_id;
  protected $user_id;
  protected $content;
  protected $status;
  protected $created;
  protected $updated;
  protected $action_taken;
  protected $no_action_taken;
  protected $type;
  protected $users;
  protected $linked_reports;
  protected $comment_user;

  public function getResource()
  {
    return 'post_comments';
  }

  protected function getDefinition()
  {
    return [
      'id'                => 'int',
      'parent_id'         => 'int',
      'post_id'           => 'int',
      'user_id'           => 'int',
      'content'           => 'string',
      'status'            => 'string',
      'created'           => 'int',
      'updated'           => 'int',
      'action_taken'      => 'boolean',
      'no_action_taken'   => 'boolean',
      'type'              => 'int',
      'users'             => 'array',
      'linked_reports'    => 'array',
      'comment_user'      => 'string'
    ];
  }

  protected function getDefaultData()
  {
    return [
      'status'            => 'published',
      'action_taken'      => false,
      'no_action_taken'   => false,
      'type'              => 0
    ];
  }
}
