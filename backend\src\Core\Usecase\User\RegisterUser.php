<?php

/**
 * Ushahidi Platform User Login Use Case
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Usecase\User;

use Ushahidi\Core\Entity\UserRepository;
use Ushahidi\Core\Tool\PasswordAuthenticator;
use Ushahidi\Core\Usecase\CreateUsecase;
use Ushahidi\Core\Exception\AuthorizerException;
use Ushahidi\Core\Entity;
use Ushahidi\Core\Tool\RateLimiter;

class RegisterUser extends CreateUsecase
{
    /**
     * @var RateLimiter
     */
    protected $rateLimiter;

    /**
     * @param RateLimiter $rateLimiter
     */
    public function setRateLimiter(RateLimiter $rateLimiter)
    {
        $this->rateLimiter = $rateLimiter;
    }

    public function interact()
    {
        $entity = $this->getEntity();                       // fetch entity
        $this->rateLimiter->limit($entity);                 // Rate limit registration attempts

        $isValid = $this->verifyValid($entity);                        // verify that the entity is in a valid state
        
        if(!$isValid){
            $this->validatorError($entity);
        }

        try {
            $this->verifyRegisterAuth($entity);             // verify that registration can be done in this case
            $id = $this->repo->register($entity);           // persist the new entity
        } catch (\Ushahidi\Core\Exception\AuthorizerException $e) {
            $id = $this->repo->selfRegister($entity);       // persist the new entity            
        }

        $entity = $this->getCreatedEntity($id);             // get the newly created entity          
        return $this->formatter->__invoke($entity);         // return the formatted entity         
    }

    protected function verifyRegisterAuth(Entity $entity)
    {
        $this->verifyAuth($entity, 'register');
    }
}
