<?php
use Phinx\Migration\AbstractMigration;

class InsertRiskLocationTypes extends AbstractMigration
{

  public function change()
  {
      $connection = $this->getAdapter()->getConnection();
      $adapter = $this->getAdapter();        
      $risk = $this->fetchRow("SELECT id FROM form_stages WHERE type = 'risk'", 0);

      if($risk[0]) {
          $types = json_encode(['Specific','General']);
          $attrInsert = $connection->prepare(
              "INSERT INTO form_attributes (" . $adapter->quoteColumnName('key') . ",
                  label, input, type, required, priority, cardinality, options, form_stage_id)
              VALUES
                  ( 'location_type_default', 'Types of location', 'select', 'varchar', 0, 4, 1, '$types', :form_stage_id )
                  ,( 'region_default', 'Region', 'select', 'varchar', 0, 5, 1, '[]', :form_stage_id )
                  ,( 'zone_default', 'Zone', 'select', 'varchar', 0, 6, 1, '[]', :form_stage_id )
                  ,( 'woreda_default', 'Woreda', 'select', 'varchar', 0, 7, 1, '[]', :form_stage_id )
              "
          )->execute([ ':form_stage_id' => $risk[0] ]);
      }
 
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
      
  }
}
