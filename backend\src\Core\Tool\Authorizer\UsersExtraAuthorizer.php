<?php

// UJEVMS-90 - <PERSON><PERSON><PERSON><PERSON><PERSON> Pramod, <EMAIL> - 03/03/2020.

namespace Us<PERSON>hidi\Core\Tool\Authorizer;

use Us<PERSON>hidi\Core\Entity;
use Us<PERSON>hidi\Core\Entity\User;
use Us<PERSON>hidi\Core\Entity\Form;
use Us<PERSON>hidi\Core\Entity\FormRepository;
use Ushahidi\Core\Entity\UserRepository;
use Us<PERSON>hidi\Core\Entity\Permission;
use Ushahidi\Core\Tool\Authorizer;
use Ushahidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\OwnerAccess;
use Us<PERSON>hidi\Core\Traits\ParentAccess;
use Us<PERSON>hidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivateDeployment;
use Ushahidi\Core\Tool\Permissions\AclTrait;

class UsersExtraAuthorizer implements Authorizer
{
  use UserContext;

  use AdminAccess, OwnerAccess, ParentAccess;

  use PrivAccess;

  use PrivateDeployment;

  use AclTrait;

  protected function getAllPrivs()
  {
    return ['read', 'create', 'update', 'delete', 'search'];
  }

  public function __construct()
  {
  }

  public function isAllowed(Entity $entity, $privilege)
  {
    $user = $this->getUser();
    return true;
  }

  protected function getParent(Entity $entity)
  {
    return false;
  }
}
