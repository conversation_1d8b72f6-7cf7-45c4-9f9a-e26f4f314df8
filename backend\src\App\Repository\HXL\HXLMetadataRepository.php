<?php

/**
 * <PERSON><PERSON><PERSON>di HXLMetadata Repository, using <PERSON>hana::$config
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package   Ushahidi\Application
 * @copyright 2014 Ushahidi
 * @license   https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */
namespace Ushahidi\App\Repository\HXL;

use Oh<PERSON>zee\DB;
use <PERSON><PERSON><PERSON><PERSON>\Core\SearchData;
use <PERSON><PERSON><PERSON>di\Core\Entity\HXL\HXLMetadata;
use <PERSON><PERSON><PERSON>di\Core\Entity\HXL\HXLMetadataRepository as HXLMetadataRepositoryContract;
use Us<PERSON>hidi\App\Repository\OhanzeeRepository;

class HXLMetadataRepository extends OhanzeeRepository implements
    HXLMetadataRepositoryContract
{
    // OhanzeeRepository
    protected function getTable()
    {
        return 'hxl_meta_data';
    }

    public function getSearchFields()
    {
        return ['dataset_title', 'export_job_id'];
    }


    /**
     * @param SearchData $search
     * Search by dataset_title and export_job_id
     */
    public function setSearchConditions(SearchData $search)
    {
        $query = $this->search_query;
        if ($search->dataset_title) {
            $query->where('dataset_title', '=', $search->dataset_title);
        }
        return $query;
    }

    public function getEntity(array $data = null)
    {
        return new HXLMetadata($data);
    }
}
