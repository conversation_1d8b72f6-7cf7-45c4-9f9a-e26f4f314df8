<?php

// UEH-146 - Check Location mapping in backend when incidents are created through SMS

use Phinx\Migration\AbstractMigration;

class UpdatePostLocation extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */

  /**
   * Migrate Up.p
   */
  public function up()
  {
    // Switch the county and district values for existing posts which was created by SMS
    $this->execute("UPDATE posts SET posts.mgmt_lev_1 = (@temp:=posts.mgmt_lev_1), posts.mgmt_lev_1 = posts.mgmt_lev_2, posts.mgmt_lev_2 = @temp where posts.mgmt_lev_1 NOT IN ('Grand Gedeh','Grand Kru','Rivercess','Gbarpolu','Sinoe','Nimba','Bong','Montserrado','River Gee','Grand Cape Mount','Grand Bassa','Bomi','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','Maryland')");
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
