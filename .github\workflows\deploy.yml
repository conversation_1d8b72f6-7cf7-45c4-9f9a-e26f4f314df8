name: Deploy
on:
  push:
    branches:
      - peru-prod

jobs:
  deploy_all:
    runs-on: ubuntu-latest
    env:
      FLEXMONSTER_KEY_ACCESS_TOKEN: ${{ secrets.FLEXMONSTER_KEY}} 
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Cache modules
        uses: actions/cache@v1
        id: yarn-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

        # Check for changes in the frontend directory
      - name: Determine frontend changes
        id: frontend-changes
        run: |
          CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})

          if echo "$CHANGED_FILES" | grep -q "frontend/"; then
            echo "Frontend changes detected."
            echo "::set-output name=frontend-changes::true"
          else
            echo "No frontend changes detected."
            echo "::set-output name=frontend-changes::false"
          fi

      # Check for changes in the backend directory
      - name: Determine backend changes
        id: backend-changes
        run: |
          CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})

          if echo "$CHANGED_FILES" | grep -q "backend/"; then
            echo "Backend changes detected."
            echo "::set-output name=backend-changes::true"
          else
            echo "No backend changes detected."
            echo "::set-output name=backend-changes::false"
          fi

      - name: Install dependencies
        if: steps.frontend-changes.outputs.frontend-changes == 'true'
        working-directory: ./frontend
        run: |
          npm install -g @angular/cli@8.3.20
          npm install --legacy-peer-deps
          ng build hybrid-app -c=prod --output-path=./dist
      - name: Deploy Frontend
        if: steps.frontend-changes.outputs.frontend-changes == 'true'
        run: aws s3 sync ./frontend/dist s3://ireport-pe-web
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Build, tag, and push the backend image to Amazon 
        if: steps.backend-changes.outputs.backend-changes == 'true'
        id: build-image
        working-directory: ./backend
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ireport-platform-api
          IMAGE_TAG: peru
        run: |
          # Build a docker container and push it to ECR 
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          echo "Pushing image to ECR..."
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
