<?php

// UJEVMS-62 - <PERSON><PERSON><PERSON>, <EMAIL> - 17/02/2020.

namespace <PERSON><PERSON>hidi\Core\Tool\Authorizer;

use <PERSON><PERSON><PERSON><PERSON>\Core\Entity;
use <PERSON><PERSON><PERSON>di\Core\Tool\Authorizer;
use <PERSON><PERSON><PERSON>di\Core\Traits\PrivAccess;
use <PERSON><PERSON><PERSON>di\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivateDeployment;

class WoredaContextRiskAuthorizer implements Authorizer
{
    // The access checks are run under the context of a specific user
    use UserContext;

    use PrivAccess;

    use PrivateDeployment;

  protected function getAllPrivs()
    {
        return ['read', 'create', 'update', 'delete', 'search', 'read_full', 'register'];
    }


  public function isAllowed(Entity $entity, $privilege)
  {
    $user = $this->getUser();
    return true;
  }

  protected function getParent(Entity $entity)
  {
    return false;
  }
}
