<?php

// UJEVMS-52 - SCAL<PERSON> Luca, <EMAIL> - 27/01/2020.

namespace <PERSON><PERSON><PERSON><PERSON>\App\Repository;

use <PERSON><PERSON><PERSON>\DB;
use <PERSON><PERSON><PERSON>di\Core\Entity;
use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\UsersExtra;
use <PERSON><PERSON><PERSON><PERSON>\Core\Traits\UserContext;
use <PERSON><PERSON>hidi\Core\Entity\UsersExtraRepository as UsersExtraRepositoryContract;

class UsersExtraRepository extends OhanzeeRepository implements UsersExtraRepositoryContract
{
  	use UserContext;
  	private $userId;

  	public function getEntity(array $data = null) { 
    	$data = [ 'user_data' => json_encode($data) ];
    	return new UsersExtra($data);
  	}

  	protected function getTable() {
    	return 'users_ex';
  	}

  	public function getUserExtra($userId) {
    	return DB::select('user_id')
					->from('users_ex')
					->where('users_ex.user_id', '=', $userId)
					->execute($this->db())
					->as_array();		
	}

  	public function getByUserId($user_id) {
		$entityAsArray	=	DB::select('users_ex.*')
								->from('users_ex')
								->where('users_ex.user_id', '=', $user_id)
								->limit(1)
								->execute($this->db())
								->as_array();

		return (count($entityAsArray) === 0)?[]:$entityAsArray[0];
  	}

  	public function create(Entity $entity) {
		
		$record 	= array_filter($entity->asArray());
		$values 	= $entity->values;
	
		if (!empty($record['user_data'])) {
			$userData			= json_decode($record["user_data"], true);
			$this->userId		= isset($userData["user_id"])? $userData["user_id"]: $this->getUserId();
			$region		 		= isset($userData['region']) ? $userData["region"] : '';
			$zone				= '';
			$woreda				= '';
			$institution		= '';

			$isUserExtra 		= $this->getUserExtra($this->userId);
			
			if (empty($isUserExtra)) {
				// Create USER EXTRAS
				$created 		= time();
				$values 		= array_map(function ($user) use ($record, $created, $institution,$woreda,$zone,$region) {
					return  [$this->userId, $user, $created, $created, $institution,$woreda,$zone,$region ];
				}, $record);
				
				$insertQuery 	= DB::insert('users_ex', ['user_id', 'user_data', 'created', 'updated', 'institution','woreda','zone','region' ]);
				foreach ($values as $value) {
					$insertQuery->values($value);
				}
				return $insertQuery->execute($this->db());
			} else {
				// Update USER EXTRAS		
				$updateQuery = DB::update('users_ex')
								->set	([	'user_data' 	=> $record, 
											'updated'		=> time(), 
											'institution' 	=> $institution 
										])
								->where	('user_id', '=', $this->userId);						
				return $updateQuery->execute($this->db());
			}
		}
		return $this->executeInsert($this->removeNullValues($record));
  	}

  	public function getSearchFields() {
    	return [ 'user_id' ];
  	}
}
