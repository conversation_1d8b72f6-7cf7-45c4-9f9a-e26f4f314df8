<?php

// UJEVMS-65 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 08/04/2020.

use Phinx\Migration\AbstractMigration;

class AlterJobsLangColumns extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */

  /**
   * Migrate Up.p
   */
  public function up()
  {
    $this->table('export_job')
      ->addColumn('lang', 'string', [
        'default' => null])
      ->update();
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
