<?php

// UCD-258 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 10/11/2020.

use Phinx\Migration\AbstractMigration;

class AddExportIdToPosts extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

   // $this->execute("ALTER TABLE posts DROP COLUMN import_post_id;");
    $this->table('posts')
      ->addColumn('import_post_id', 'integer', [
        'default' => 0,
        'comment' => 'Contain post id of imported incidents',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
