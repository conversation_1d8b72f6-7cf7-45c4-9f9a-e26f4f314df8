<?php

use Phinx\Migration\AbstractMigration;

class AddDefaultCountryCodeInTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
       
        $zimbabwe_default_country_code  = 'ZW';
        $liberia_default_country_code  = 'LR';
        $madagascar_default_country_code  = 'MG';
        $peru_default_country_code  = 'PE';
        $malawi_default_country_code  = 'MW';
      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'default_country_code', '$zimbabwe_default_country_code')");
      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'default_country_code', '$liberia_default_country_code')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'default_country_code', '$madagascar_default_country_code')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'default_country_code', '$peru_default_country_code')");

        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'default_country_code', '$malawi_default_country_code')");


    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
