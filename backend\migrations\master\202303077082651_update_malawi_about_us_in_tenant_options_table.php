<?php

use Phinx\Migration\AbstractMigration;

class UpdateMalawiAboutUsInTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {

        // Liberia About Us Section
        // $liberia_about_us_section = array(
        //     'about_us' => [
        //         'image' => 'about_us.png',
        //         'who_we_are' => [
        //             'title' => 'Who We Are',
        //             'description' => 'The Liberia Peacebuilding Office (PBO), based in the Ministry of Internal Affairs (MIA), was established in January 2009 jointly by the United Nations and the Government of Liberia with a primary function of supporting the Government of Liberia in the development and implementation of peace and reconciliation programs. It serves as the specialized program for Government\'s peace and reconciliation related to the development of policy and strategies; provides strategic advice on issues of peace, reconciliation and social cohesion. The PBO also has a convening role in coordination and collaboration with all key actors including government institutions, partners, private sector and civil society. PBO further undertakes periodic conflict mapping/analysis, undertake measures to address various conflict issues across the country; closely working with the Independent National Commission on Human Rights (INCHR), Office of the National Peace Ambassador (ONPA), National Traditional Council of Liberia (NTCL), as well as with local peace structures in support of the Ministry of Internal Affairs and national partners’ agenda for peace consolidation.<br/><br/>The PBO has a broad mandate of providing conflict sensitivity analysis on the government\'s development policies and addressing the root causes and potential triggers of conflict in the country, as well as building capacities of peace structures to prevent and amicably resolve conflicts. The PBO further develops programs, strategies and facilitates peace building coordination and coherence to measure cumulative impact of peace building in Liberia.<br/><br/>To strengthen national and local capacities for peace building, reconciliation and the prevention of violent conflicts; and the vision of the PBO is a Liberia that demonstrates social cohesion, peaceful and reconciled based on the rule of law.<br/><br/>In pursuit of the above mission and vision statements, the PBO is committed at all times to the following core values: EQUITY, GENDER RESPONSIVENESS, TOLERANCE, INTEGRITY, INCLUSIVENESS, and ACCOUNTABILITY.',
        //         ],
        //         'how_we_work' => [
        //             'title' => 'How We Work',
        //             'description' => 'The PBO in its quest to finding lasting and sustained peace and reconciliation in Liberia and working along with our peace structures in the 15 counties; and including civil society organizations; and with support from the Government of Liberia and partners; carries out the following programs and activities:<br/><br/><ul><li>Dialogues and mediation on land boundary disputes; civic engagements; political reconciliation dialogues; inter-tribal conflict dialogues; capacity building trainings and workshops; conflict early warning data collection and reporting; election disputes management trainings; election monitoring; coordination and reporting; host and manage the Early Warning and Early Response (LERN/IREPORT) platforms.</li></ul>The early warning system of the Liberia Peacebuilding Office is a chain of conflict early warning information communication system that focuses on systematic data collection, analysis, and/or formulation of recommendations, including risk assessment and information sharing. It allows for the early identification of potential threats that could lead to the emergence of new conflict or the expansion of the existing conflict; it creates conditions necessary for all stakeholders to work toward the prevention and/or containment of violence and the promotion of reconciliation and the rule of law.<br/><br/><br/><ul><li>County Peace Committees (CPCs); Early Warning and Early Response Reporters (EWER); Youth Agents of Peace (YAPs); Regional Early Warning Hubs; Early Warning Working Group (EWWG); Covid 19, Election Monitoring and Violence Prevention Situation Room (CEMViP-SR); Peacebuilding and Reconciliation Technical Working Group (PRTWG- PAPD Pillar 3 –Sustaining the Peace); and LERN (www.pbolern.com) and iReport platform.</li></ul>The LERN platform is the publicly available website intended to showcase different incidents (i.e., Security, Health and Environment, Gender, Governance and Human Rights Violations and Elections) occurring in Liberia to all interested citizens and organizations both in Liberia and internationally. The iReport platform, linked to LERN, is an interface intended for PBO and partners facilitating the early identification, transmission, verification and response of incidents occurring in all 15 counties in Liberia.<br/><br/>The PBO is supported by the United Nations Development Programme (UNDP), through the Liberia Electoral Support Project (LESP) and the UNDP Electoral Task Force on Electoral Assistance.<br/><br/>'
        //         ],
        //         'logos' => [
        //             'liberia-pbo-logo.png',
        //             'pnud-logo.png',
        //         ],
        //         'donors_info' => [
        //             'title' => 'Donors',
        //             'description' => 'The Early Warning and Early Response (EWER) system and the development of the LERN/iReport platforms is funded by the Embassy of Ireland, Embassy of Sweden, European Union Delegation and United Nations Peacebuilding Fund.',
        //         ],
        //         'donors_logos' => [
        //             'unpf.png',
        //             'eu.png',
        //             'ss.jpg',
        //             'iar.jpg',
        //         ],
        //     ],
        // );
        // $liberia_about_us_section = json_encode($liberia_about_us_section, JSON_UNESCAPED_SLASHES);
        // $this->execute('UPDATE tenant_options SET tenant_value = "'.addslashes($liberia_about_us_section).'" WHERE tenant_id = "2" AND tenant_key = "about_us"');

        // $liberia_qot_query_section = array(
        //     'got_query' => ['title' => 'Contact', 'description' => 'For any further information or request, please contact the Peacebuilding Office by clicking on <b>contact us</b>.<br/>We will get back to you within the shortest delay possible.'],
        // );
        // $liberia_qot_query_section = json_encode($liberia_qot_query_section, JSON_UNESCAPED_SLASHES);
        // $this->execute("UPDATE tenant_options SET tenant_value = '$liberia_qot_query_section' WHERE tenant_id = '2' AND tenant_key = 'got_query'");

        
        // // Liberia Looking for Incident Alert
        // $this->execute("DELETE from tenant_options WHERE tenant_id = 2 AND tenant_key = 'looking_for_alerts'");
        // $liberia_alert_section = array(
        //     'looking_for_alerts' => ['title' => 'Incident Alerts', 'description' => 'Sign up to receive incident alerts by email by clicking on <b>Get Alerts</b>.'],
        // );
        // $liberia_alert_section = json_encode($liberia_alert_section);
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'looking_for_alerts', '$liberia_alert_section')");
        $malawi_about_us_section = array(
            'about_us' => [
                'image' => 'about_us.png',
                'who_we_are' => [
                    'title' => 'Who We Are',
                    'description' => 'Maso Athu (iReport) Malawi is a cutting-edge digital platform dedicated to monitoring, documenting, and reporting of incidents of electoral violence, human rights violations, and other threats to democratic integrity. Hosted by the National Initiative for Civic Education (NICE) and the Malawi Peace and Unity Commission (MPUC), iReport is supported by the United Nations Development Programme (UNDP), which provides technical expertise and financial support. This backing from a global organisation underscores the platform\'s importance in safeguarding free, fair, and peaceful elections.',
                ],
                'how_we_work' => [
                    'title' => 'How We Work',
                    'description' => 'How We Work.<br/><br/>'
                ],
                'logos' => [
                    'MPAUC_logo.png',
                    'nice_logo.png',
                ],
                'donors_info' => [
                    'title' => 'Donors',
                    'description' => 'The Early Warning and Early Response (EWER) system and the development of the LERN/iReport platforms is funded by the Embassy of Ireland, Embassy of Sweden, European Union Delegation and United Nations Peacebuilding Fund.',
                ],
                'donors_logos' => [
                    'EU_LOGO.png',
                    'Embassy_of_Ireland.jpg',
                    'USAID-Identity.svg.png',
                    'Norway_Logo_Positive_RGB.png',
                    'UK_International_Development_logo.png',
                    'UN_Women_logo_en.png',
                    'UNDP-Logo-Blue-Large-Transparent.png'
                ],
            ],
        );

        $malawi_about_us_section = json_encode($malawi_about_us_section, JSON_UNESCAPED_SLASHES);
        $this->execute('UPDATE tenant_options SET tenant_value = "'.addslashes($malawi_about_us_section).'" WHERE tenant_id = "1" AND tenant_key = "about_us"');

        $peru_qot_query_section = array(
            'got_query' => ['title' => 'Contact', 'description' => 'For any further information or request, please contact the Peacebuilding Office by clicking on <b>contact us</b>.<br/>We will get back to you within the shortest delay possible.'],
        );
        $peru_qot_query_section = json_encode($peru_qot_query_section, JSON_UNESCAPED_SLASHES);
        $this->execute("UPDATE tenant_options SET tenant_value = '$peru_qot_query_section' WHERE tenant_id = '1' AND tenant_key = 'got_query'");

        
        // Liberia Looking for Incident Alert
        $this->execute("DELETE from tenant_options WHERE tenant_id = 1 AND tenant_key = 'looking_for_alerts'");
        $peru_alert_section = array(
            'looking_for_alerts' => ['title' => 'Incident Alerts', 'description' => 'Sign up to receive incident alerts by email by clicking on <b>Get Alerts</b>.'],
        );
        $peru_alert_section = json_encode($peru_alert_section);
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'looking_for_alerts', '$peru_alert_section')");
    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
