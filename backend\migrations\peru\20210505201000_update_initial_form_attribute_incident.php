<?php

// UJEVMS-56 - SC<PERSON><PERSON>, <EMAIL> - 11/02/2020.
// This migration will initialize the form attributes and tags.

use Phinx\Migration\AbstractMigration;

class UpdateInitialFormAttributeIncident extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

    $this->table('tags')
      ->changeColumn('type', 'string', [
        'limit' => 150,
        'default' => 'category',
      ])
      ->update();

    $target = json_encode(['Individual candidates (identify political party and candidate)', 'Political party (identify political party)', 'Candidate / political party supporters (identify political party and candidate)', 
     'ECZ election material (polling stations, ballots, voter registers and voting equipment)', 'Election officials (voter registrars / poll workers)', 'Media or journalists (identify media organizations or as independent)',
     'election observers (identify election observer organization)', 'Security forces', 'Voters', 'Traditional or religious leaders (identify region or group)',
     'Women','Youth','Person with disabilities','Other']);

    $perpetrators = json_encode(['Individual candidates','Political party','Political party cadres (identify political party)', 'Youth groups (not necessarily connected to political parties)',
     'Traditional or religious leaders (identify)', 'Security forces', 'Individual citizen, citizen groups including civil society organizations', 'Media or journalists', 'Election officials', 'Criminal elements', 'Unknown', 
     'Other'
     ]);

    $means = json_encode(['Verbal intimidation', 'Social media', 'traditional media','Physical intimidation', 'Explosives', 'Firearms', 'Cold weapon (machetes / knives / pangas / axes)', 'Sticks / stones', 'Fight', 'Unknown', 'Other']);

    $venue = json_encode(['Election office', 'Registration site or polling site', 'Political party office or candidate office', 'Government office', 'Private residence', 'Place of worship', 'Police station', 'media office', 'Election observer office', 'In movement (in the street)',
     'Political rallies / meeting', 'Market / bus station', 'Other']);
    
    $this->execute("
      start transaction;

      -- Setting form attributes.
      set FOREIGN_KEY_CHECKS = 0;
      truncate table form_attributes;
      set FOREIGN_KEY_CHECKS = 1;
      
      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Title of incident', 'text', 'title', 1, 1, NULL, 0, '[]', 1);
      
      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Description', 'text', 'description', 0, 2, NULL, 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Date/Time', 'datetime', 'datetime', 0, 3, NULL, 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Status', 'select', 'varchar', 0, 4, '[\"Ongoing\", \"Past\"]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Location', 'location', 'point', 0, 5, NULL, 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Source of information', 'select', 'varchar', 0, 6, '[\"Mainstream media\",\"Social media\",\"Word of mouth\",\"Other\"]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values ('source_of_info_other', 'Please specify', 'text', 'varchar', 0, 7, '[]', 0, '[]', 1);
      
      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Type of incident', 'tags', 'tags', 0, 8, NULL, 0, '[]', 1);    

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Election related', 'select', 'varchar', 0, 9, '[\"Yes\",\"No\",\"Unknown\"]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Are women the main target', 'select', 'varchar', 0, 10, '[\"Yes\",\"No\",\"Unknown\"]', 0, '[]', 1);  
      
      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Target', 'select', 'varchar', 0, 11, '$target', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values ('target_other', 'Please specify', 'text', 'varchar', 0, 12, '[]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Alleged perpetrators', 'select', 'varchar', 0, 13, '$perpetrators', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values ('first_perpetrator_other', 'Please specify', 'text', 'varchar', 0, 14, '[]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Second alleged perpetrators', 'select', 'varchar', 0, 15, '$perpetrators', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values ('second_perpetrator_other', 'Please specify', 'text', 'varchar', 0, 16, '[]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Means', 'select', 'varchar', 0, 17, '$means', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values ('means_other', 'Please specify', 'text', 'varchar', 0, 18, '[]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Incident venue', 'select', 'varchar', 0, 19, '$venue', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values ('venue_other', 'Please specify', 'text', 'varchar', 0, 20, '[]', 0, '[]', 1);
    
      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Intensity - n° of casualties', 'number', 'int', 0, 21, '[]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Intensity - n° of injured', 'number', 'int', 0, 22, '[]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Intensity - n° of property damage', 'number', 'int', 0, 23, '[]', 0, '[]', 1);

      insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, cardinality, config, form_stage_id) 
      values (uuid(), 'Intensity - n° of displaced people', 'number', 'int', 0, 24, '[]', 0, '[]', 1);
      
      
      update form_attributes set `default` = '';

      -- Setting tags.
      set FOREIGN_KEY_CHECKS = 0;
      truncate table tags;
      set FOREIGN_KEY_CHECKS = 1;

      insert into tags (tag, slug, type, priority) values ('Killing', 'killing', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Shooting', 'shooting', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Confrontation between actors', 'Confrontation between actors', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Physical assaults', 'physical assaults', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Gender based violence in elections', 'gender based violence in elections', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Ferrying of people/forcing people to political/electoral activities', 'ferrying of people/forcing people to political/electoral activities', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Disruption of campaigning activities, disfiguring of campaign material', 'disruption of campaigning activities, disfiguring of campaign material', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Obstruction / declaration of no-go areas', 'obstruction / declaration of no-go areas', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Displacement of voters /communities', 'displacement of voters /communities', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Property destruction / arson', 'property destruction / arson', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Protest or demonstrations', 'protest or demonstrations', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Dissemination of mis- and disinformation / malicious rumors', 'dissemination of mis- and disinformation / malicious rumors', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Dissemination of disturbing materials or posters', '', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Tribal talk / violent communication', 'tribal talk / violent communication', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Incitement of violence / hate speech', 'Incitement of violence / hate speech', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Pressure, threats and intimidation', 'pressure, threats and intimidation', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Fraud / bribery voting influence', 'fraud / bribery voting influence', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Selective application of the law / violation of the law', 'Selective application of the law / violation of the law', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Arbitrary arrest/detention', 'arbitrary arrest/detention', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Unlawful detention', 'unlawful detention', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Victimization', 'victimization', 'category', 1);
      insert into tags (tag, slug, type, priority) values ('Other', 'Other', 'category', 1);      
      commit;
      ");

      $rows = $this->fetchAll(
            "SELECT *
                FROM form_attributes"
        );

      foreach ($rows as $row) {
        

        if ($row['type'] === 'tags') {
          $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"22\"}}]'
          where label = 'Election related' OR label = 'Are women the main target';
          ");

          $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"3\"}}]'
          where label = 'Second alleged perpetrators';
          ");

          $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"1\"}}, {\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"2\"}}, {\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"4\"}}, {\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"10\"}}]'
          where input = 'number';
          ");

        } elseif (strtolower($row['label']) === 'target') {
           $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"Other\"}}]'
          where form_attributes.key = 'target_other';
          ");

        } elseif (strtolower($row['label']) === 'source of information') {
           $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"Other\"}}]'
          where form_attributes.key = 'source_of_info_other';
          ");

        } elseif (strtolower($row['label']) === 'alleged perpetrators') {
           $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"Other\"}}]'
          where form_attributes.key = 'first_perpetrator_other';
          ");

        } elseif (strtolower($row['label']) === 'second alleged perpetrators') {
           $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"Other\"}}]'
          where form_attributes.key = 'second_perpetrator_other';
          ");

        } elseif (strtolower($row['label']) === 'means') {
           $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"Other\"}}]'
          where form_attributes.key = 'means_other';
          ");

        } elseif (strtolower($row['label']) === 'incident venue') {
           $this->execute("
          update form_attributes set config = '[{\"id\":1,\"action\":\"show\",\"target\":{\"key\":\"$row[key]\",\"value\":\"Other\"}}]'
          where form_attributes.key = 'venue_other';
          ");
        }
        
      }


  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
