<?php

use Phinx\Migration\AbstractMigration;

class AddPostCommentsNoActionTaken extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('post_comments')
      ->addColumn('no_action_taken', 'boolean', [
        'default' => false,
        'comment' => 'Action taken or not',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
