<?php

use Phinx\Migration\AbstractMigration;

class CreateAdditionalPostCommentsTable extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {
        $this->table('additional_post_comments')
            ->addColumn('message', 'string', ['null' => false])
            ->addColumn('post_id', 'integer', ['default' => 0])
            ->addcolumn('status', 'integer', ['default' => 0])
            ->addColumn('created', 'integer', ['default' => 0])
            ->addColumn('author', 'string', ['default' => 'public'])
            ->create();

        $this->execute("ALTER TABLE `additional_post_comments` CHANGE `status` `status` ENUM('published', 'deleted') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'published';");

    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        $this->dropTable('additional_post_comments');
    }
}
