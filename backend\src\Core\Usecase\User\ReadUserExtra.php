<?php

namespace <PERSON><PERSON><PERSON>di\Core\Usecase\User;

use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\ReadUsecase;

class ReadUserExtra extends ReadUsecase
{
  protected function getEntity()
  {
    $user_id = $this->getRequiredIdentifier('user_id');
    return $this->repo->getByUserId($user_id);
  }

  public function interact()
  {
    $entity = $this->getEntity();
    return $entity;
  }
}
