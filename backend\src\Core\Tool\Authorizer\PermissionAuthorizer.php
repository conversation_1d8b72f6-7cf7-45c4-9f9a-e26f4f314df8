<?php

/**
 * <PERSON>hahidi Permission Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use <PERSON>hahidi\Core\Tool\Authorizer;
use Us<PERSON>hidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Traits\PrivAccess;

class PermissionAuthorizer implements Authorizer
{
    use UserContext;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;

    // Check if user has Admin access
    use AdminAccess;

    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {
        // These checks are run within the user context.
        $user = $this->getUser();

        // UJEVMS-51 - <PERSON><PERSON><PERSON> <PERSON>, <EMAIL> - 21/01/2020.
        if ($user) return true;

        // Only allow admin access
        if (
            $this->isUserAdmin($user)
            && in_array($privilege, ['search', 'read'])
        ) {
            return true;
        }

        return false;
    }
}
