<?php

/**
 * Ushahidi User Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use Ushahidi\Core\Entity\User;
use Us<PERSON>hidi\Core\Entity\Permission;
use <PERSON>hahidi\Core\Tool\Authorizer;
use Ushahidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\UserContext;
use Us<PERSON>hidi\Core\Traits\PrivAccess;
use Us<PERSON>hidi\Core\Traits\PrivateDeployment;
use Ushahidi\Core\Tool\Permissions\AclTrait;

// The `UserAuthorizer` class is responsible for access checks on `Users`
class UserAuthorizer implements Authorizer
{
    // The access checks are run under the context of a specific user
    use UserContext;

    // - `AdminAccess` to check if the user has admin access
    use AdminAccess;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;

    // It uses `PrivateDeployment` to check whether a deployment is private
    use PrivateDeployment;

    // Check that the user has the necessary permissions
    use AclTrait;

    /**
     * Get a list of all possible privilges.
     * By default, returns standard HTTP REST methods.
     * @return Array
     */
    protected function getAllPrivs()
    {
        return ['read', 'create', 'update', 'delete', 'search', 'read_full', 'register'];
    }

    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {
        // These checks are run within the user context.
        $user = $this->getUser();

        // Only logged in users have access if the deployment is private
        if (!$this->canAccessDeployment($user)) {
            return false;
        }

        // User should not be able to delete self
        if ($privilege === 'delete' && $this->isUserSelf($entity)) {
            return false;
        }

        // Role with the Manage Users permission can manage all users
        // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/08/2020.
        // if ($this->acl->hasPermission($user, Permission::ASSIGN_SUPER_ROLE)
        //     && $this->acl->hasPermission($user, Permission::CREATE_USERS)
        //     && $this->acl->hasPermission($user, Permission::ASSIGN_ROLES)
        //     && $this->acl->hasPermission($user, Permission::DELETE_ROLES)
        // ) {
        //     return true;
        // }

        // Admin user should be able to do anything - short of deleting self
        // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/08/2020.
        if ($this->isUserAdmin($user)) {
            return true;
        }

        if ($this->acl->hasPermission($user, Permission::ACCESS_AND_MODIFY_PARAMETERS_SETTINGS)) {
            return true;
        }

        // User cannot change their own role
        if ('update' === $privilege && $this->isUserSelf($entity) && $entity->hasChanged('role')) {
            return false;
        }

        // Regular user should be able to update and read_full only self
        if ($this->isUserSelf($entity) && in_array($privilege, ['update', 'read_full', 'read'])) {
            return true;
        }

        // Users should always be allowed to register
        // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/08/2020.
        // && $this->acl->hasPermission($user, Permission::CREATE_USERS)
        if ($privilege === 'register') {
            return true;
        }

        // UJEVMS-56 - SCALA Luca, <EMAIL> - 30/01/2020.
        //$this->acl->hasPermission($user, Permission::LIST_USERS) && 
        if (in_array($privilege, ['search', 'read'])) {
            return true;
        }

        // If no other access checks succeed, we default to denying access
        return false;
    }
}
