<?php

use Phinx\Migration\AbstractMigration;

class AddIncidentReportAttributes extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {

        // /home/<USER>/Projects/UNDP/ireport-malawi-prod/backend/migrations/malawi/20210505201000_update_initial_form_attribute_incident.php


        $this->execute("
            start transaction;

            -- Setting form attributes.
            set FOREIGN_KEY_CHECKS = 0;
            truncate table form_attributes;
            set FOREIGN_KEY_CHECKS = 1;

            commit; ");


        $connection = $this->getAdapter()->getConnection();
        $adapter = $this->getAdapter();

        $form_stage_id = 1;

        $affected_groups = '["Women", "Youth", "Persons with Albinism", "Persons with Disabilities", "Elderly"]';

	$connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('IR_TITLE', 'Title of incident', 'text', 'title', 1, 1, NULL,  '[]', :form_stage_id ),
                ( 'IR_DESCRIPTION', 'Description', 'text', 'description', 1, 2, NULL, '[]', :form_stage_id ),
                ( 'IR_SECURITY', 'Security', 'tags', 'tags', 1, 3, '[]', '[]', :form_stage_id ),
                ( 'IR_HEALTH_AND_ENVIRONMENT', 'Health and environment', 'tags', 'tags', 1, 4, '[]', '[]', :form_stage_id ),
                ( 'IR_GENDER', 'Gender', 'tags', 'tags', 1, 5, '[]', '[]', :form_stage_id ),
                ( 'IR_GOVERNANCE_AND_HUMAN_RIGHTS_VIOLATIONS', 'Governance and human rights violations', 'tags', 'tags', 1, 3, '[]', '[]', :form_stage_id ),
                ( 'IR_CAMPAIGN_ELECTIONS', 'Campaign/Elections', 'tags', 'tags', 1, 6, '[]', '[]', :form_stage_id ),
                ( 'IR_MEDIA_AND_INFORMATION', 'Media and Information', 'tags', 'tags', 1, 7, '[]', '[]', :form_stage_id ),
                ( 'IR_AFFECTED_GROUPS', 'Affected Groups', 'select', 'varchar', 1, 8, '" . $affected_groups . "', '[]', :form_stage_id ),
                ( 'IR_DATE_TIME', 'Date and time', 'datetime', 'datetime', 1, 9, '[]', '[]', :form_stage_id ),
                ( 'IR_LOCATION', 'Location', 'location', 'point', 1, 10, '[]', '[]', :form_stage_id );
        "
    )->execute([ ':form_stage_id' => 1 ]); 
            
            





    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // Noop - too risky to delete a post type
    }
}
