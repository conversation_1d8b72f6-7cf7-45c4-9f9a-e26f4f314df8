<?php

// UJEVMS-69 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 07/09/2020.


use Phinx\Migration\AbstractMigration;

class UpdatePermissionRole extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

    $this->execute("
    start transaction;

    set FOREIGN_KEY_CHECKS = 0;
    
    update permissions set name = 'Access CEWER dashboard', description = 'Access CEWER dashboard' where name = 'Access eview dashboard';
    update permissions set name = 'Access incident/risk reports', description = 'Access incident/risk reports' where name = 'Access incident risk reports';
    update permissions set name = 'View incident/risk reporter identity', description = 'View incident/risk reporter identity' where name = 'View incident risk reporter identity';
    update permissions set name = 'Modify personal incident/risk reports', description = 'Modify personal incident/risk reports' where name = 'Modify personal incident risk reports';

    update roles_permissions set permission = 'Access CEWER dashboard' where permission = 'Access eview dashboard';
    update roles_permissions set permission = 'Access incident/risk reports' where permission = 'Access incident risk reports';
    update roles_permissions set permission = 'View incident/risk reporter identity' where permission = 'View incident risk reporter identity';
    update roles_permissions set permission = 'Modify personal incident/risk reports' where permission = 'Modify personal incident risk reports';
    set FOREIGN_KEY_CHECKS = 1;

    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
