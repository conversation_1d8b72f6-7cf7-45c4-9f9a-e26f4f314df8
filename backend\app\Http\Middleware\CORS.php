<?php

namespace <PERSON><PERSON><PERSON><PERSON>\App\Http\Middleware;

use Closure;

class CORS
{
  /**
   * Handle an incoming request.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  \Closure  $next
   * @return mixed
   */
  public function handle($request, Closure $next)
  {
    $resheaders = [
      'Access-Control-Allow-Origin'      => '*',
      'Access-Control-Request-Method'    => '*',
      'Access-Control-Allow-Methods'     => 'DELETE, GET, OPTIONS, POST, PUT',
      'Access-Control-Allow-Headers'     => '*'
    ];

    $reqHeaders = $request->headers->all();
    if (isset($reqHeaders['origin'])) {
      $resheaders['Access-Control-Allow-Origin'] = $reqHeaders['origin'];
    }

    if ($request->isMethod('OPTIONS')) {
      return response()->json('{"method":"OPTIONS"}', 200, $resheaders);
    }

    $response = $next($request);

    foreach ($resheaders as $key => $value) {
      $response->header($key, $value);
    }

    return $response;
  }
}
