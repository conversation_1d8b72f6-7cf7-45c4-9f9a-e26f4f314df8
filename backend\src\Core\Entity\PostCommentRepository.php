<?php

// UJEVMS-52 - <PERSON><PERSON><PERSON>, <EMAIL> - 27/01/2020.

namespace <PERSON><PERSON>hidi\Core\Entity;

use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\CreateRepository;
use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\SearchRepository;
use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\UpdateRepository;

interface PostCommentRepository extends
  CreateRepository,
  UpdateRepository,
  SearchRepository
{
}
