<?php

// UJEVMS-52 - <PERSON><PERSON><PERSON>, <EMAIL> - 27/01/2020.

namespace <PERSON><PERSON>hidi\App\Validator\Post;

use <PERSON><PERSON><PERSON><PERSON>\App\Validator\LegacyValidator;
use <PERSON><PERSON><PERSON>di\Core\Entity\PostRepository;

class UpdateComment extends LegacyValidator
{
  protected $post_repo;

  public function __construct(PostRepository $post_repo)
  {
    $this->post_repo = $post_repo;
  }

  protected function getRules()
  {
    return [
      'post_id' => [
        [[$this->post_repo, 'exists'], [':value']],
      ],
    ];
  }
}
