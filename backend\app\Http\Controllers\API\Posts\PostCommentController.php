<?php

// UJEVMS-52 - SC<PERSON><PERSON>, <EMAIL> - 27/01/2020.

namespace Ushahidi\App\Http\Controllers\API\Posts;

use <PERSON><PERSON>hidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;

class PostCommentController extends RESTController
{
  protected function getResource()
  {
    return 'post_comments';
  }

  protected function getIdentifiers(Request $request)
  {
    return $this->getRouteParams($request);
  }

  protected function getFilters(Request $request)
  {
    $params = $this->getRouteParams($request);
    return $params;
  }

  public function index(Request $request)
  {
    $this->usecase = $this->usecaseFactory
      ->get($this->getResource(), 'search')
      ->setFilters($this->getFilters($request));

    return $this->prepResponse($this->executeUsecase($request), $request);
  }

  public function delete(Request $request)
  {
    $this->usecase = $this->usecaseFactory
      ->get($this->getResource(), 'delete')
      ->setIdentifiers($this->getIdentifiers($request));

    return $this->prepResponse($this->executeUsecase($request), $request);
  }
}
