<?php
namespace <PERSON><PERSON><PERSON><PERSON>\App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ResetPassword extends Mailable
{
    use Queueable, SerializesModels;

    public $password;

    public function __construct($password)
    {
        $this->password = $password;
    }

    public function build()
    {
        return $this
            ->subject('NICE Account Recovery')
            ->view('emails.reset_password_template')
            ->with(['password' => $this->password]);
    }
}