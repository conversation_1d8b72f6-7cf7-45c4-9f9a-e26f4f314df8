<?php

// UJEVMS-56 - SC<PERSON><PERSON>, <EMAIL> - 11/02/2020.
// This migration will initialize the form attributes and tags.

use Phinx\Migration\AbstractMigration;

class UpdateIncidentReportTagsAttribute extends AbstractMigration
{
  /**
   * Migrate Up.
   */
  public function up()
  {

    // /home/<USER>/Projects/UNDP/ireport-malawi-prod/backend/migrations/malawi/20210505201000_update_initial_form_attribute_incident.php

    $security = [
      'Physical violence',
      'Homicide',
      'Robbery',
      'Destruction of property',
      'Arson',
      'Demonstrations (peaceful or violent)',
      'Drugs and trafficking',
      'Ritualistic killings'
    ];

    $health = [
      'Disease outbreaks (Covid, Ebola, etc.)',
      'Natural disasters',
      'Environmental pollution or illegal dumping'
    ];


    $gender = [
      'Physical violence',
      'Psychological violence',
      'Sexual violence',
      'Restrictions and lack of equal opportunities',
      'Cyber-bullying'
    ];


    $governance = [
      'Corruption and bribery',
      'Land/boundary disputes',
      'Torture, ill-treatment',
      'Non-discrimination and equality',
      'Presumption of innocence',
      'Accountability',
      'Equal participation',
      'Effective remedy',
      'Access to Education',
      'Health and safety',
      'Right to Peaceful assembly',
      'Illegal Arrests',
      'Use of force',
      'Expression of views',
      'Access to information',
      'Access to basic social services (roads, food, electricity, safe drinking water, etc.)'
    ];

    $campaign = [
      'Fraud and irregularities',
      'Vote buying',
      'Voter intimidation and coercion',
      'Intimidation or attacks on polling workers',
      'Disruption of polling at the Polling Station',
      'Quarrels among voters in the queue',
      'Fights among voters in the queue',
      'Incidents involving voters and security personnel',
      'People fainting or collapsing at the polling station',
      'Injuries reported at the polling station',
      'Medical emergencies at the polling station',
      'Campaigning by party representatives near the polling station',
      'Vote buying by party representatives',
      'Violence against women in elections',
      'Destruction of polling material or installations',
      'Voter trucking',
      'Observer Obstruction',
      'Illegal Campaign Activities'
    ];

    $media = [
      'Fake News',
      'Hate Speech',
      'Biased Coverage',
      'Censorship'
    ];


    $tag_string = '';


    $tag_string = $this->getTagString('security', $security);
    $tag_string .= $this->getTagString('health-and-environment', $health);
    $tag_string .= $this->getTagString('gender', $gender);
    $tag_string .= $this->getTagString('governance-and-human-rights-violations', $governance);
    $tag_string .= $this->getTagString('campaign/elections', $campaign);
    $tag_string .= $this->getTagString('media-and-information', $media);

    $tag_string =  rtrim($tag_string, ",");

    $this->execute("
      start transaction;

      -- Setting tags.
      set FOREIGN_KEY_CHECKS = 0;

                    INSERT INTO tags (tag, slug, type) values " . $tag_string . ";
    // insert into tags (tag, slug, type) values ('Physical violence', 'physical-violence', 'security');
    // insert into tags (tag, slug, type) values ('Homicide', 'homicide', 'security');
    // insert into tags (tag, slug, type) values ('Environmental pollution or illegal dumping', 'environmental-pollution-or-illegal-dumping', 'health-and-environment');
    // insert into tags (tag, slug, type) values ('Natural disasters', 'natural-disasters', 'governance-and-human-rights-violations');
    // insert into tags (tag, slug, type) values ('Natural disasters', 'natural-disasters', 'campaign/elections');
    // insert into tags (tag, slug, type) values ('Natural disasters', 'natural-disasters', 'media-and-information');

      set FOREIGN_KEY_CHECKS = 1;

      commit;
      ");



  }

public function getTagString($key, $array) {
   $tag_string = '';

  foreach ($array as $value) {
    $str_value = str_replace(" ", "-", str_replace("(", "" , str_replace(")", "", strtolower($value))));
    $tag_string .= "('" . $value . "', '" . $str_value . "', '". $key . "'),";
}

return $tag_string;
    
}
  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
