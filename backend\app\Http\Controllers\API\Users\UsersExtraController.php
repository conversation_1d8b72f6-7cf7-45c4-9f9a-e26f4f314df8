<?php

// UJEVMS-90 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 03/03/2020.

namespace <PERSON>hahidi\App\Http\Controllers\API\Users;

use <PERSON>hahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;

class UsersExtraController extends RESTController
{
	protected function getResource() {
    	return 'users_ex';
  	}

  	protected function getIdentifiers(Request $request) {
    	return $this->getRouteParams($request);
  	}

	/**
	 * Update current user last read notification Id
	 *
	 * POST /api/users/extra
	 *
	 */
  	public function store(Request $request) {
		  
    	$this->usecase = $this->usecaseFactory
							->get($this->getResource(), 'create')
							->setPayload($request->all());

    	return $this->prepResponse($this->executeUsecase($request), $request);
  	}

	/**
	 * Get current user last read notification Id
	 *
	 * Get /api/users/extra
	 *
	 */
  	public function index(Request $request)
  	{
		$userId 		= !is_null($request->get('user_id'))?$request->get('user_id'):$request->user()->id;
		$params 		= $this->getRouteParams($request);
		$this->usecase 	= $this->usecaseFactory->get($this->getResource(), 'read')->setIdentifiers([ 'user_id' => $userId ]);

    	return $this->prepResponse($this->executeUsecase($request), $request);
  	}
}
