<?php

use Phinx\Migration\AbstractMigration;

class UsersRegionEntries extends AbstractMigration
{
    public function up()
    {
        $this->table('users')->addColumn('mgmt_lev_1', 'string', ['null' => true])->update();
        $this->table('users')->addColumn('mgmt_lev_2', 'string', ['null' => true])->update();
        $this->table('users')->addColumn('mgmt_lev_3', 'string', ['null' => true])->update();
    }

    public function down()
    {
        $this->table('users')->removeColumn('region')->update();
        $this->table('users')->removeColumn('zone'  )->update();
        $this->table('users')->removeColumn('woreda')->update();
    }
}
