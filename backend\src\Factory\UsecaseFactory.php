<?php

/**
 * Ushahidi Platform Factory for Use Cases
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Factory;
use Ushahidi\App\Models\User As UserData;
use DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\QueryException;
class UsecaseFactory
{
    // Array of common use cases, mapped by action:
    //
    //     $actions = [
    //         'create' => $di->newFactory('Namespace\To\CreateUsecase'),
    //         'search' => $di->newFactory('Namespace\To\SearchUsecase'),
    //         ...
    //     ]
    //
    // Each action is reusable by any resource, but only if the endpoint
    // definition allows it.
    protected $actions = [];

    // Array of specific use casess, mapped by resource and action:
    //
    //     $map['messsages']['create'] = $di->lazyNew('Namespace\To\OverloadedUsecase');
    //
    // Actions correspond with usecases, resources with entity types.
    protected $map = [];

    /**
     * @param  AuthorizerFactory
     */
    protected $authorizers;

    /**
     * @param  FormatterFactory
     */
    protected $formatters;

    /**
     * @param  RepositoryFactory
     */
    protected $repositories;

    /**
     * @param  ValidatorFactory
     */
    protected $validators;

    /**
     * Uses collaborator factories to load use case interactors using
     * specific collaborators for the entity/resource type.
     *
     * @param  AuthorizerFactory $authorizers
     * @param  RepositoryFactory $repositories
     * @param  FormatterFactory  $formatters
     * @param  Array $actions
     * @param  Array $map
     */
    public function __construct(
        AuthorizerFactory $authorizers,
        DataFactory       $data,
        FormatterFactory  $formatters,
        RepositoryFactory $repositories,
        ValidatorFactory  $validators,
        array $actions,
        array $map
    ) {
        $this->authorizers  = $authorizers;
        $this->data         = $data;
        $this->formatters   = $formatters;
        $this->repositories = $repositories;
        $this->validators   = $validators;

        $this->actions = $actions;
        $this->map     = $map;
    }

    /**
     * Gets a usecase from the map by action. Loads the tools for the usecase
     * from the factories by resource and action.
     *
     *     $read_post = $usecases->get('posts', 'read');
     *
     * @param  String $resource
     * @param  String $action
     * @return Ushahidi\Core\Usecase
     */
    public function get($resource, $action)
    {
        if (isset($this->map[$resource][$action])) {
            $factory = $this->map[$resource][$action];
        } elseif (isset($this->actions[$action])) {
            $factory = $this->actions[$action];
        }

        if (empty($factory)) {
            throw new \Exception(sprintf('Usecase %s.%s is not defined', $resource, $action));
        }

        $usecase = $factory()
            ->setAuthorizer($this->authorizers->get($resource))
            ->setRepository($this->repositories->get($resource))
            ->setFormatter($this->formatters->get($resource, $action))
            ;

        if ($usecase->isWrite()) {
            $usecase->setValidator($this->validators->get($resource, $action));
        }

        if ($usecase->isSearch()) {
            $usecase->setData($this->data->get($action));
        }

        return $usecase;
    }

    public function userExportData(){
        return DB::table('users')
            ->select('users.*','users.id as uid','contacts.contact','contacts.type','roles.short_name as short_name', 'users_ex.region as region')
            ->leftjoin('contacts',function($join) {
                $join->on('contacts.user_id', '=', 'users.id')
                ->where('contacts.type', '=', 'phone');
            })
            ->leftjoin('roles',    'roles.name',        '=', 'users.role'   )
            ->leftjoin('users_ex', 'users_ex.user_id',  '=', 'users.id'     )
            ->get()
            ->groupBy('user_id');
    }

    public function userImportData($userData)
    {
        $file = $userData->file('csv');
        $path = $userData->file('csv')->getRealPath();
        $data = array_map('str_getcsv',file($path));
        $csv_data = array_slice($data,1);
        $insUsers = [];
        $usersExData = [];
        
        $filename = $path;
        $csvArray = $this->ImportCSV2Array($filename);

        if(count(array_filter($csvArray)) != 0){
            DB::transaction(function() use($csvArray){
                foreach($csvArray as $key => $userValues){
                    if(count(array_filter($userValues)) != 0){

                        $userRole = 'super';

                        // Use short_name as ROLE to avoid mispellings
                        if(isset($userValues["ROLE"]) && !empty($userValues["ROLE"])){
                            $roles          = DB::table('roles')->where('short_name','=',strtolower($userValues["ROLE"]))->first();
                            $userRole       = (isset($roles->name) && !empty($roles->name)) ? $roles->name : 'super';
                        }                         

                        $insUsers = [
                            'realname'  => (isset($userValues["USER NAME"]) && !empty($userValues["USER NAME"])) ? $userValues["USER NAME"] : '',
                            'email'     => (isset($userValues["EMAIL"]) && !empty($userValues["EMAIL"])) ? $userValues["EMAIL"] : '',
                            'password'  => (isset($userValues["PASSWORD"]) && !empty($userValues["PASSWORD"])) ? Hash::make($userValues["PASSWORD"]) : '',
                            'role'      => $userRole,
                        ];

                        if((!isset($userValues["PASSWORD"]))) { unset($insUsers['password']);   }

                        // Check for user through email
                        try{
                            $email      = $insUsers['email'];
                            $checkUser  = DB::table('users')->where('email', $email)->first();
                            if($checkUser) {
                                DB::table('users')->where('email', $email)->update($insUsers);
                                $userInsert = $checkUser->id;     
                            } else {
                                $userInsert = DB::table('users')->insertGetId($insUsers);
                            }
                        } 
                        catch(QueryException $e){ continue;   }

                        $contactData=[];
                        if(isset($userValues["TELEPHONE"]) && !empty($userValues["TELEPHONE"])){
                            $contactData = [
                                'user_id'   => $userInsert,
                                'type'      => 'phone',
                                'contact'   => $userValues["TELEPHONE"],
                            ];
                        }

                        if(isset($userValues["TELEPHONE2"]) && !empty($userValues["TELEPHONE2"])){
                            $contactData = [
                                'user_id'   => $userInsert,
                                'type'      => 'phone',
                                'contact'   => $userValues["TELEPHONE2"],
                            ];
                        }

                        if($contactData && count($contactData) > 0){
                            $user_id        = $contactData['user_id'];
                            $checkUserId    = DB::table('contacts')->where('user_id', $user_id)->first();
                            
                            if($checkUserId){ DB::table('contacts')->where('user_id', $user_id)->update($contactData);      }
                            else            { DB::table('contacts')->insert($contactData);                                  }
                        }
                        
                        $usersExData = [
                            'user_id'       => $userInsert,
                            'institution'   => (isset($userValues["INSTITUTION"])   && !empty($userValues["INSTITUTION"]))  ? $userValues["INSTITUTION"]    : '',
                            'province'        => (isset($userValues["REGION"])        && !empty($userValues["REGION"]))       ? $userValues["REGION"]         : '',
                            'district '          => (isset($userValues["ZONE"])          && !empty($userValues["ZONE"]))         ? $userValues["ZONE"]           : '',
                            'ward'        => (isset($userValues["WOREDA"])        && !empty($userValues["WOREDA"]))       ? $userValues["WOREDA"]         : '',
                        ];

                        $userExUserId       = $usersExData['user_id'];
                        $checkUserEx        = DB::table('users_ex')->where('user_id', $userExUserId)->first();
                        
                        if($checkUserEx)    { DB::table('users_ex')->where('user_id', $userExUserId)->update($usersExData); }
                        else                { DB::table('users_ex')->insert($usersExData);                                  }
                    }   
                }
            });
        }
    }
    
    /**
     * Import from CSV to array
     * Use , as a separator
     */
    public function ImportCSV2Array($filename)
    {
        $row = 0;
        $col = 0;
    
        $handle = @fopen($filename, "r");
        if ($handle) {
            while (($row = fgetcsv($handle, 4096)) !== false) {
                if (empty($fields))         {   $fields = $row; continue;               }
                foreach ($row as $k=>$value){   $results[$col][$fields[$k]] = $value;   }
                $col++;
                unset($row);
            }
            if (!feof($handle)) {   echo "Error: unexpected fgets() failn"; }
            fclose($handle);
        }
    
        return $results;
    }
}
