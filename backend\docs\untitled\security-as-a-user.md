---
description: Basic tips for every tech user‌
---

# Security as a user

## Security position overview

As an everyday user of technology we would summarise your main concerns as these:

* **Understanding your actions and their consequences as you work with your devices**. The main intention here is to avoid being tricked into revealing valuable information to undesirable third parties, which is what happens with phishing attacks. Phishing is one of the most usual techniques deployed against specific targets.
* **Keeping your devices and applications defences strong**. Sometimes it's not you, sometimes the equipment and software that you use was poorly designed and there were gaps left for attackers to find their way in. However, equipment and software can be made better and more reliable, and that is something you can do.  
* **Containing the impact of any successful breach**. Unfortunately, in some occasions, the bad guys win. However, there are things you can do to limit how a breach affects you and keeping the bad guys from winning even more.

All the advices and techniques shown here will revolve around in some way around these three aspects.

## Keep a critical attitude

### Don't believe everything you read \(or see, or hear!\)

Producing and exchanging information has become so easy and affordable with digital technologies and the Internet. The downside of that is that it has also become relatively issue to produce false and misleading information. The scope of attacks that take advantage of this has grown staggeringly:

* Grifters may send you an e-mail or SMS, appearing as coming from your bank, inviting you to do something to reveal your personal data to them.
* You may receive a call with someone speaking _with exactly the same voice_ as your dear friend or co-worker, asking you to do something you wouldn't do for just anyone else. \(These are known as audio deep-fakes.\)
* You may be directed into watching a video of a public figure, acquaintance or relative, doing something or stating facts that alter your perceptions, expectations, opinions and ultimately your behaviour. \(These are video deep-fakes.\)

As you can see, there are very few limits left, in terms of how fake digital information may be presented as truthful. Therefore, don't implicitly trust any information you receive, and build your own critical procedures to come to a conclusion of how much you can believe in what you have received.

### Qualifying source authenticity

Almost all attempts to peddle misleading information make an effort to present themselves as coming from a trusted or genuine source.

When clicking links from e-mails or other sources, that direct you to log in or share information about a service you usually make use of, **make sure to always check the address bar in your browser**.

1. Check the domain name \(the part before the first "/" character\). Your opponent may be tricking to share the personal information you keep in secure-bank.com by directing you to a fake website named similarly, i.e.: secure-banking.com .
2. Check the authenticity and security of the connection. Never send data through a web browser tab that is not showing anything other than a glorious fully green padlock next to the website address.

If in doubt, contact back directly the apparent source of the information \(i.e. call your bank phone support, reach out to your boss, friend or relative\). Or, in more general disinformation propaganda scenarios, run your own research. If the information received still seems true after running 2 or 3 of your own checks, then you may most probably rely on it. 

## A few other quick pointers

* Keep cold copies of your most important data. This includes data you may have in cloud storage. You may keep these copies as off-site backups in a specialised provider, or inside a handy little pen-drive or portable flash storage device. It's important that it should be cold: only connected for backing up and recovery purposes.
* Use two-factor authentication \(2FA\) where available. **Don't forget to keep a safe copy of your recovery codes.** Phones get lost and broken.
* Use different passwords for different accounts, and use a password manager to store them.
* Use as much encryption as is possible, prefer applications and services that offer strong encryption.
* Update your device and applications regularly. If you have a device you are not able to update \(i.e. an Android phone that is out of support by the vendor\), double up your guard and limit your actions and the sensitivity of the data that you store on that device. 



