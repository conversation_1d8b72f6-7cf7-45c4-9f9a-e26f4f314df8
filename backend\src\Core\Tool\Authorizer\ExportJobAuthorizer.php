<?php

/**
 * Us<PERSON>hidi Export Job Authorizer
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2018 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Tool\Authorizer;

use <PERSON><PERSON>hidi\Core\Entity;
use Ushahidi\Core\Tool\Authorizer;
use Us<PERSON>hidi\Core\Traits\AdminAccess;
use Ushahidi\Core\Traits\OwnerAccess;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\Core\Entity\Permission;
use Ushahidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Traits\PrivateDeployment;
use Ushahidi\Core\Tool\Permissions\AclTrait;

class ExportJobAuthorizer implements Authorizer
{
    // The access checks are run under the context of a specific user
    use UserContext;

    // To check whether the user has admin access
    use AdminAccess;

    // To check whether user owns the webhook
    use OwnerAccess;

    // It uses `PrivAccess` to provide the `getAllowedPrivs` method.
    use PrivAccess;

    // It uses `PrivateDeployment` to check whether a deployment is private
    use PrivateDeployment;

    // Check that the user has the necessary permissions
    // if roles are available for this deployment.
    use AclTrait;


    /* Authorizer */
    public function isAllowed(Entity $entity, $privilege)
    {

        // These checks are run within the user context.
        $user = $this->getUser();

        // Only logged in users have access if the deployment is private
        if (!$this->canAccessDeployment($user)) {
            return false;
        }

        // First check whether there is a role with the right permissions
        // $this->acl->hasPermission($user, Permission::DATA_IMPORT_EXPORT) or
        // $this->acl->hasPermission($user, Permission::LEGACY_DATA_IMPORT)
        if ($this->acl->hasPermission($user, [Permission::ExportIncidentListConflictividadElectoral,Permission::ExportIncidentListOperacionesElectorales])) {
            return true;
        }

        // First check whether there is a role with the right permissions
        // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/08/2020.

        // $this->acl->hasPermission($user, Permission::CREATE_POST)
        // && $this->acl->hasPermission($user, Permission::EDIT_POSTS)
        // && $this->acl->hasPermission($user, Permission::VIEW_POSTS)
        // && $this->acl->hasPermission($user, Permission::DEL_POSTS
        if ($this->acl->hasPermission($user, [Permission::CreateANewreportConflictividadElectoral,Permission::CreateANewReportOperacionesElectorales])
        ) {
            return true;
        }
        // if ($this->acl->hasPermission($user, Permission::MANAGE_POSTS)) {
        //     return true;
        // }

        // Admin is allowed access to everything
        if ($this->isUserAdmin($user)) {
            return true;
        }

        // If no other access checks succeed, we default to denying access
        return false;
    }
}
