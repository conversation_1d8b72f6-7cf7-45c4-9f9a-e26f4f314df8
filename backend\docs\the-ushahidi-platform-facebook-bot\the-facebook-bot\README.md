# The Facebook bot

The Ushahidi Facebook chatbot is a microservice built for collecting answers from facebook messenger and post them on an Ushahidi deployment. It was built within the research project Comrades and was used as a pilot in the Uchaguzi-project during the Kenyan elections 2017. The bot is open source and you can install, customize and use it yourself in your deployment. The following sections gives an introduction to how to install it for development, how to deploy it and how to customize it for your deployment.

The code for the bot lives in this repo: [https://github.com/ushahidi/platform-facebook-bot](https://github.com/ushahidi/platform-facebook-bot)

[Also, look at this nice video <PERSON><PERSON>rades did about the bot](https://youtu.be/KRugR_7wGOM).

