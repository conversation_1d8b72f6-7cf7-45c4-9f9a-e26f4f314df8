<?php

use Phinx\Migration\AbstractMigration;

class UpdateIncidentReportAttributes extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {

        // /home/<USER>/Projects/UNDP/ireport-malawi-prod/backend/migrations/malawi/20210505201000_update_initial_form_attribute_incident.php


        $this->execute("DELETE from form_attributes WHERE form_stage_id = 1");


        $connection = $this->getAdapter()->getConnection();
        $adapter = $this->getAdapter();

        $form_stage_id = 1;

        $type_of_incident = '["Security", "Health and environment", "Gender", "Governance and human rights violations", "Campaign/Elections", "Media and Information"]';
        $affected_groups = '["Women", "Youth", "Persons with Albinism", "Persons with Disabilities", "Elderly"]';
/*
	$connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('IR_TITLE', 'Title of incident', 'text', 'title', 1, 1, NULL,  '[]', :form_stage_id ),
                ( 'IR_DESCRIPTION', 'Description', 'text', 'description', 1, 2, NULL, '[]', :form_stage_id ),
                ( 'IR_SECURITY', 'Security', 'tags', 'tags', 1, 3, '[]', '[]', :form_stage_id ),
                ( 'IR_HEALTH_AND_ENVIRONMENT', 'Health and environment', 'tags', 'tags', 1, 4, '[]', '[]', :form_stage_id ),
                ( 'IR_GENDER', 'Gender', 'tags', 'tags', 1, 5, '[]', '[]', :form_stage_id ),
                ( 'IR_GOVERNANCE_AND_HUMAN_RIGHTS_VIOLATIONS', 'Governance and human rights violations', 'tags', 'tags', 1, 3, '[]', '[]', :form_stage_id ),
                ( 'IR_CAMPAIGN_ELECTIONS', 'Campaign/Elections', 'tags', 'tags', 1, 6, '[]', '[]', :form_stage_id ),
                ( 'IR_MEDIA_AND_INFORMATION', 'Media and Information', 'tags', 'tags', 1, 7, '[]', '[]', :form_stage_id ),
                ( 'IR_AFFECTED_GROUPS', 'Affected Groups', 'select', 'varchar', 1, 8, '" . $affected_groups . "', '[]', :form_stage_id ),
                ( 'IR_DATE_TIME', 'Date and time', 'datetime', 'datetime', 1, 9, '[]', '[]', :form_stage_id ),
                ( 'IR_LOCATION', 'Location', 'location', 'point', 1, 10, '[]', '[]', :form_stage_id );
        "
    )->execute([ ':form_stage_id' => 1 ]); 
 */           

         $incident_type_config1 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "IR_INCIDENT_TYPE", "value" => "Security"]]);
         $incident_type_config2 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "IR_INCIDENT_TYPE", "value" => "Health and environment"]]);
         $incident_type_config3 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "IR_INCIDENT_TYPE", "value" => "Gender"]]);
         $incident_type_config4 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "IR_INCIDENT_TYPE", "value" => "Governance and human rights violations"]]);
         $incident_type_config5 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "IR_INCIDENT_TYPE", "value" => "Campaign/Elections"]]);
         $incident_type_config6 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "IR_INCIDENT_TYPE", "value" => "Media and Information"]]);

	$connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('IR_TITLE', 'Title of incident', 'text', 'title', 1, 1, NULL,  '[]', :form_stage_id ),
                ( 'IR_DESCRIPTION', 'Description', 'text', 'description', 1, 2, NULL, '[]', :form_stage_id ),
                ( 'IR_INCIDENT_TYPE', 'Type of Incident', 'select', 'varchar', 1, 3, '" . $type_of_incident . "', '[]', :form_stage_id ),
                ( 'IR_SECURITY', 'Security', 'tags', 'tags', 0, 4, '[]', '[" . $incident_type_config1 . "]', :form_stage_id ),
                ( 'IR_HEALTH_AND_ENVIRONMENT', 'Health and environment', 'tags', 'tags', 0, 4, '[]', '[" . $incident_type_config2 . "]', :form_stage_id ),
                ( 'IR_GENDER', 'Gender', 'tags', 'tags', 0, 4, '[]', '[" . $incident_type_config3 . "]', :form_stage_id ),
                ( 'IR_GOVERNANCE_AND_HUMAN_RIGHTS_VIOLATIONS', 'Governance and human rights violations', 'tags', 'tags', 0, 4, '[]', '[" . $incident_type_config4 . "]', :form_stage_id ),
                ( 'IR_CAMPAIGN_ELECTIONS', 'Campaign/Elections', 'tags', 'tags', 0, 4, '[]', '[" . $incident_type_config5 . "]', :form_stage_id ),
                ( 'IR_MEDIA_AND_INFORMATION', 'Media and Information', 'tags', 'tags', 0, 4, '[]', '[" . $incident_type_config6 . "]', :form_stage_id ),
                ( 'IR_AFFECTED_GROUPS', 'Affected Groups', 'select', 'varchar', 0, 5, '" . $affected_groups . "', '[]', :form_stage_id ),
                ( 'IR_DATE_TIME', 'Date and time', 'datetime', 'datetime', 1, 6, '[]', '[]', :form_stage_id ),
                ( 'IR_LOCATION', 'Location', 'location', 'point', 0, 7, '[]', '[]', :form_stage_id ),
                ( 'IR_UPLOAD_1', 'Upload Document 1', 'upload', 'media', 0, 8, '[]', '[]', :form_stage_id ) ;
        "
    )->execute([ ':form_stage_id' => 1 ]); 


	$connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ( 'IR_UPLOAD_2', 'Upload Document 2', 'upload', 'media', 0, 8, '[]', '[]', :form_stage_id ) ,
                ( 'IR_UPLOAD_3', 'Upload Document 3', 'upload', 'media', 0, 8, '[]', '[]', :form_stage_id ) ,
                ( 'IR_UPLOAD_4', 'Upload Document 4', 'upload', 'media', 0, 8, '[]', '[]', :form_stage_id ) ,
                ( 'IR_UPLOAD_5', 'Upload Document 5', 'upload', 'media', 0, 8, '[]', '[]', :form_stage_id ) ;
        "
    )->execute([ ':form_stage_id' => 1 ]); 





    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // Noop - too risky to delete a post type
    }
}
