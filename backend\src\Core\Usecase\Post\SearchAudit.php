<?php

/**
 * Ushahidi Platform Post Read Use Case
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Usecase\Post;

use Ushahidi\Core\Usecase\SearchUsecase;

class SearchAudit extends SearchUsecase
{

  protected function getPagingFields()
  {
      return [
          'orderby' => 'created',
          'order'   => 'desc',
          'limit'   => null,
          'offset'  => 0
      ];
  }
  
  public function interact()
  {
    $entity = $this->getEntity();
    return $entity;
  }

  protected function getEntity()
  {
    // ... get the results of the search
    $search = $this->getSearch();
    $data['results'] = $this->repo->getAuditByParams($search);
    $data['total_count'] = $this->repo->getSearchTotal();
    return $this->formatter->__invoke($data);
  }
}
