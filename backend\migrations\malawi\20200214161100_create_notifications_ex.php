<?php

use Phinx\Migration\AbstractMigration;

class CreateNotificationsEx extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('notifications_ex')
      ->addColumn('post_id', 'integer')
      ->addColumn('user_id', 'integer')
      ->addColumn('event_type', 'string')
      ->addColumn('created', 'integer', ['default' => 0])
      ->addForeignKey('post_id', 'posts', 'id', [
        'delete' => 'CASCADE',
        'update' => 'RESTRICT',
      ])
      ->addForeignKey('user_id', 'users', 'id', [
        'delete' => 'CASCADE',
        'update' => 'RESTRICT',
      ])
      ->create();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
