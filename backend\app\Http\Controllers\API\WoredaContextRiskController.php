<?php
namespace Us<PERSON>hidi\App\Http\Controllers\API;

use Ushahidi\App\Http\Controllers\RESTController;
use Illuminate\Http\Request;
use Ushahidi\Core\Usecase;

/**
 * Ushahidi API Index Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class WoredaContextRiskController extends RESTController
{

  protected function getResource() {
    return 'woreda_context_risk';
  }

   
  public function index(Request $request)
  {
    $params = $this->getRouteParams($request);
    $this->usecase = $this->usecaseFactory
      ->get($this->getResource(), 'readdata')
      ->setFilters($request->query());
      
    return $this->prepResponse($this->executeUsecase($request), $request);
  }
    
}
