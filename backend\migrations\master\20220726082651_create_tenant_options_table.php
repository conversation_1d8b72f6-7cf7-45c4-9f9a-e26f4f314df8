<?php

use Phinx\Migration\AbstractMigration;

class CreateTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
        $this->execute("DROP TABLE IF EXISTS tenant_options");
        $this->table('tenant_options')
            ->addColumn('tenant_id', 'integer')
            ->addColumn('tenant_key', 'string')
            ->addColumn('tenant_value', 'text')
            ->create();
        /// Images
        $zimbabwe_images = array(
                'images' => [
                    'logo' => 'logo.png',
                    'left_side_logo' => 'left_side_logo.png',
                    'right_side_logo' => 'right_side_logo.png',
                    'main_logo_image' => 'main_logo_image.png'
                ],
                );
        $zimbabwe_images = json_encode($zimbabwe_images);

        $liberia_images = array(
            'images' => [
                'logo' => 'logo.png',
                'left_side_logo' => 'left_side_logo.png',
                'right_side_logo' => 'right_side_logo.png',
                'main_logo_image' => 'main_logo_image.png'
            ],
            );
        $liberia_images = json_encode($liberia_images);

        $madagascar_images = array(
            'images' => [
                'logo' => 'logo.png',
                'left_side_logo' => 'left_side_logo.png',
                'right_side_logo' => 'right_side_logo.png',
                'main_logo_image' => 'main_logo_image.png'
            ],
            );
        $madagascar_images = json_encode($madagascar_images);

        $peru_images = array(
            'images' => [
                'logo' => 'logo.png',
                'left_side_logo' => 'left_side_logo.png',
                'right_side_logo' => 'right_side_logo.png',
                'main_logo_image' => 'main_logo_image.png'
            ],
            );
        $peru_images = json_encode($peru_images);

      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'images', '$zimbabwe_images')");
      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'images', '$liberia_images')");
//        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'images', '$madagascar_images')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'images', '$peru_images')");


        // Private Pages
        $zimbabwe_private_pages = array(
            'pages' => ['contact-us', 'get-alerts'],
            );
        $zimbabwe_private_pages = json_encode($zimbabwe_private_pages);

        $liberia_private_pages = array(
            'pages' => [],
            );
        $liberia_private_pages = json_encode($liberia_private_pages);

        $madagascar_private_pages = array(
            'pages' => [],
            );
        $madagascar_private_pages = json_encode($madagascar_private_pages);

        $peru_private_pages = array(
            'pages' => [],
            );
        $peru_private_pages = json_encode($peru_private_pages);
       
      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'private_pages', '$zimbabwe_private_pages')");
      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'private_pages', '$liberia_private_pages')");
//        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'private_pages', '$madagascar_private_pages')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'private_pages', '$peru_private_pages')");


        //Colors
        $zimbabwe_colors = array(
            'colors' => [
                'primary_color' => '#087407',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#087407',
                'text_color' => '#bbbbbb',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#006600',
                'bg_color' => '#087407',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#006600',
                'btn_dark_color' => '#006600',
                'btn_dark_hover_bg_color' => '#FFE500',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'

            ],
            );
        $zimbabwe_colors = json_encode($zimbabwe_colors);

        $liberia_colors = array(
            'colors' => [
                'primary_color' => '#087407',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#087407',
                'text_color' => '#bbbbbb',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#006600',
                'bg_color' => '#087407',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#006600',
                'btn_dark_color' => '#006600',
                'btn_dark_hover_bg_color' => '#FFE500',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'
            ],
            );
        $liberia_colors = json_encode($liberia_colors);

        $madagascar_colors = array(
            'colors' => [
                'primary_color' => '#087407',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#087407',
                'text_color' => '#bbbbbb',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#006600',
                'bg_color' => '#087407',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#006600',
                'btn_dark_color' => '#006600',
                'btn_dark_hover_bg_color' => '#FFE500',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'
            ],
            );
        $liberia_colors = json_encode($madagascar_colors);

        $peru_colors = array(
            'colors' => [
                'primary_color' => '#087407',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#087407',
                'text_color' => '#bbbbbb',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#006600',
                'bg_color' => '#087407',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#006600',
                'btn_dark_color' => '#006600',
                'btn_dark_hover_bg_color' => '#FFE500',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'
            ],
            );
        $peru_colors = json_encode($peru_colors);

        $malawi_colors = array(
            'colors' => [
                'primary_color' => '#7B14BB',
                'primary_color_transparent' => '#fff',
                'secondary_color' => '#FFD700',
                'text_color' => '#7B14BB',
                'text_header_color' => '#23191b',
                'text_hover_color' => '#000000',
                'accent_color' => '#000000',
                'bg_color' => '#7B14BB',
                'dark_bg_color' => '#EF9F50',
                'active_card_color' => '#92D46A',
                'dark_light_bg_color' => '#FCFFFA',
                'text_dark_bg_color' => '#fff',
                'link_dark_bg_color' => '#15d04e',
                'link_dark_bg_hover_color' => '#000000',
                'btn_dark_color' => '#7B14BB',
                'btn_dark_hover_bg_color' => '#FFE500',
                'btn_text_color' => '#fff',
                'btn_dark_hover_color' => '#23191b',
                'status_verified_color' => '#4ceb0d',
                'status_warning_color' => '#ffc107',
                'status_unverified_bg_color' => '#fff3cd',
                'status_unverified_color' => '#856404',
                'status_evaluated_color' => '#4282c2',
                'status_responded_color' => '#007bff',
                'region_border_color' => '#e45d5dd8'
            ],
            );
        $malawi_colors = json_encode($malawi_colors);
        
    //    $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'colors', '$zimbabwe_colors')");
    //    $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'colors', '$liberia_colors')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'colors', '$malawi_colors')");
//        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'colors', '$peru_colors')");


        $malawi_about_us_section = array(
            'about_us' => [
                'image' => 'about_us.png',
                'what_we_do' => [
                    'title' => 'What we do',
                    'description' => 'What we do description'
                ],
                'how_we_do' => [
                    'title' => 'How we do',
                    'description' => 'How we do description'
                ],
                'who_we_are' => [
                    'title' => 'Who we are',
                    'people' => [
                       [
                        'image' => 'people1.png',
                        'name' => 'Name',
                        'occupation' => 'Occupation'
                       ],
                       [
                        'image' => 'people2.png',
                        'name' => 'Name',
                        'occupation' => 'Occupation'
                       ]
                    ]
                ],
             ],
            );
        $malawi_about_us_section = json_encode($malawi_about_us_section);

        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'about_us', '$malawi_about_us_section')");

        // Liberia Got Query Section
        $liberia_qot_query_section = array(
            'got_query' => ['title' => 'title', 'description' => 'description'],
            );
        $liberia_qot_query_section = json_encode($liberia_qot_query_section);

        $madagascar_qot_query_section = array(
            'got_query' => ['title' => 'title', 'description' => 'description'],
            );
        $madagascar_qot_query_section = json_encode($madagascar_qot_query_section);

//        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'got_query', '$liberia_qot_query_section')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'got_query', '$madagascar_qot_query_section')");
    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
