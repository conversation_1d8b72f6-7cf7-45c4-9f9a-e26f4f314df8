<?php

use Phinx\Migration\AbstractMigration;

class AddNewPermissions extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {
        $permissions = [
            'Access Management',
            'User Management',
            'View Reports - Map - Conflictividad Electoral',
            'View Reports - Map - Operaciones Electorales',
            'View Reports - List - Conflictividad Electoral',
            'View Reports - List - Operaciones Electorales',
            'Comment Report - Conflictividad Electoral',
            'Comment Report - Operaciones Electorales',
            'Edit own incident report content - Conflictividad Electoral',
            'Edit own incident report content - Operaciones Electorales',
            'Edit and manage reports - Conflictividad Electoral',
            'Edit and manage reports - Operaciones Electorales',
            'Access and edit incident reports form - Names of reports',
            'Access and edit incident indicators',
            'Delete reports - Conflictividad Electoral',
            'Delete reports - Operaciones Electorales',
            'View incident reporter identity - Conflictividad Electoral',
            'View incident reporter identity - Operaciones Electorales',
            'Export incident list - Conflictividad Electoral',
            'Export incident list - Operaciones Electorales',
            'Access Analysis Dashboard - Conflictividad Electoral',
            'Access Analysis Dashboard - Operaciones Electorales',
            'Create a new report - Conflictividad Electoral',
            'Create a new report - Operaciones Electorales'

        ];


        $permission_string = '';
        $role_permission_string = '';

        foreach ($permissions as $value) {
		$permission_string .= "('" . $value . "', '" . $value . "'),";
		$role_permission_string .= "('super', '" . $value . "'), ('admin', '" . $value . "'),";
        }

        $permission_string =  rtrim($permission_string, ",");
        $role_permission_string =  rtrim($role_permission_string, ",");

                $this->execute("
                    start transaction;

                    set FOREIGN_KEY_CHECKS = 0;
                    
                    truncate table  permissions; 
                    truncate table  roles_permissions; 
                    
                    INSERT INTO permissions (name, description) values " . $permission_string . ";

                    INSERT INTO roles_permissions (role, permission) values " . $role_permission_string . ";
                    
                    commit;");

            

    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        $this->dropTable('additional_post_comments');
    }
}
