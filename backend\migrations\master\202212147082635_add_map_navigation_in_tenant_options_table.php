<?php

use Phinx\Migration\AbstractMigration;

class AddMapNavigationInTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
        $liberia_map_navigation = array(
            'navData' => [
                [
                    'label' => '2023 -',
                    'route' => '/dashboard/map',
                    'isActive' => false,
                    'description' => 'Showing From January 01, 2023',
                    'slug' => 'latest'
                ]
            ],
            'viewData' => [
                [
                    'label' => 'Map View',
                    'activeForRoutes' => ['/dashboard/map', '/dashboard/map/lern'],
                    'route' => '/dashboard/map/lern',
                    'isActive' => true
                ],
                [
                    'label' => 'List View',
                    'activeForRoutes' => ['/dashboard/posts/lern'],
                    'route' => '/dashboard/posts/lern',
                    'isActive' => false,
                ]
            ]
        );

        $liberia_map_navigation_json = json_encode($liberia_map_navigation);

        $madagascar_map_navigation = array(
            'navData' => [
                [
                    'label' => '2023 -',
                    'route' => '/dashboard/map',
                    'isActive' => false,
                    'description' => 'Showing From January 01, 2023',
                    'slug' => 'latest'
                ]
            ],
            'viewData' => [
                [
                    'label' => 'Vue de la carte',
                    'activeForRoutes' => ['/dashboard/map', '/dashboard/map/lern'],
                    'route' => '/dashboard/map/lern',
                    'isActive' => true
                ],
                [
                    'label' => 'Vue de la liste',
                    'activeForRoutes' => ['/dashboard/posts/lern'],
                    'route' => '/dashboard/posts/lern',
                    'isActive' => false,
                ]
            ]
        );

        $madagascar_map_navigation_json = json_encode($madagascar_map_navigation);

        $peru_map_navigation = array(
            'navData' => [
                [
                    'label' => 'Informe de Operaciones Electorales',
                    'route' => '/dashboard/map/mace',
                    'isActive' => true,
                    'description' => 'Informe de Operaciones Electorales',
                    'slug' => 'past_records'
                ],
                [
                    'label' => 'Informe de Conflictividad Electoral',
                    'route' => '/dashboard/map',
                    'isActive' => false,
                    'description' => 'Informe de Conflictividad Electoral',
                    'slug' => 'latest'
                ]
            ],
            'viewData' => [
                [
                    'label' => 'Formato de mapa MACE',
                    'activeForRoutes' => ['/dashboard/map', '/dashboard/map/mace'],
                    'route' => '/dashboard/map/mace',
                    'isActive' => true
                ],
                [
                    'label' => 'Formato de lista MACE',
                    'activeForRoutes' => ['/dashboard/posts/mace'],
                    'route' => '/dashboard/posts/mace',
                    'isActive' => false,
                ]
            ]
        );

        $peru_map_navigation_json = json_encode($peru_map_navigation);

        $malawi_map_navigation = array(
            "navData" => [
                [
                    "label" => "E-day Observer Checklist ",
                    "route" => "/dashboard/map/mace",
                    "isActive" => true,
                    "description" => "Elections Day Observer Checklist",
                    "slug" => "latest"
                ],
                [
                    "label" => "Incident Report",
                    "route" => "/dashboard/map",
                    "isActive" => false,
                    "description" => "EWER Incidents Report (Early Warning and Early Response System)",
                    "slug" => "past_records"
                ]
            ],
            "viewData" => [
                [
                    "label" => "Map Format",
                    "activeForRoutes" => [
                        "/dashboard/map",
                        "/dashboard/map/mace"
                    ],
                    "route" => "/dashboard/map",
                    "isActive" => true
                ],
                [
                    "label" => "List Format",
                    "activeForRoutes" => [
                        "/dashboard/posts/mace"
                    ],
                    "route" => "/dashboard/posts/mace",
                    "isActive" => false
                ]
            ]
        );

        $malawi_map_navigation_json = json_encode($malawi_map_navigation);

        //   $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'mapNavigation', '$liberia_map_navigation_json')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'mapNavigation', '$madagascar_map_navigation_json')");
        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'mapNavigation', '$malawi_map_navigation_json')");


    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
