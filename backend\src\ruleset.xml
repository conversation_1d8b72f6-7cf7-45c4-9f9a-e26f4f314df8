<?xml version="1.0"?>
<ruleset name="<PERSON><PERSON><PERSON><PERSON>">
    <description><PERSON><PERSON><PERSON><PERSON> uses PSR2 with tabs instead of spaces</description>

    <rule ref="PSR2" />

    <rule ref="Generic.PHP.CharacterBeforePHPOpeningTag" />
    <rule ref="Generic.Arrays.DisallowLongArraySyntax" />

    <exclude-pattern>vendor/*</exclude-pattern>
    <exclude-pattern>tests/spec/*</exclude-pattern>
    <exclude-pattern>migrations/*</exclude-pattern>
    <exclude-pattern>bin/*</exclude-pattern>
    <exclude-pattern>database/*</exclude-pattern>
    <exclude-pattern>resources/views/*</exclude-pattern>
</ruleset>
