<?php

namespace Ushahidi\App\Http\Controllers\API;

use <PERSON>hahidi\App\Http\Controllers\Controller;
use <PERSON>hahidi\App\Models\TenantOption;
use Ushahidi\App\Models\Tenant;
use Illuminate\Http\Request;
/**
 * Ushahidi API Index Controller
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application\Controllers
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

class TenantController extends Controller
{

    /**
     * Retrieve a basic information about the API
     *
     * GET /api
     *
     * @return void
     */
    public function index()
    {   
        $options = [];
        $options['id'] = app('tenant')->id;
        $options['name'] = app('tenant')->name;
        $options['unique_name'] = app('tenant')->unique_name;
        $options['backend_url'] = app('tenant')->backend_url;
        $options['db'] = app('tenant')->db;
        $options['partners'] = app('tenant')->partners;
        $tenant_data = TenantOption::where('tenant_id', app('tenant')->id)->get();
        if(!empty($tenant_data)){
            foreach($tenant_data as $data){
                $options[$data['tenant_key']] =  is_null(json_decode($data['tenant_value'])) ? $data['tenant_value'] : json_decode($data['tenant_value']) ;
            }
        }
        return $options;
    }

    public function getTenant(Request $request){
        $this->validate($request,[
            'frontend_url' => 'required|string|max:255'
        ]);
        $tenant = Tenant::where('frontend_url', $request->frontend_url)->first();
        if(!is_null($tenant)){
            $options = [];
            $options['id'] = $tenant->id;
            $options['name'] = $tenant->name;
            $options['unique_name'] = $tenant->unique_name;
            $options['backend_url'] = $tenant->backend_url;
            $options['db'] = $tenant->db;
            $options['partners'] = $tenant->partners;
            $tenant_data = TenantOption::where('tenant_id', $tenant->id)->get();
            if(!empty($tenant_data)){
                foreach($tenant_data as $data){
                    $options[$data['tenant_key']] =  is_null(json_decode($data['tenant_value'])) ? $data['tenant_value'] : json_decode($data['tenant_value']) ;
                }
            }

            return $options;
        }
        return response()->json(["success" => false, 'message' => 'tenant not found'],404,['Content-type'=> 'application/json; charset=utf-8'],JSON_UNESCAPED_UNICODE);

    }
}
