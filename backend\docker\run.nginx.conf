server {
  listen *:{{ default .Env.HTTP_PORT 8080 }} default_server;

  root {{ default .Env.VHOST_ROOT "/var/www/httpdocs" }};
  index {{ default .Env.VHOST_INDEX "index.php" }};

  # Force encoding.
  charset utf-8;
  override_charset on;

  client_max_body_size 20m;

  location / {
    if ($request_method = 'OPTIONS') {
      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Methods' 'DELETE, GET, OPTIONS, POST, PUT';
      add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,authorization,*';
      add_header 'Access-Control-Max-Age' 1728000;
      add_header 'Content-Type' 'text/plain; charset=utf-8';
      add_header 'Content-Length' 0;
      return 204;
    }
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'DELETE, GET, OPTIONS, POST, PUT';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,authorization,*';
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
    client_max_body_size 20m;

    # First attempt to serve request as file, then
    # as directory, then fall back to displaying a 404.
    try_files $uri $uri/ /{{ default .Env.VHOST_INDEX "index.php" }}$uri?$args;
  }

  # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
  #
  location ^~ /{{ default .Env.VHOST_INDEX "index.php" }} {
      fastcgi_split_path_info ^(.+\.php)(/.+)$;
      # NOTE: You should have "cgi.fix_pathinfo = 0;" in php.ini

      fastcgi_pass 127.0.0.1:9000;
      fastcgi_index {{ default .Env.VHOST_INDEX "index.php" }};
      fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
      fastcgi_read_timeout {{ .Env.PHP_EXEC_TIME_LIMIT }};
      include fastcgi_params;

      client_max_body_size 20m;
      break;
  }
}
