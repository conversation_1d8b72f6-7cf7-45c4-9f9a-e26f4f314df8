<?php

use Phinx\Migration\AbstractMigration;

class AddDeploymentNameInTenantOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {
       
        $zimbabwe_deployment_name  = 'iReport Zimbabwe';
        $liberia_deployment_name  = 'iReport Madagascar';
        $madagascar_deployment_name  = 'iReport Madagascar';
        $peru_deployment_name  = 'iReport Peru';
        $malawi_deployment_name  = '<PERSON><PERSON>hu';

        //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'deploymentName', '$zimbabwe_deployment_name')");
      //  $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'deploymentName', '$liberia_deployment_name')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'deploymentName', '$liberia_deployment_name')");
        // $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('2', 'deploymentName', '$peru_deployment_name')");

        $this->execute("INSERT INTO tenant_options (tenant_id,tenant_key,tenant_value) VALUES ('1', 'deploymentName', '$malawi_deployment_name')");

    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
