---
description: Guidance on how to design well for <PERSON><PERSON><PERSON><PERSON>.
---

# 'Best practice' design

## Best practice design

<PERSON><PERSON><PERSON><PERSON> encourages everyone who wants to contribute to their OSS products regardless of perceived skill level. As an OSS contributor, you should be able to receive feedback and guidance with your contribution in order for your work to be merged into the project regardless if it is code, documentation or design.

We do expect you to be ready to discuss, collaborate and support your design efforts with appropriate user insight and research or good sound design logic.

We expect consideration of several aspects when designing for <PERSON><PERSON><PERSON><PERSON>'s products:

**1 -** Understanding of the Ushahidi product by using and investigating the tool yourself as well as the varied use cases of the tool. You can learn more about varied use cases by visiting our website [grassroots section](https://www.ushahidi.com/donate) and [case studies like Quakemap](https://www.ushahidi.com/case-studies/quakemap). Familiarity with the tool will help you understand how to design with the users in front of your mind.  
Reviewing the [Pattern library](http://preview.ushahidi.com/platform-pattern-library/develop/index.html) is also very useful.

<PERSON><PERSON><PERSON><PERSON>'s products are used in high-stress scenarios. From natural disasters to trauma survivors and human rights activists. Always be aware of the vulnerability of these kinds of users and their safety.

**2 -** Make appropriate consideration of accessibility needs like colour impairments, sight-impairments, low connectivity/download speeds, older devices that might not run complex systems well, literacy levels, physical impairments etc. Learn more about designing inclusively with accessibility in mind at [A11y](https://a11yproject.com/).

**3 -** The user comes first and balancing a tech build is important. If the choice between a good user experience that works and is inclusive or what is 'easiset' to build technologically then is it best to not build or take longer to build than build something which at best, doesn't work well or at worst could potentially harm human life.

**4 -** When the above criteria have been met, design beautiful, engaging, visually pleasing work. Be mindful of design fashion and trends that will not age well.

