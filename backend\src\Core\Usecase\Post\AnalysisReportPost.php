<?php

namespace Us<PERSON>hidi\Core\Usecase\Post;

use <PERSON><PERSON><PERSON><PERSON>\Core\Usecase\SearchUsecase;

class AnalysisReportPost extends SearchUsecase
{

  // - VerifyParentLoaded for checking that the parent exists
  use VerifyParentLoaded;
  // Usecase
    public function interact()
    {
        // Fetch an empty entity...
        $entity = $this->getEntity();

       // ... and get the search filters for this entity
        $search = $this->getSearch();
        if ($this->getIdentifier('status')) {
            $search->setFilter('status', $this->getIdentifier('status'));
        }
        if ($this->getIdentifier('startDate')) {
            $search->setFilter('created_after', $this->getIdentifier('startDate'));
        }
        if ($this->getIdentifier('endDate')) {
            $search->setFilter('created_before', $this->getIdentifier('endDate'));
        }
        if ($this->getIdentifier('mgmt_lev')) {
            $search->setFilter('mgmt_lev', $this->getIdentifier('mgmt_lev'));
        }
        if ($this->getIdentifier('mgmt_lev_1')) {
            $search->setFilter('mgmt_lev_1', $this->getIdentifier('mgmt_lev_1'));
        }
        $search->setFilter('group_by', $this->getIdentifier('category'));
        if ($this->getIdentifier('category') === 'attribute') {
            $search->setFilter('group_by_attribute_key', $this->getIdentifier('key'));
        }        
        // ... get the total count for the search
        $results = $this->repo->getGroupedTotalsByFilters($search);
        // ... and return the formatted results.
        return $results;
    }
}
