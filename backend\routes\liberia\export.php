<?php

// Export Posts
$router->group([
    'middleware' => ['scope:export-posts']
], function () use ($router) {

    // Public access
    //$router->get('/export-posts', 'PostsCSVController@exportCSV');

    // Restricted access
    $router->group([
        'middleware' => ['auth:api']
    ], function () use ($router) {
        $router->get('/export-posts', 'PostsCSVController@exportCSV');
    });
});
