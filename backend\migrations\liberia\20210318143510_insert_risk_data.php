<?php
use Phinx\Migration\AbstractMigration;

class InsertRiskData extends AbstractMigration
{

  public function change()
  {
      $connection = $this->getAdapter()->getConnection();
      $adapter = $this->getAdapter();
      $this->execute(
          "INSERT INTO forms (name, description, type, created)
          VALUES ( 'Risk Report', 'risk', 'risk', UNIX_TIMESTAMP(NOW()) )"
      );

      $connection->prepare(
          "INSERT INTO form_stages (label, priority, required, form_id, type, show_when_published, task_is_internal_only)
          VALUES ( 'Structure', 0, 1, :form_id, 'risk', 1, 0 )"
      )->execute([ ':form_id' => $connection->lastInsertId() ]);

      $title = json_encode(['Policical','Security','Economy','Social','Environment']);
      $info = json_encode(['broadcast media','social media','word of mouth','witness','other','unknown']);
      $credi = json_encode(['High','Medium','Low','Unknown']);
      $like = json_encode(['Certain','Very likely','Likely','Possible','Unlikely','Very unlikely']);
      $impact = json_encode(['Extreme','Critical','Major','Minor','Negligible']);
      $stage = json_encode(['Voter registration','Campaign','Polling and counting','Results']);
      $attrInsert = $connection->prepare(
          "INSERT INTO form_attributes (" . $adapter->quoteColumnName('key') . ",
              label, input, type, required, priority, cardinality, options, form_stage_id)
          VALUES
              ( 'title_default', 'Title', 'text', 'title', 1, 1, 1, '[]', :form_stage_id )
              ,( 'description_default', 'Description', 'text', 'description', 1, 2, 1, '[]', :form_stage_id )
              ,( 'risk_default', 'Risk type', 'select', 'varchar', 0, 3, 1, '$title', :form_stage_id )
              ,('info_default', 'Source of information','select','varchar',0,5,1,'$info',:form_stage_id)
              ,('credibility_default', 'Credibility of the risk','select','varchar',0,6,1,'$credi',:form_stage_id)
              ,('likelihood_default', 'Likelihood of the risk','select','varchar',0,7,1,'$like',:form_stage_id)
              ,('impact_default', 'Impact of the risk','select','varchar',0,8,1,'$impact',:form_stage_id)
              ,('stage_default', 'Stage of electoral process risk applies to','select','varchar',0,9,1,'$stage',:form_stage_id)
          "
      )->execute([ ':form_stage_id' => $connection->lastInsertId() ]);
 
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
    // Delete  foreign keys to posts_tags table
        $this->table('woreda_context_risk')
            ->dropForeignKey('woreda_context_id')
            ->update()
            ;
  }
}
