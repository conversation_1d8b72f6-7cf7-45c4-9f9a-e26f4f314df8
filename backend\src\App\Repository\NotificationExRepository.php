<?php

// UJEVMS-62 - SCAL<PERSON> Luca, <EMAIL> - 14/02/2020.

namespace <PERSON>hahidi\App\Repository;

use Ohanzee\DB;
use <PERSON><PERSON>hidi\Core\Entity;
use <PERSON><PERSON><PERSON>di\Core\Entity\NotificationEx;
use <PERSON><PERSON><PERSON><PERSON>\Core\Entity\NotificationExRepository as NotificationExRepositoryContract;
use <PERSON><PERSON>hidi\Core\Entity\Permission;
use Ushahidi\Core\Entity\UserRepository;
use Ushahidi\App\Multisite\OhanzeeResolver;
use Ushahidi\Core\SearchData;
use Ushahidi\Core\Tool\Permissions\AclTrait;
use Ushahidi\Core\Traits\UserContext;
use Ushahidi\App\Http\Controllers\RESTController;

class NotificationExRepository extends OhanzeeRepository implements NotificationExRepositoryContract
{
  use UserContext;

  use AclTrait;

  protected $user_repo = [];

  public function __construct(
        OhanzeeResolver $resolver,
        UserRepository $user_repo
    ) {
      parent::__construct($resolver);
      $this->user_repo = $user_repo;
  }

  public function getEntity(array $data = null)
  {
    $data['post'] = [
      'id' => intval($data['post_id']),
      'url' => url(RESTController::url($this->getTable(), $data['id'])),
      'title' => $data['title']
    ];

    if (!empty($data['user_id'])) {
      $userInfo = $data['user_id'];
      if (isset($data['user_id'])) {
        $userInfo =  $this->user_repo->getById($data['user_id']);
      }
      $data += [
        'comment_user' => $userInfo->realname
      ];
    }
    unset($data['title']);
    return new NotificationEx($data);
  }

  protected function getTable()
  {
    return 'notifications_ex';
  }

  protected function setSearchConditions(SearchData $search)
  {
    $query = $this->search_query;

    $query->join('posts')
      ->on('posts.id', '=', 'notifications_ex.post_id')
      ->select('posts.title');

    $user = $this->getUser();
    $query->where('notifications_ex.user_id', '!=', $user->id);

    // UJEVMS-69 - Fulpagare Pramod, <EMAIL> - 07/08/2020.
    // Changed MANAGE_POST => VIEW_POSTS

    if (
      $this->acl->hasPermission($user, Permission::EDIT_POSTS)
      && !$this->acl->hasPermission($user, Permission::VIEW_POSTS)
    ) {
      $query->where('posts.user_id', '=', $user->id);
    }

    return $query;
  }

  public function getSearchFields()
  {
    return [
      'post_id',
      'user_id'
    ];
  }

  public function create(Entity $entity)
  {
    $record = array_filter($entity->asArray());
    $record['created'] = time();
    unset($record['post']);
    $id = $this->executeInsert($this->removeNullValues($record));
    return $id;
  }
}
