<?php

/**
 * Repository for Form Contacts
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\Core\Entity;

use <PERSON><PERSON>hidi\Core\Entity\Repository\EntityGet;
use Ushahidi\Core\Entity\Repository\EntityExists;

interface FormContactRepository extends
    EntityGet,
    EntityExists
{

    /**
     * @param  int $form_id
     * @return [Ushahidi\Core\Entity\FormContact, ...]
     */
    public function getByForm($form_id);

    /**
     * @param  int $contact_id
     * @param  int $form_id
     * @return [Ushahidi\Core\Entity\FormContact, ...]
     */
    public function existsInFormContact($contact_id, $form_id);

    /**
     * @param  [<PERSON><PERSON>hi<PERSON>\Core\Entity\FormContact, ...]  $entities
     * @return [Us<PERSON>hi<PERSON>\Core\Entity\FormContact, ...]
     */
    public function updateCollection(array $entities);
}
