<?php
use Phinx\Migration\AbstractMigration;

class UpdateRiskOptions extends AbstractMigration
{

  public function change()
  {
      $connection = $this->getAdapter()->getConnection();
      $adapter = $this->getAdapter();

     
      $like = json_encode(['Certain','Very likely','Likely','Possible','Unlikely','Very unlikely']);
      $impact = json_encode(['Extreme','Critical','Major','Minor','Negligible']);
      $stage = json_encode(['Campaign','Polling and counting','Results']);
      $attrInsert = $connection->prepare(
        "UPDATE form_attributes 
          SET options = '$like' WHERE form_attributes.key = 'likelihood_default' AND form_stage_id = :form_stage_id")->execute([ ':form_stage_id' => 2 ]);

      $attrInsert = $connection->prepare(
          "UPDATE form_attributes 
          SET options = '$impact' WHERE form_attributes.key = 'impact_default' AND form_stage_id = :form_stage_id")->execute([ ':form_stage_id' => 2 ]);

      $attrInsert = $connection->prepare(
          "UPDATE form_attributes 
          SET options = '$stage' WHERE form_attributes.key = 'stage_default' AND form_stage_id = :form_stage_id")->execute([ ':form_stage_id' => 2 ]);         
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
    // Delete  foreign keys to posts_tags table
        $this->table('woreda_context_risk')
            ->dropForeignKey('woreda_context_id')
            ->update()
            ;
  }
}
