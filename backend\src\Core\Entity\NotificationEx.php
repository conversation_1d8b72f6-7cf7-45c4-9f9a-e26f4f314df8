<?php

// UJEVMS-62 - <PERSON><PERSON><PERSON>, <EMAIL> - 14/02/2020.

namespace <PERSON><PERSON><PERSON><PERSON>\Core\Entity;

use <PERSON><PERSON><PERSON><PERSON>\Core\StaticEntity;

class NotificationEx extends StaticEntity
{
    protected $id;
    protected $user_id;
    protected $post_id;
    protected $event_type;
    protected $created;
    protected $post;
    protected $comment_user;

    protected function getDefinition()
    {
        return [
            'id'            => 'int',
            'user_id'       => 'int',
            'post_id'       => 'int',
            'event_type'    => 'string',
            'created'       => 'int',
            'comment_user'  => 'string'
        ];
    }

    public function getResource()
    {
        return 'notifications_ex';
    }
}
