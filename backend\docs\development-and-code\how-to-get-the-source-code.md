# How to get the source code

Note, this page contains shortcuts to each repository. If you are setting up the Platform for the first time, please follow our **setup guides** to set up the code **for your specific purpose:**

{% page-ref page="setup\_alternatives/" %}

## Platform API

The API-source-code is found in the [Platform API repository.](https://github.com/ushahidi/platform)

To get the source-code, clone the code with:

```text
git clone https://github.com/ushahidi/platform.git
```

## Platform Client

The Platform soure-code is found in the[ Platform Client repository](https://github.com/ushahidi/platform-client).

To get the source-code, clone the code with:

```
git clone https://github.com/ushahidi/platform-client.git
```

## Platform Pattern Library

The source-code for the Pattern Library is found in the [Platform Pattern Library repository](https://github.com/ushahidi/platform-pattern-library).

To get the source-code, clone the code with:

```text
git clone https://github.com/ushahidi/platform-pattern-library.git;
```





