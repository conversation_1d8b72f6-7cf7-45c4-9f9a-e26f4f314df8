<?php

use Phinx\Migration\AbstractMigration;

class AddPostsPriority extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {
    $this->table('posts')
      ->addColumn('priority', 'string', [
        'limit' => 20,
        'default' => 'standard',
        'comment' => 'standard, urgent',
      ])
      ->update();
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
