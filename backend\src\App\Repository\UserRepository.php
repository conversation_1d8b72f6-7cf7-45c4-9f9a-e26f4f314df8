<?php

/**
 * Ushahidi User Repository
 *
 * Also implements registration checks
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Repository;

use Ohanzee\DB;
use <PERSON>hahidi\Core\Entity;
use Us<PERSON>hidi\Core\Entity\User;
use Ushahidi\Core\Entity\UserRepository as UserRepositoryContract;
use Ushahidi\Core\SearchData;
use Ushahidi\Core\Tool\Hasher;
use Ushahidi\Core\Usecase\User\RegisterRepository;
use Ushahidi\Core\Usecase\User\ResetPasswordRepository;

use League\Event\ListenerInterface;
use Ushahidi\Core\Traits\Event;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class UserRepository extends OhanzeeRepository implements
    UserRepositoryContract,
    RegisterRepository,
    ResetPasswordRepository
{
    /**
     * @var Hasher
     */
    protected $hasher;

    // Use Event trait to trigger events
    use Event;

    /**
     * @param  Hasher $hasher
     * @return $this
     */
    public function setHasher(Hasher $hasher)
    {
        $this->hasher = $hasher;
        return $this;
    }

    // OhanzeeRepository
    protected function getTable()
    {
        return 'users';
    }

    // OhanzeeRepository
    public function getEntity(array $data = null)
    {
        if (!empty($data['id'])) {
            $data += [
                'contacts' => $this->getContacts($data['id']),
            ];
        }
        return new User($data);
    }

    protected function getContacts($entity_id)
    {
        // Unfortunately there is a circular reference created if the Contact repo is
        // injected into the User repo to avoid this we access the table directly
        // NOTE: This creates a hard coded dependency on the table naming for contacts
        $query = DB::select('*')->from('contacts')
            ->where('user_id', '=', $entity_id);

        $results = $query->execute($this->db());

        return $results->as_array();
    }

    // CreateRepository
    public function create(Entity $entity)
    {
        $state = [
            'created'  => time(),
            'password' => $this->hasher->hash($entity->password),
        ];
        $entity->setState($state);
        if ($entity->role === 'admin') {
            $this->updateIntercomAdminUsers($entity);
        }
        return parent::create($entity);
    }

    // CreateRepository
    public function createWithHash(Entity $entity)
    {
        $state = [
            'created'  => time()
        ];
        $entity->setState($state);
        if ($entity->role === 'admin') {
            $this->updateIntercomAdminUsers($entity);
        }

        return parent::create($entity);
    }

    // UpdateRepository
    public function update(Entity $entity)
    {
        $user               = $entity->getChanged();
        $user['updated']    = time();
        unset($user['contacts']);       // Remove contacts
        
        if ($entity->hasChanged('password')) {
            $user['password'] = $this->hasher->hash($entity->password);
        }

        if ($entity->role === 'admin') {
            $this->updateIntercomAdminUsers($entity);
        }

         // Force to USER role if not set
        if (is_null($entity->role) || $entity->role == ""){
            $entity->role = "user";
        }
        return $this->executeUpdate(['id' => $entity->id], $user);
    }

    // SearchRepository
    public function getSearchFields()
    {
        return ['email', 'role', 'q' /* LIKE realname, email */];
    }

    // SearchRepository
    public function setSearchConditions(SearchData $search)
    {
        $query = $this->search_query;
        $table = $this->getTable();

        if ($search->q) {
            $query->and_where_open();
            $query->where('email', 'LIKE', "%" . $search->q . "%");
            $query->or_where('realname', 'LIKE', "%" . $search->q . "%");
            $query->and_where_close();

            // Adding search contacts
            $query->join('contacts', 'left')->on("$table.id", '=', 'contacts.user_id')
                ->or_where('contacts.contact', 'like', '%' . $search->q . '%');
        }

        if ($search->role) {
            $role = $search->role;
            if (!is_array($search->role)) {
                $role = explode(',', $search->role);
            }

            $query->where('role', 'IN', $role);
        }


        return $query;
    }

    // UserRepository
    public function getByEmail($email)
    {
        return $this->getEntity($this->selectOne(['email' => $email, 'active' => 1]));
    }

    // RegisterRepository
    public function isUniqueEmail($email)
    {
        return $this->selectCount(compact('email')) === 0;
    }

    // Should be check to avoid name collision
    public function isUniqueUsername($email)
    {
        return $this->selectCount(compact('email')) === 0;
    }

    // RegisterRepository
    public function register(Entity $entity)
    {
        // if(!$this->isUniqueEmail($entity->email)){
        //     throw new \Exception("The registration cannot be completed with the provided credentials", 0);
        // }

        return $this->executeInsert([
            'active'        => true,
            'realname'      => $entity->realname,
            'email'         => $entity->email,
            'password'      => $this->hasher->hash($entity->password),
            'role'          => $entity->role,
            'mgmt_lev_1'    => isset($entity->mgmt_lev_1)?$entity->mgmt_lev_1 :'',
            'mgmt_lev_2'    => isset($entity->mgmt_lev_2)?$entity->mgmt_lev_2 :'',
            'mgmt_lev_3'    => isset($entity->mgmt_lev_3)?$entity->mgmt_lev_3 :'', 
            'organization'  => $entity->organization,
            'position'      => $entity->position,
            'created'       => time()
        ]);
    }

    // RegisterRepository
    public function selfRegister(Entity $entity)
    {
        // if(!$this->isUniqueEmail($entity->email)){
        //     throw new \Exception("The registration cannot be completed with the provided credentials", 0);
        // }

        return $this->executeInsert([
            'active'        => false,
            'realname'      => $entity->realname,
            'email'         => $entity->email,
            'password'      => $this->hasher->hash($entity->password),
            'organization'  => $entity->organization,
            'position'      => $entity->position,
            'role'          => 'user',
            'created'       => time()
        ]);
    }


    // ResetPasswordRepository
    public function getResetToken(Entity $entity)
    {
        $token = Hash::make(Str::random(40));

        $input = [
            'reset_token' => $token,
            'user_id' => $entity->id,
            'created' => time()
        ];

        // Save the token
        $query = DB::insert('user_reset_tokens')
            ->columns(array_keys($input))
            ->values(array_values($input))
            ->execute($this->db());

        return $token;
    }

    // ResetPasswordRepository
    public function isValidResetToken($token)
    {
        $result = DB::select([DB::expr('COUNT(*)'), 'total'])
            ->from('user_reset_tokens')
            ->where('reset_token', '=', $token)
            ->where('created', '>', time() - 1800) // Expire tokens after less than 30 mins
            ->execute($this->db());
        $count = $result->get('total') ?: 0;

        return $count !== 0;
    }

    // ResetPasswordRepository
    public function setPassword($token, $password)
    {
        $sub = DB::select('user_id')
            ->from('user_reset_tokens')
            ->where('reset_token', '=', $token);

        $this->executeUpdate(['id' => $sub], [
            'password' => $this->hasher->hash($password)
        ]);
    }

    public function resetPassword($id, $password)
    {
        $this->executeUpdate(['id' =>  $id], [
            'password' => $this->hasher->hash($password)
        ]);
    }

    // ResetPasswordRepository
    public function deleteResetToken($token)
    {
        $result = DB::delete('user_reset_tokens')
            ->where('reset_token', '=', $token)
            ->execute($this->db());
    }

    /**
     * Get total count of entities
     * @param  Array $where
     * @return int
     */
    public function getTotalCount(array $where = [])
    {
        return $this->selectCount($where);
    }

    // DeleteRepository
    public function delete(Entity $entity)
    {
        if ($entity->role === 'admin') {
            $this->updateIntercomAdminUsers($entity);
        }

        $result = DB::select([DB::expr('COUNT(*)'), 'total'])
            ->from('posts')
            ->where('user_id', '=', $entity->id)
            ->execute($this->db());
        $count = $result->get('total') ?: 0;

        if ($count > 0) {
            abort(409, 'Sorry, this user account cannot be deleted due to dependencies.');
        }
        
        return parent::delete($entity);
    }

    /**
     * Pass User count to Intercom
     * takes a postive/negative offset by which to increase/decrease count for create/delete
     * @param Integer $offset
     * @return void
     */
    protected function updateIntercomAdminUsers($user)
    {
        $this->emit($this->event, $user);
    }

    // Update last login
    public function updateLastLogin(Entity $entity)
    {
        $input['last_login'] = time();
        $updateQuery = DB::update('users')
            ->set($input)
            ->where('id', '=', $entity->id);
        return $updateQuery->execute($this->db());
    }

         // UserRepository
    public function getById($id)
    {
        return $this->getEntity($this->selectOne(compact('id')));
    }
}
