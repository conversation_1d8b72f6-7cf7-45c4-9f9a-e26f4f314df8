---
description: >-
  Learn how you can contribute to the Ushahidi Platform to support our shared
  mission to help marginalized people raise their voice and those who serve them
  to listen and respond better.
---

# Contributing \| Getting Involved

## **Required reading: Code Of Conduct**

We love having you here. But before you start, to ensure everyone has a good experience, we ask **everyone** that interacts with our community and staff to read our code of conduct.

{% page-ref page="../code-of-conduct.md" %}

## **Ways of contributing**

There are many ways to get involved with <PERSON><PERSON><PERSON><PERSON>, and some of them are great for first time contributors. If you never contributed to Open Source Software before, or need more guidance doing it, please join the community in one of the channels listed [here](../get-in-touch.md).

### 📝 **I am interested in writing documentation**

**I**f you find an area of the Ushahidi platform that could use better docs, we would love to hear from you in an [issue](https://github.com/ushahidi/platform/issues/new/choose), and would be seriously excited if you send a [Pull Request](https://github.com/ushahidi/platform/compare). **This is a great way to get involved,** and one of the highest impact changes you can make at the moment. You can choose to work on user documentation, fix typos, or add new sections for discussion and collaboration with the community. Every little bit helps.

### 🐛 I found a bug and want to r**eport it**

If you found an issue/bug, please report it [here](https://github.com/ushahidi/platform/issues). Someone on the team will jump in to check it, try to help, and prioritize it for future development depending on the issue type.

If you think you have found a security issue, please follow [this link where we explain our disclosure and reporting policies](https://www.ushahidi.com/security).

### ⌨️ **I want to fix a bug**

If you want to contribute a fix for a bug you or someone else found, we will be happy to review your PR and provide support**.** You can find our issues here: 

* [All issues available for the community](https://github.com/ushahidi/platform/issues?q=is%3Aopen+is%3Aissue+label%3A%22Community+Task%22)
* [First-timers-only-issues](https://github.com/ushahidi/platform/issues?q=is%3Aopen+is%3Aissue+label%3Afirst-timers-only)
* [Good starter-issues](https://github.com/ushahidi/platform/issues?q=is%3Aopen+is%3Aissue+label%3Agood-first-issue)

Before you start coding, read through the [Development & Code](../development-and-code/getting-started.md) section to get started and to get help setting up your environment!

#### 💡A note about n**ew features**

Feature development is generally driven by our product and engineering team members, but if you have a great idea or found a user need that we haven't covered, you are more than welcome to make a suggestion in the form of a GitHub issue [here](https://github.com/ushahidi/platform/issues), or reach out to Ushahidi staff in [Gitter](https://gitter.im/ushahidi/Community).

### 🎨 I want to contribute to Design

If you are a designer and want to contribute to Ushahidi, please contact the Ushahidi design team at [<EMAIL>](mailto:%<EMAIL>) or through the [Join the Ushahidi community](../get-in-touch.md) section of these docs. While you are waiting for us to reply, start reading through our [Design-docs](../design/design-process.md).

### 🔍 I want to do QA and testing

If you are interested in helping us QA and test new releases, please send an email to [<EMAIL>](mailto:<EMAIL>) or through the [Get in Touch](https://ushahidi.gitbook.io/platform-developer-documentation/contributing-or-getting-involved/get-in-touch) section of these docs to request access to the test environment. While you are waiting for us to reply, start reading trough the [QA & Testing](../qa-and-testing/the-qa-process.md) docs.

### 🌍 I want to contribute to translations

Help us make the platform available in as many languages as possible.

* Translations live in our [Transifex repository](https://transifex.com/ushahidi/ushahidi-v3/)
* [Instructions on how to start translating](../translation/software-localization-and-translation.md)

### 💁 I want to h**elp other users in the community**

You are welcome and encouraged to jump in and help other members of the community, either by responding to issues in GitHub or jumping into our community channels to answer questions. Read more [here](../get-in-touch.md) how to join the community channels.

### 👨‍💻 I want to help out in a project as a volunteer

We encourage community development workers, international development workers, activists and volunteers to join in and contribute with their knowledge and experience. Check out the section [Encouraging contribution from non-developers](encouraging-contribution-from-non-developers.md) to learn more how you can get involved.

### 



