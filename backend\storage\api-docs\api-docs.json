{"swagger": "2.0", "info": {"title": "This is Ewer API", "description": "Api description...", "termsOfService": "", "contact": {"email": "<EMAIL>"}, "license": {"name": "Private License", "url": "URL to the license"}, "version": "1.0.0"}, "host": "localhost:4199/api", "basePath": "/", "schemes": ["http", "https"], "paths": {"/v3/register": {"post": {"tags": ["User"], "summary": "register new user", "description": "enter the fields below to register new user", "produces": ["application/json"], "parameters": [{"name": "body", "in": "body", "description": "Register new user", "required": true, "schema": {"properties": {"email": {"description": "email", "type": "string"}, "password": {"description": "password", "type": "string"}}, "type": "object"}}], "responses": {"200": {"description": "successful operation"}, "401": {"description": "Credentials Incorrect."}, "422": {"description": "Validation Error"}}}}}, "definitions": {}, "externalDocs": {"description": "Find out more about my website", "url": "http..."}}