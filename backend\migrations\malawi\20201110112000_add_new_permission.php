<?php

// UJEVMS-258 - <PERSON><PERSON><PERSON><PERSON><PERSON>, <EMAIL> - 10/11/2020.


use Phinx\Migration\AbstractMigration;

class AddNewPermission extends AbstractMigration
{
  /**
   * Change Method.
   *
   * More information on this method is available here:
   * http://docs.phinx.org/en/latest/migrations.html#the-change-method
   */
  public function change()
  {

    $this->execute("
    start transaction;
    
    set FOREIGN_KEY_CHECKS = 0;
    
    insert into roles_permissions (role, permission) values
        ('super', 'Manage Collections, Saved Searches and Messages'),
        ('admin', 'Manage Collections, Saved Searches and Messages'),
        ('admin', 'Create Users');          
    set FOREIGN_KEY_CHECKS = 1;
    
    commit;");
  }

  /**
   * Migrate Up.
   */
  public function up()
  {
    // noop, uses change()
  }

  /**
   * Migrate Down.
   */
  public function down()
  {
    // noop, uses change()
  }
}
