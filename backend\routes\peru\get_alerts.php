<?php

// Get Alerts
$router->group([
    'middleware' => ['scope:get-alerts']
], function () use ($router) {

    // Public access
    $router->post('/get-alerts', 'GetAlertsController@save_alert');
    $router->get('/get-alerts/unsubscribe-email/{hash}', 'GetAlertsController@unsubscribeEmail');

    // Restricted access
    // $router->group([
    //     'middleware' => ['auth:api']
    // ], function () use ($router) {
    //     //
    // });
});
