<?php

namespace Us<PERSON>hidi\Core;

use Aura\Di\Container;
use Aura\Di\ContainerConfig;

class CoreConfig extends ContainerConfig
{
    public function define(Container $di)
    {
        // Validators are used to parse **and** verify input data used for write operations.
        $di->set('factory.validator', $di->lazyNew('Ushahidi\Factory\ValidatorFactory'));

        // Implemented validators will be mapped to resources and actions.
        $di->params['Ushahidi\Factory\ValidatorFactory']['map'] = [];

        // Authorizers are used to check if the accessing user has permission to use an action.
        $di->set('factory.authorizer', $di->lazyNew('Ushahidi\Factory\AuthorizerFactory'));

        // Authorizers are shared, so mapping is done with service names.
        $di->params['Ushahidi\Factory\AuthorizerFactory']['map'] = [
            'config'               => $di->lazyGet('authorizer.config'),
            'dataproviders'        => $di->lazyGet('authorizer.dataprovider'),
            'export_jobs'          => $di->lazyGet('authorizer.export_job'),
            'country_codes'        => $di->lazyGet('authorizer.country_code'),
            'external_auth'        => $di->lazyGet('authorizer.external_auth'),
            'forms'                => $di->lazyGet('authorizer.form'),
            'form_contacts'        => $di->lazyGet('authorizer.form_contact'),
            'form_attributes'      => $di->lazyGet('authorizer.form_attribute'),
            'form_roles'           => $di->lazyGet('authorizer.form_role'),
            'form_stages'          => $di->lazyGet('authorizer.form_stage'),
            'form_stats'           => $di->lazyGet('authorizer.form_stats'),
            'tags'                 => $di->lazyGet('authorizer.tag'),
            'layers'               => $di->lazyGet('authorizer.layer'),
            'media'                => $di->lazyGet('authorizer.media'),
            'messages'             => $di->lazyGet('authorizer.message'),
            'posts'                => $di->lazyGet('authorizer.post'),
            // UJEVMS-52 - SCALA Luca, <EMAIL> - 27/01/2020.
            'post_comments'        => $di->lazyGet('authorizer.post_comments'),
            // UJEVMS-90 - Fulpagare Pramod, <EMAIL> - 03/03/2020.
            'users_ex'        => $di->lazyGet('authorizer.users_ex'),
            // UJEVMS-65 - Fulpagare Pramod, <EMAIL> - 29/05/2020.
            'audit'        => $di->lazyGet('authorizer.audit'),
            'posts_lock'           => $di->lazyGet('authorizer.post_lock'),
            'tags'                 => $di->lazyGet('authorizer.tag'),
            'sets'                 => $di->lazyGet('authorizer.set'),
            'sets_posts'           => $di->lazyGet('authorizer.post'),
            'savedsearches'        => $di->lazyGet('authorizer.savedsearch'),
            'users'                => $di->lazyGet('authorizer.user'),
            'user_settings'        => $di->lazyGet('authorizer.user_setting'),
            'notifications'        => $di->lazyGet('authorizer.notification'),
            // UJEVMS-62 - SCALA Luca, <EMAIL> - 14/02/2020.
            'notifications_ex'     => $di->lazyGet('authorizer.notification_ex'),
            'woreda_context_risk'  => $di->lazyGet('authorizer.woreda_context_risk'),
            'webhooks'             => $di->lazyGet('authorizer.webhook'),
            'apikeys'              => $di->lazyGet('authorizer.apikey'),
            'contacts'             => $di->lazyGet('authorizer.contact'),
            'csv'                  => $di->lazyGet('authorizer.csv'),
            'roles'                => $di->lazyGet('authorizer.role'),
            'permissions'          => $di->lazyGet('authorizer.permission'),
            'posts_export'         => $di->lazyGet('authorizer.post'),
            'tos'                   => $di->lazyGet('authorizer.tos'),
        ];

        // Repositories are used for storage and retrieval of records.
        $di->set('factory.repository', $di->lazyNew('Ushahidi\Factory\RepositoryFactory'));

        // Repositories are shared, so mapping is done with service names.
        $di->params['Ushahidi\Factory\RepositoryFactory']['map'] = [
            'config'               => $di->lazyGet('repository.config'),
            'country_codes'        => $di->lazyGet('repository.country_code'),
            'export_jobs'          => $di->lazyGet('repository.export_job'),
            'dataproviders'        => $di->lazyGet('repository.dataprovider'),
            'targeted_survey_states'   => $di->lazyGet('repository.targeted_survey_state'),
            'forms'                => $di->lazyGet('repository.form'),
            'form_attributes'      => $di->lazyGet('repository.form_attribute'),
            'form_contacts'      => $di->lazyGet('repository.form_contact'),
            'form_stats'      => $di->lazyGet('repository.form_stats'),
            'form_roles'           => $di->lazyGet('repository.form_role'),
            'form_stages'          => $di->lazyGet('repository.form_stage'),
            'layers'               => $di->lazyGet('repository.layer'),
            'media'                => $di->lazyGet('repository.media'),
            'messages'             => $di->lazyGet('repository.message'),
            'posts'                => $di->lazyGet('repository.post'),
            // UJEVMS-52 - SCALA Luca, <EMAIL> - 27/01/2020.
            'post_comments'        => $di->lazyGet('repository.post_comments'),
            // UJEVMS-90 - Fulpagare Pramod, <EMAIL> - 03/03/2020.
            'users_ex'        => $di->lazyGet('repository.users_ex'),
            // UJEVMS-65 - Fulpagare Pramod, <EMAIL> - 29/05/2020.
            'audit'        => $di->lazyGet('repository.audit'),
            'posts_lock'           => $di->lazyGet('repository.post_lock'),
            'tags'                 => $di->lazyGet('repository.tag'),
            'sets'                 => $di->lazyGet('repository.set'),
            'sets_posts'           => $di->lazyGet('repository.post'),
            'savedsearches'        => $di->lazyGet('repository.savedsearch'),
            'users'                => $di->lazyGet('repository.user'),
            'user_settings'        => $di->lazyGet('repository.user_setting'),
            'notifications'        => $di->lazyGet('repository.notification'),
            // UJEVMS-62 - SCALA Luca, <EMAIL> - 14/02/2020.
            'notifications_ex'     => $di->lazyGet('repository.notification_ex'),
            'woreda_context_risk'     => $di->lazyGet('repository.woreda_context_risk'),
            'webhooks'             => $di->lazyGet('repository.webhook'),
            'apikeys'              => $di->lazyGet('repository.apikey'),
            'contacts'             => $di->lazyGet('repository.contact'),
            'csv'                  => $di->lazyGet('repository.csv'),
            'roles'                => $di->lazyGet('repository.role'),
            'permissions'          => $di->lazyGet('repository.permission'),
            'posts_export'         => $di->lazyGet('repository.export_batch'),
            'tos'                  => $di->lazyGet('repository.tos'),
        ];

        // Formatters are used for to prepare the output of records. Actions that return
        // multiple results use collection formatters for recursion.
        $di->set('factory.formatter', $di->lazyNew('Ushahidi\Factory\FormatterFactory'));

        // Implemented collection formatter will register as the factory.
        $di->params['Ushahidi\Factory\FormatterFactory']['factory'] = null;

        // Formatters used on collections of records are run recursively. This expectation
        // is mapped by actions that return collections.
        $di->params['Ushahidi\Factory\FormatterFactory']['collections'] = [
            'search' => true,
            'update_collection' => true
        ];

        // Data transfer objects are used to carry complex search filters between collaborators.
        $di->set('factory.data', $di->lazyNew('Ushahidi\Factory\DataFactory'));

        // Usecases that perform searches are the most typical usage of data objects.
        $di->params['Ushahidi\Factory\DataFactory']['actions'] = [
            'search' => $di->lazyNew('Ushahidi\Core\SearchData'),
            'stats'  => $di->lazyNew('Ushahidi\Core\SearchData'),
            'export'  => $di->lazyNew('Ushahidi\Core\SearchData'),
            'analysis'  => $di->lazyNew('Ushahidi\Core\SearchData'),
            'readdata'  => $di->lazyNew('Ushahidi\Core\SearchData'),
        ];

        // Use cases are used to join multiple collaborators together for a single interaction.
        $di->set('factory.usecase', $di->lazyNew('Ushahidi\Factory\UsecaseFactory'));
        $di->params['Ushahidi\Factory\UsecaseFactory'] = [
            'authorizers'  => $di->lazyGet('factory.authorizer'),
            'repositories' => $di->lazyGet('factory.repository'),
            'formatters'   => $di->lazyGet('factory.formatter'),
            'validators'   => $di->lazyGet('factory.validator'),
            'data'         => $di->lazyGet('factory.data'),
        ];

        // Each of the actions follows a standard sequence of events and is simply constructed
        // with a unique set of collaborators that follow specific interfaces.
        $di->params['Ushahidi\Factory\UsecaseFactory']['actions'] = [
            'create' => $di->newFactory('Ushahidi\Core\Usecase\CreateUsecase'),
            'read'   => $di->newFactory('Ushahidi\Core\Usecase\ReadUsecase'),
            'update' => $di->newFactory('Ushahidi\Core\Usecase\UpdateUsecase'),
            'delete' => $di->newFactory('Ushahidi\Core\Usecase\DeleteUsecase'),
            'search' => $di->newFactory('Ushahidi\Core\Usecase\SearchUsecase'),
            'options' => $di->newFactory('Ushahidi\Core\Usecase\OptionsUsecase'),
        ];

        // It is also possible to overload usecases by setting a specific resource and action.
        // The same collaborator mapping will be applied by action as with default use cases.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map'] = [];

        // Config does not allow ordering or sorting, because of its simple key/value nature.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['config'] = [
            'search' => $di->newFactory('Ushahidi\Core\Usecase\Config\SearchConfig'),
        ];

        // Form sub-endpoints must verify that the form exists before anything else.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['form_attributes'] = [
            'create'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\CreateFormAttribute'),
            'read'    => $di->lazyNew('Ushahidi\Core\Usecase\Form\ReadFormAttribute'),
            'update'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\UpdateFormAttribute'),
            'delete'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\DeleteFormAttribute'),
            'search'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\SearchFormAttribute'),
        ];
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['form_roles'] = [
            'update_collection'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\UpdateFormRole'),
            'search'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\SearchFormRole'),
        ];

        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['form_contacts'] = [
            'create'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\CreateFormContact'),
            'read'    => $di->lazyNew('Ushahidi\Core\Usecase\Form\ReadFormContact'),
            //'update'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\UpdateFormContact'),
            // 'delete'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\DeleteFormContact'),
            'search'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\SearchFormContact'),
        ];

        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['form_stats'] = [
            'search'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\SearchFormStats'),
        ];

        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['woreda_context_risk'] = [
            'readdata'  => $di->lazyNew('Ushahidi\Core\Usecase\WoredaContextRepository')
        ];

        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['form_stages'] = [
            'create'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\CreateFormStage'),
            'read'    => $di->lazyNew('Ushahidi\Core\Usecase\Form\ReadFormStage'),
            'update'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\UpdateFormStage'),
            'delete'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\DeleteFormStage'),
            'search'  => $di->lazyNew('Ushahidi\Core\Usecase\Form\SearchFormStage'),
        ];

        // Media create requires file uploading as part of the payload.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['media'] = [
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\Media\CreateMedia'),
        ];
        $di->setters['Ushahidi\Core\Usecase\Media\CreateMedia']['setUploader'] = $di->lazyGet('tool.uploader');

        // CSV requires file upload
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['csv'] = [
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\CSV\CreateCSVUsecase'),
            'read'    => $di->lazyNew('Ushahidi\Core\Usecase\ReadUsecase'),
            'delete' => $di->lazyNew('Ushahidi\Core\Usecase\CSV\DeleteCSVUsecase'),
        ];

        $di->setters['Ushahidi\Core\Usecase\CSV\CreateCSVUsecase']['setUploader'] = $di->lazyGet('tool.uploader');
        $di->setters['Ushahidi\Core\Usecase\CSV\CreateCSVUsecase']['setReaderFactory']
            = $di->lazyGet('csv.reader_factory');
        $di->setters['Ushahidi\Core\Usecase\CSV\DeleteCSVUsecase']['setUploader'] = $di->lazyGet('tool.uploader');

        // Message update requires extra validation of message direction+status.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['messages'] = [
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\Message\CreateMessage'),
            'update' => $di->lazyNew('Ushahidi\Core\Usecase\Message\UpdateMessage'),
            'receive' => $di->newFactory('Ushahidi\Core\Usecase\Message\ReceiveMessage'),
        ];
        // Message receive requires extra repos
        $di->setters['Ushahidi\Core\Usecase\Message\ReceiveMessage']['setContactRepository']
            = $di->lazyGet('repository.contact');
        $di->setters['Ushahidi\Core\Usecase\Message\ReceiveMessage']['setTagRepository']
            = $di->lazyGet('repository.tag');
        $di->setters['Ushahidi\Core\Usecase\Message\ReceiveMessage']['setContactValidator']
            = $di->lazyGet('validator.contact.receive');

        // Add custom usecases for posts
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['posts'] = [
            'create'          => $di->lazyNew('Ushahidi\Core\Usecase\Post\CreatePost'),
            'read'            => $di->lazyNew('Ushahidi\Core\Usecase\Post\ReadPost'),
            'update'          => $di->lazyNew('Ushahidi\Core\Usecase\Post\UpdatePost'),
            'webhook-update'  => $di->lazyNew('Ushahidi\Core\Usecase\Post\WebhookUpdatePost'),
            'delete'          => $di->lazyNew('Ushahidi\Core\Usecase\Post\DeletePost'),
            'search'          => $di->lazyNew('Ushahidi\Core\Usecase\Post\SearchPost'),
            'stats'           => $di->lazyNew('Ushahidi\Core\Usecase\Post\StatsPost'),
            'analysis'        => $di->lazyNew('Ushahidi\Core\Usecase\Post\AnalysisReportPost'),
            'import'          => $di->lazyNew('Ushahidi\Core\Usecase\ImportUsecase')
        ];

        // UJEVMS-52 - SCALA Luca, <EMAIL> - 27/01/2020.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['post_comments'] = [
            'search'          => $di->lazyNew('Ushahidi\Core\Usecase\Post\SearchPostComment'),
            'create'          => $di->lazyNew('Ushahidi\Core\Usecase\Post\CreatePostComment'),
        ];


        // UJEVMS-90 - Fulpagare Pramod, <EMAIL> - 03/03/2020.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['users_ex'] = [
            'create'          => $di->lazyNew('Ushahidi\Core\Usecase\User\CreateUserExtra'),
            'read'            => $di->lazyNew('Ushahidi\Core\Usecase\User\ReadUserExtra'),
        ];

        // UJEVMS-65 - Fulpagare Pramod, <EMAIL> - 29/05/2020.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['audit'] = [
            'search'          => $di->lazyNew('Ushahidi\Core\Usecase\Post\SearchAudit'),
            'read'            => $di->lazyNew('Ushahidi\Core\Usecase\Post\ReadAudit'),
        ];

        // Add custom create usecase for notifications
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['notifications'] = [
            'create'  => $di->lazyNew('Ushahidi\Core\Usecase\Notification\CreateNotification')
        ];

        // UJEVMS-62 - SCALA Luca, <EMAIL> - 17/02/2020.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['notifications_ex'] = [
            'search'          => $di->lazyNew('Ushahidi\Core\Usecase\NotificationEx\SearchNotificationEx')
        ];

        // Add custom create usecase for webhooks
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['webhooks'] = [
            'create'  => $di->lazyNew('Ushahidi\Core\Usecase\Webhook\CreateWebhook')
        ];

        // Add custom create usecase for export jobs
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['export_jobs'] = [
            'create'  => $di->lazyNew('Ushahidi\Core\Usecase\Export\Job\CreateJob'),
            'post-count'  => $di->lazyNew('Ushahidi\Core\Usecase\Export\Job\PostCount')
        ];
        // Add custom create usecase for contacts
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['contacts'] = [
            'create'  => $di->lazyNew('Ushahidi\Core\Usecase\Contact\CreateContact')
        ];

        // Add custom create usecase for terms of service
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['tos'] = [
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\Tos\CreateTos'),
            'search' => $di->lazyNew('Ushahidi\Core\Usecase\Tos\SearchTos'),
        ];

        // Add custom usecases for sets_posts
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['sets_posts'] = [
            'search' => $di->lazyNew('Ushahidi\Core\Usecase\Set\SearchSetPost'),
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\Set\CreateSetPost'),
            'delete' => $di->lazyNew('Ushahidi\Core\Usecase\Set\DeleteSetPost'),
            'read'   => $di->lazyNew('Ushahidi\Core\Usecase\Set\ReadSetPost'),
        ];

        // Add custom useses for post_lock
        // Add usecase for posts_lock
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['posts_lock'] = [
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\Post\CreatePostLock'),
            'delete' => $di->lazyNew('Ushahidi\Core\Usecase\Post\DeletePostLock'),
        ];

        $di->setters['Ushahidi\Core\Usecase\Post\PostLockTrait']['setPostRepository'] = $di->lazyGet('repository.post');

        // Add custom usecases for sets_posts
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['savedsearches'] = [
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\Set\CreateSet'),
        ];

        // Add custom usecases for sets_posts
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['sets'] = [
            'create' => $di->lazyNew('Ushahidi\Core\Usecase\Set\CreateSet'),
        ];

        // Add usecase for posts_export
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['posts_export'] = [
            'export' => $di->lazyNew('Ushahidi\Core\Usecase\Post\Export'),
        ];


        // Set up traits for SetsPosts Usecases
        $di->setters['Ushahidi\Core\Usecase\Set\SetRepositoryTrait']['setSetRepository']
            = $di->lazyGet('repository.set');
        $di->setters['Ushahidi\Core\Usecase\Set\AuthorizeSet']['setSetAuthorizer']
            = $di->lazyGet('authorizer.set');

        // repositories for Ushahidi\Core\Usecase\Post\Export usecase
        $di->setters['Ushahidi\Core\Usecase\Post\Export']['setExportJobRepository']
            = $di->lazyGet('repository.export_job');
        $di->setters['Ushahidi\Core\Usecase\Post\Export']['setFormAttributeRepository']
            = $di->lazyGet('repository.form_attribute');
        $di->setters['Ushahidi\Core\Usecase\Post\Export']['setPostExportRepository']
            = $di->lazyGet('repository.posts_export');

        $di->setters['Ushahidi\Core\Usecase\Post\Export']['setHXLFromAttributeHxlAttributeTagRepo'] =
            $di->lazyGet('repository.form_attribute_hxl_attribute_tag');

        // User login is a custom read the uses authentication.
        $di->params['Ushahidi\Factory\UsecaseFactory']['map']['users'] = [
            'login'    => $di->lazyNew('Ushahidi\Core\Usecase\User\LoginUser'),
            'register' => $di->lazyNew('Ushahidi\Core\Usecase\User\RegisterUser'),
            'getresettoken' => $di->lazyNew('Ushahidi\Core\Usecase\User\GetResetToken'),
            'passwordreset' => $di->lazyNew('Ushahidi\Core\Usecase\User\ResetUserPassword'),
        ];
        $di->setters['Ushahidi\Core\Usecase\User\LoginUser']['setAuthenticator']
            = $di->lazyGet('tool.authenticator.password');
        $di->setters['Ushahidi\Core\Usecase\User\LoginUser']['setRateLimiter'] = $di->lazyGet('ratelimiter.login');

        $di->setters['Ushahidi\Core\Usecase\User\RegisterUser']['setRateLimiter']
            = $di->lazyGet('ratelimiter.register');

        $di->setters['Ushahidi\Core\Usecase\User\GetResetToken']['setMailer'] = $di->lazyGet('tool.mailer');

        // Traits
        $di->setters['Ushahidi\Core\Traits\UserContext']['setSession'] = $di->lazyGet('session');
        $di->setters['Ushahidi\Core\Usecase\Form\VerifyFormLoaded']['setFormRepository']
            = $di->lazyGet('repository.form');
        $di->setters['Ushahidi\Core\Usecase\Form\VerifyFormLoaded']['setFormContactRepository']
            = $di->lazyGet('repository.form_contact');
        $di->setters['Ushahidi\Core\Usecase\Form\VerifyStageLoaded']['setStageRepository']
            = $di->lazyGet('repository.form_stage');

        $di->setters['Ushahidi\Core\Traits\Event']['setEmitter'] = $di->lazyNew('League\Event\Emitter');
        // Set ACL for ACL Trait
        $di->setters['Ushahidi\Core\Tool\Permissions\AclTrait']['setAcl'] = $di->lazyGet('tool.acl');

        // Set post permissions instance
        $di->setters['Ushahidi\Core\Tool\Permissions\InteractsWithPostPermissions']['setPostPermissions']
            = $di->lazyNew(\Ushahidi\Core\Tool\Permissions\PostPermissions::class);

        // Set form permissions instance
        $di->setters['Ushahidi\Core\Tool\Permissions\InteractsWithFormPermissions']['setFormPermissions']
            = $di->lazyNew(\Ushahidi\Core\Tool\Permissions\FormPermissions::class);

        // Set ACL for ACL Trait
        $di->setters['Ushahidi\Core\Tool\Permissions\AclTrait']['setAcl'] = $di->lazyGet('tool.acl');

        // Tools
        $di->set('tool.signer', $di->lazyNew('Ushahidi\Core\Tool\Signer'));
        $di->set('tool.verifier', $di->lazyNew('Ushahidi\Core\Tool\Verifier', [
            'apiKeyRepo' => $di->lazyGet('repository.apikey')
        ]));
        $di->set('tool.uploader', $di->lazyNew('Ushahidi\Core\Tool\Uploader'));
        $di->params['Ushahidi\Core\Tool\Uploader'] = [
            'fs' => $di->lazyGet('tool.filesystem'),
            'multisite' => $di->lazyGet('multisite'),
        ];

        $di->set('authorizer.config', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\ConfigAuthorizer'));
        $di->set('authorizer.dataprovider', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\DataProviderAuthorizer'));
        $di->set('authorizer.form', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\FormAuthorizer'));
        $di->params['Ushahidi\Core\Tool\Authorizer\FormAuthorizer'] = [
            'form_repo' => $di->lazyGet('repository.form'),
        ];
        $di->set('authorizer.form_attribute', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\FormAttributeAuthorizer'));
        $di->params['Ushahidi\Core\Tool\Authorizer\FormAttributeAuthorizer'] = [
            'stage_repo' => $di->lazyGet('repository.form_stage'),
            'stage_auth' => $di->lazyGet('authorizer.form_stage'),
        ];
        $di->set('authorizer.form_role', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\FormRoleAuthorizer'));
        $di->params['Ushahidi\Core\Tool\Authorizer\FormRoleAuthorizer'] = [
            'form_repo' => $di->lazyGet('repository.form'),
            'form_auth' => $di->lazyGet('authorizer.form'),
        ];
        $di->set('authorizer.form_stage', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\FormStageAuthorizer'));
        $di->params['Ushahidi\Core\Tool\Authorizer\FormStageAuthorizer'] = [
            'form_repo' => $di->lazyGet('repository.form'),
            'form_auth' => $di->lazyGet('authorizer.form'),
        ];

        $di->set('authorizer.form_contact', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\FormContactAuthorizer'));
        $di->params['Ushahidi\Core\Tool\Authorizer\FormContactAuthorizer'] = [
            'form_repo' => $di->lazyGet('repository.form'),
            'form_auth' => $di->lazyGet('authorizer.form'),
        ];

        $di->set('authorizer.form_stats', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\FormStatsAuthorizer'));

        $di->set('authorizer.user', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\UserAuthorizer'));
        $di->set('authorizer.user_setting', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\UserSettingAuthorizer'));

        $di->set('authorizer.layer', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\LayerAuthorizer'));
        $di->set('authorizer.media', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\MediaAuthorizer'));
        $di->set('authorizer.message', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\MessageAuthorizer'));
        $di->set('authorizer.tag', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\TagAuthorizer'));
        $di->set('authorizer.savedsearch', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\SetAuthorizer'));
        $di->set('authorizer.set', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\SetAuthorizer'));
        $di->set('authorizer.notification', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\NotificationAuthorizer'));
        // UJEVMS-62 - SCALA Luca, <EMAIL> - 14/02/2020.
        $di->set('authorizer.notification_ex', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\NotificationExAuthorizer'));
        $di->set('authorizer.webhook', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\WebhookAuthorizer'));
        $di->set('authorizer.apikey', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\ApiKeyAuthorizer'));
        $di->set('authorizer.contact', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\ContactAuthorizer'));
        $di->set('authorizer.csv', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\CSVAuthorizer'));
        $di->set('authorizer.role', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\RoleAuthorizer'));
        $di->set('authorizer.permission', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\PermissionAuthorizer'));
        $di->set('authorizer.post', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\PostAuthorizer'));
        $di->set('authorizer.post_lock', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\PostAuthorizer'));
        // UJEVMS-52 - SCALA Luca, <EMAIL> - 28/01/2020.
        $di->set('authorizer.post_comments', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\PostCommentAuthorizer'));
        // UJEVMS-90 - Fulpagare Pramod, <EMAIL> - 03/03/2020.
        $di->set('authorizer.users_ex', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\UsersExtraAuthorizer'));
        // UJEVMS-65 - Fulpagare Pramod, <EMAIL> - 29/05/2020.
        $di->set('authorizer.audit', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\AuditAuthorizer'));
         $di->set('authorizer.woreda_context_risk', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\WoredaContextRiskAuthorizer'));
        $di->set('authorizer.tos', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\TosAuthorizer'));
        $di->set('authorizer.external_auth', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\ExternalAuthorizer'));
        $di->set('authorizer.export_job', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\ExportJobAuthorizer'));
        $di->params['Ushahidi\Core\Tool\Authorizer\PostAuthorizer'] = [
            'post_repo' => $di->lazyGet('repository.post'),
            'form_repo' => $di->lazyGet('repository.form'),
        ];
        $di->params['Ushahidi\Core\Tool\Authorizer\TagAuthorizer'] = [
            'tag_repo' => $di->lazyGet('repository.tag'),
        ];

        $di->set('authorizer.country_code', $di->lazyNew('Ushahidi\Core\Tool\Authorizer\CountryCodeAuthorizer'));
    }
}
