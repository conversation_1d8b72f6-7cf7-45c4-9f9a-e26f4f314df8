<?php

// UJEVMS-62 - <PERSON><PERSON><PERSON>, <EMAIL> - 17/02/2020.

namespace <PERSON><PERSON>hidi\Core\Tool\Authorizer;

use <PERSON><PERSON><PERSON>di\Core\Entity;
use Us<PERSON>hidi\Core\Tool\Authorizer;
use <PERSON><PERSON>hidi\Core\Traits\UserContext;
use Us<PERSON>hidi\Core\Traits\PrivAccess;
use Ushahidi\Core\Traits\PrivateDeployment;

class NotificationExAuthorizer implements Authorizer
{
    use UserContext;

    use PrivAccess;

    use PrivateDeployment;

    public function isAllowed(Entity $entity, $privilege)
    {
        $user = $this->getUser();
        if ($user->getId()) {
            return true;
        }

        return false;
    }
}
