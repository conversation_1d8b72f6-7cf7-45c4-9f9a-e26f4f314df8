<?php

use Phinx\Migration\AbstractMigration;

class AddObserverChecklistFormAttributes extends AbstractMigration
{

    /**
     * Migrate Up.
     */
    public function up()
    {

        // /home/<USER>/Projects/UNDP/ireport-malawi-prod/backend/migrations/malawi/20210505201000_update_initial_form_attribute_incident.php

        include 'observer_checklist_form_attributes_polling_stations.php';

        include 'observer_checklist_form_attributes_wards.php';

        $political_parties = '"Alliance for Democracy (AFORD)", "Assembly for Democracy and Development (ADD)", "Democratic Peoples Congress (DEPECO)", "Democratic Progressive Party (DPP)", "Freedom Party (FP)", "Leadership with Compassion Party (LCP)", "Liberation for Economic Freedom Party (LEFP)", "Malawi Congress Party (MCP)", "Malawi Forum for Unity and Development (MAFUNDE) Party", "Mbakuwaku Movement for Development (MMD)", "Muviwachilungamo Revolutionary Party (MRP)", "National Development Party (NDP)", "National Patriotic Party (NPP)", "Nzika Coalition (NC)", "Peoples Development Party, (PDP)", "Peoples Party (PP)", "Peoples Progressive Movement (PPM)", "Peoples Transformation Party (PETRA)", "Umodzi Party (UP)", "United Democratic Front (UDF)", "United Transformation Movement (UTM)"';


        $required = 1;
        $not_required = 0;


        $connection = $this->getAdapter()->getConnection();
        $adapter = $this->getAdapter();

        $i = 0;
//        ('OC_WARD_1', 'Ward', 'select', 'varchar', " . $required . ", " . $i++ . ",'" . $ward1 . "','[" . $ward_config1 . "]', :form_stage_id ),

// ('OC_TITLE', 'Title of incident', 'text', 'title', " . $required . ", " . $i++ . ", '[]',  '[]', :form_stage_id ),
// ('OC_DESC', 'Description', 'text', 'description', " . $required . ", " . $i++ . ", '[]',  '[]', :form_stage_id ),

$connection->prepare(
    "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
        ('OC_CONSTITUENCY', 'Constituency', 'tags', 'tags', " . $required . ", " . $i++ . ", '[]',  '[]', :form_stage_id ),
        ('OC_WARD', 'Ward', 'tags', 'tags', " . $required . ", " . $i++ . ", '[]',  '[]', :form_stage_id ),
        ('OC_POLLING_STATION', 'Polling Station', 'tags', 'tags', " . $required . ", " . $i++ . ", '[]',  '[]', :form_stage_id );
"
)->execute([':form_stage_id' => 2]);

/*

        $w_in = 1; $ps_in = 1;
        $ws =  "";   
        foreach ($wards_array as $key => $value) {
            $constituency = $this->fetchRow("SELECT id FROM tags WHERE tag = '" . $key . "' AND type = 'constituency'", 0);
            $constituency_id = $constituency[0];
      
            $ward_config = '[{"id":1,"action":"show","target":{"key":"OC_CONSTITUENCY", "value":"' . $constituency_id .'"}}]';    

        $ws .= "('OC_WARD_" . $w_in  ."', 'Ward', 'select', 'varchar', " . $not_required . ", " . $i++ . ",'[" .  str_replace("'", "\'" , $value) . "]','" . $ward_config . "', :form_stage_id ),";

        $val_array = explode(",", $value);
        foreach($val_array as $key1 => $value1) {
            ${"polling_config" . $ps_in} = '[{"id":1,"action":"show","target":{"key":"OC_WARD_'.  $w_in  . '", "value":"' . str_replace("'", "\'" , trim(trim($value1),'"')) .'"}}]';;    
            $ps_in++;
            }

        $w_in++;
        $constituency_id++;

        }
//        $ws = "('OC_WARD_1', 'Ward', 'select', 'varchar', " . $not_required . ", " . $i++ . ",'" . $ward1 . "','" . $ward_config1 . "', :form_stage_id ),";

//$ps = "('OC_POLLING_STATION_1', 'Ward', 'select', 'varchar', " . $not_required . ", " . $i++ . ",'" . $polling1 . "','" . $polling_config1 . "', :form_stage_id ),";

        $ps_in = 1;
        $ps =  "";   
        foreach ($polling_station_array as $key => $value) {
//            $polling_config = '[{"id":1,"action":"show","target":{"key":"OC_WARD_'.  $ps_in  . '", "value":"' . $key .'"}}]';    

            $ps .= "('OC_POLLING_STATION_" . $ps_in  ."', 'Polling Station', 'select', 'varchar', " . $not_required . ", " . $i++ . ",'[" . str_replace("'", "\'" ,$value) . "]','" . ${"polling_config" . $ps_in} . "', :form_stage_id ),";
            $ps_in++;
        }



        $ps = rtrim($ps, ",") . ';';

//        echo $ws ; //."==" . $ps ;


        $connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                " . $ws . $ps ."

        "
        )->execute([':form_stage_id' => 2]);
*/

        include 'observer_checklist_form_attributes_morning_questions.php';

        $time = json_encode(["Morning (6 to 10am)", "Afternoon (10am to 2pm)", "Evening (2pm to 4pm)", "Vote Count (4pm to 12am)"]);

        $connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('OC_TIME_1', 'Time Line', 'select', 'varchar'," . $required . ", " . $i++ . ", '" . $time . "', '[]', :form_stage_id );
        "
        )->execute([':form_stage_id' => 2]);

        $yes_no = '["Yes", "No"]';
        $delay_reason = '["No materials", "No polling staff", "Delayed security", "Other"]';
        
        $how_long_resolve = '["Less than 1 hour", "1–2 hours",  "2–3 hours", "3–4 hours", "4–5 hours", "More than 5 hours"]';

        $time_config1 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_TIME_1", "value" => "Morning (6 to 10am)"]]);

        $why_delay_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_1_A", "value" => "No"]]);
        $why_delay_reason_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_DELAY_REASON", "value" => "Other"]]);
        $party_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_2_C", "value" => "Yes"]]);
        $question4h_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_4_H", "value" => "Yes"]]);
        $question7a_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_7_A", "value" => "No"]]);
        $question9c_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_9_C", "value" => "Yes"]]);

        $j=0;
        $connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('OC_QUESTION_1_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config1 . "]', :form_stage_id ),
                ('OC_DELAY_REASON', 'Reasons for Late Opening ', 'select', 'varchar', " . $not_required . ", " . $i++ . ", '" . $delay_reason . "',  '[" . $why_delay_config . "]', :form_stage_id ),
                ('OC_WHY_THE_DELAY', 'Explain why the delay', 'text', 'varchar', " . $not_required . ", " . $i++ . ", '[]',  '[" . $why_delay_reason_config . "]', :form_stage_id ),
                ('OC_QUESTION_1_B', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_2_A', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_2_B', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_2_C', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_1', 'Political Party (a)', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '[" . $political_parties . "]','[" . $party_config . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_REP_1', 'Number of representatives (a)', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $party_config . "]', :form_stage_id ),
                ('OC_POLITICAL_PARTY_2', 'Political Party (b)', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '[" . $political_parties . "]','[" . $party_config . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_REP_2', 'Number of representatives (b)', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $party_config . "]', :form_stage_id ),
                ('OC_QUESTION_3_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_3_B', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_B', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_C', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_D', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_E', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_F', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_G', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_H', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_4_H_1', 'How long did it take to resolve the issue?', 'select', 'varchar', " . $not_required . ", " . $i++ . ", '" . $how_long_resolve . "',  '[" . $question4h_config . "]', :form_stage_id ),
                ('OC_QUESTION_5_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_5_B', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_5_C', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_5_D', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_5_E', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_5_F', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_5_G', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_B', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_C', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_D', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_E', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_F', '" . $morning_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_G', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_H', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_I', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_6_J', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_7_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_7_A_1', 'Explain why line not moving', 'text', 'varchar', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question7a_config . "]', :form_stage_id ),
                ('OC_QUESTION_7_B', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_7_C', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_7_D', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_8_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_8_B', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_8_C', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_8_D', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_9_A', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_9_B', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_9_C', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config1 . "]' , :form_stage_id ),
                ('OC_QUESTION_9_C_1', 'How many complaints were lodged?', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question9c_config . "]', :form_stage_id );
        "
        )->execute([':form_stage_id' => 2]);

        include 'observer_checklist_form_attributes_afternoon_questions.php';

        $time_config2 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_TIME_1", "value" => "Afternoon (10am to 2pm)"]]);

        $question11c_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_11_C", "value" => "Yes"]]);
        $question14a_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_14_A", "value" => "Yes"]]);
        $question17a_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_17_A", "value" => "No"]]);
        $question19c_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_19_C", "value" => "Yes"]]);

        $j=0;
        $connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('OC_QUESTION_10_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_10_B', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_11_A', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_11_B', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_11_C', '" . $morning_questions_array[$j++] . "', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '" . $yes_no . "','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_11_1', 'Political Party (a)', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '[" . $political_parties . "]','[" . $question11c_config . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_REP_11_1', 'Number of representatives (a)', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question11c_config . "]', :form_stage_id ),
                ('OC_POLITICAL_PARTY_11_2', 'Political Party (b)', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '[" . $political_parties . "]','[" . $question11c_config . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_REP_11_2', 'Number of representatives (b)', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question11c_config . "]', :form_stage_id ),
                ('OC_QUESTION_12_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_12_B', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_13_A', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_13_B', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_13_C', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_14_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_14_A_1', 'How long did it take to resolve the issue?', 'select', 'varchar', " . $not_required . ", " . $i++ . ", '" . $how_long_resolve . "',  '[" . $question14a_config . "]', :form_stage_id ),
                ('OC_QUESTION_15_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_15_B', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_15_C', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_15_D', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_15_E', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_15_F', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_15_G', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_16_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_16_B', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_16_C', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_16_D', '" . $afternoon_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config2 . "]' , :form_stage_id ),
                ('OC_QUESTION_16_E', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_16_R', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_16_G', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_16_H', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_17_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_17_A_1', 'Explain why line not moving', 'text', 'varchar', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question17a_config . "]', :form_stage_id ),
                ('OC_QUESTION_17_B', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_17_C', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_17_D', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_18_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_18_B', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_18_C', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_18_D', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_19_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_19_B', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_19_C', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id ),
                ('OC_QUESTION_19_C_1', 'How many complaints were lodged?', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question19c_config . "]', :form_stage_id ),
                ('OC_QUESTION_20_A', '" . $afternoon_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config2 . "]', :form_stage_id );
        "
        )->execute([':form_stage_id' => 2]);


        include 'observer_checklist_form_attributes_evening_questions.php';

        $time_config3 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_TIME_1", "value" => "Evening (2pm to 4pm)"]]);

        $question21a_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_21_A", "value" => "No"]]);
        $question22c_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_22_C", "value" => "Yes"]]);
        $question24e_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_24_E", "value" => "Yes"]]);
        $question27a_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_27_A", "value" => "No"]]);
        $question29c_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_29_C", "value" => "Yes"]]);

        $j=0;
        $connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('OC_QUESTION_21_A', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_21_A_1', 'Explain why', 'text', 'varchar', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question21a_config . "]', :form_stage_id ),
                ('OC_QUESTION_21_B', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_22_A', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_22_B', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_22_C', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_POLITICAL_PARTY_22_1', 'Political Party (a)', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '[" . $political_parties . "]','[" . $question22c_config . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_REP_22_1', 'Number of representatives (a)', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question22c_config . "]', :form_stage_id ),
                ('OC_POLITICAL_PARTY_22_2', 'Political Party (b)', 'select', 'varchar' ," . $not_required . ",  " . $i++ . ", '[" . $political_parties . "]','[" . $question22c_config . "]' , :form_stage_id ),
                ('OC_POLITICAL_PARTY_REP_22_2', 'Number of representatives (b)', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question22c_config . "]', :form_stage_id ),
                ('OC_QUESTION_23_A', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_24_A', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_24_B', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_24_C', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_24_D', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_24_E', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_24_E_1', 'How long did it take to resolve the issue?', 'select', 'varchar', " . $not_required . ", " . $i++ . ", '" . $how_long_resolve . "',  '[" . $question24e_config . "]', :form_stage_id ),
                ('OC_QUESTION_25_A', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_25_B', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_25_C', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_25_D', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_25_E', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_25_F', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_25_G', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_26_A', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_26_B', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_26_C', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_26_D', '" . $evening_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config3 . "]' , :form_stage_id ),
                ('OC_QUESTION_26_E', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_26_F', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_26_G', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_26_H', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_27_A', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_27_A_1', 'Explain why line not moving', 'text', 'varchar', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question27a_config . "]', :form_stage_id ),
                ('OC_QUESTION_27_B', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_27_C', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_27_D', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_28_A', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_28_B', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_28_C', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_29_A', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_29_B', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_29_C', '" . $evening_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config3 . "]', :form_stage_id ),
                ('OC_QUESTION_29_C_1', 'How many complaints were lodged?', 'number', 'int', " . $not_required . ", " . $i++ . ", '[]',  '[" . $question29c_config . "]', :form_stage_id );
        "
        )->execute([':form_stage_id' => 2]);


        include 'observer_checklist_form_attributes_votecount_questions.php';
    
        $time_config4 = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_TIME_1", "value" => "Vote Count (4pm to 12am)"]]);

        $vote_time_start = '["16:00", "17:00",  "18:00", "19:00", "20:00", "21:00"]';

        $question11c_config = json_encode(["id" => 1, "action" => "show", "target" => ["key" => "OC_QUESTION_11_C", "value" => "Yes"]]);

        $j=0;
        $connection->prepare(
            "insert into form_attributes (`key`, label, `input`, `type`, required, priority, options, config, form_stage_id) values
                ('OC_QUESTION_30_A', '" . $votecount_questions_array[$j++] . "', 'select', 'varchar', " . $not_required . ", " . $i++ . ", '" . $vote_time_start . "',  '[" . $time_config4 . "]', :form_stage_id ),
                ('OC_QUESTION_31_A', '" . $votecount_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config4 . "]', :form_stage_id ),
                ('OC_QUESTION_31_B', '" . $votecount_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config4 . "]', :form_stage_id ),
                ('OC_QUESTION_32_A', '" . $votecount_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config4 . "]', :form_stage_id ),
                ('OC_QUESTION_32_B', '" . $votecount_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config4 . "]' , :form_stage_id ),
                ('OC_QUESTION_32_C', '" . $votecount_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config4 . "]' , :form_stage_id ),
                ('OC_QUESTION_32_D', '" . $votecount_questions_array[$j++] . "', 'number', 'int' ," . $not_required . ",  " . $i++ . ", '[]','[" . $time_config4 . "]' , :form_stage_id ),
                ('OC_QUESTION_32_E', '" . $votecount_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config4 . "]', :form_stage_id ),
                ('OC_QUESTION_32_F', '" . $votecount_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config4 . "]', :form_stage_id ),
                ('OC_QUESTION_32_G', '" . $votecount_questions_array[$j++] . "', 'select', 'varchar'," . $not_required . ", " . $i++ . ", '" . $yes_no . "', '[" . $time_config4 . "]', :form_stage_id );

        "
        )->execute([':form_stage_id' => 2]);

    }


    /**
     * Migrate Down.
     */
    public function down()
    {
        // Noop - too risky to delete a post type
    }
}
