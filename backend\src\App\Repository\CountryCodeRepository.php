<?php

/**
 * <PERSON><PERSON><PERSON><PERSON> Config Repository, using Kohana::$config
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Application
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Repository;

use <PERSON>hahidi\Core\Entity\CountryCode;
use <PERSON><PERSON><PERSON>di\Core\Entity\CountryCodeRepository as CountryCodeRepositoryContract;
use Ushahidi\Core\SearchData;
use Ushahidi\Core\Usecase\ReadRepository;
use <PERSON><PERSON><PERSON>di\Core\Usecase\SearchRepository;

class CountryCodeRepository extends OhanzeeRepository implements
    CountryCodeRepositoryContract,
    ReadRepository,
    SearchRepository
{
    // OhanzeeRepository
    protected function getTable()
    {
        return 'country_codes';
    }

    public function getSearchFields()
    {
        return ['country_code', 'dial_code'];
    }

    public function setSearchConditions(SearchData $search)
    {
        $query = $this->search_query;
        return $query;
    }

    public function getEntity(array $data = null)
    {
        return new CountryCode($data);
    }
}
