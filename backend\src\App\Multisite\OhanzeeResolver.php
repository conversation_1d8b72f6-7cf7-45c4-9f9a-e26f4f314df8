<?php

/**
 * Ushahidi Platform DB resolver
 *
 * <AUTHOR> Team <<EMAIL>>
 * @package    Ushahidi\Platform
 * @copyright  2014 Ushahidi
 * @license    https://www.gnu.org/licenses/agpl-3.0.html GNU Affero General Public License Version 3 (AGPL3)
 */

namespace Ushahidi\App\Multisite;

use Ohanzee\Database;
use Ushahidi\App\Models\Tenant;

class OhanzeeResolver
{
    /**
     * @var Ohanzee\Database
     */
    protected $currentConnection;

    public function useDefaultConnection()
    {
        $config = config('ohanzee-db'); // In construct() ??

        $this->currentConnection = Database::instance('default', $config['default']);
    }

    public function setConnection($name, $config)
    {
        $defaults = config('ohanzee-db')['default']; // In construct() ??
        $host = @$_SERVER['HTTP_HOST'];
        //$host = substr($host, 0, strpos($host, ".")); 
        $tenant = Tenant::where('backend_url',$host)->first();
        if(is_null($tenant)){
            $db = 'madagascar'; // ireport
        }else{
            $db = $tenant->db;
        }

        $defaults['connection']['hostname'] = $config['host'];
        $defaults['connection']['database'] = $db; //$config['database'];
        $defaults['connection']['username'] = $config['username'];
        $defaults['connection']['password'] = $config['password'];

        // @todo check if config already exists
        $this->currentConnection = Database::instance($name, $defaults);
    }

    public function connection()
    {
        if (!$this->currentConnection) {
            throw new \RuntimeException('Database not configured yet');
        }

        return $this->currentConnection;
    }
}
