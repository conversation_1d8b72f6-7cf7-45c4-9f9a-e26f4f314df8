<?php

use Phinx\Migration\AbstractMigration;

class ModifyAboutUsOptionsTable extends AbstractMigration
{
    /**
     * Change Method.
     *
     * More information on this method is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-change-method
     */
    public function change()
    {

        // Liberia About Us Section
        // $liberia_about_us_section = array(
        //     'about_us' => [
        //         'image' => 'about_us.png',
        //         'what_we_do' => [
        //             'title' => 'What we do',
        //             'description' => 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry’s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.'
        //         ],
        //         'how_we_do' => [
        //             'title' => 'How we do',
        //             'description' => 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry’s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.'
        //         ],
        //     ],
        // );
        // $liberia_about_us_section = json_encode($liberia_about_us_section);
        // $this->execute("UPDATE tenant_options SET tenant_value = '$liberia_about_us_section' WHERE tenant_id = '2' AND tenant_key = 'about_us'");

        // // Liberia Got Query Section
        // $liberia_qot_query_section = array(
        //     'got_query' => ['title' => 'Got any Queries?', 'description' => 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry’s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the Lorem Ipsum.'],
        // );
        // $liberia_qot_query_section = json_encode($liberia_qot_query_section);
        // $this->execute("UPDATE tenant_options SET tenant_value = '$liberia_qot_query_section' WHERE tenant_id = '2' AND tenant_key = 'got_query'");
    }

    /**
     * Migrate Up.
     */
    public function up()
    {
        // noop, uses change()
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        // noop, uses change()
    }
}
